FROM registry.cn-beijing.aliyuncs.com/titan_nice/base-jdk:agent

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 配置环境变量
ENV MEM_OPT "-XX:+UseG1GC -Xmx2048m -Xms1024m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/heap-dump.hprof -XX:+DisableExplicitGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:/data/logs/gc.log"

# 创建目录
RUN mkdir -p /data/server/bin/
RUN mkdir -p /data/logs/

# 拷贝文件
COPY target/venue-0.0.1-SNAPSHOT.jar /data/server/bin/server.jar

RUN echo 'java $MEM_OPT -Dfile.encoding=UTF-8 \
           -jar -Duser.timezone=Asia/Shanghai \
           /data/server/bin/server.jar' > /data/server/bin/start.sh

# 设置脚本权限
RUN chmod +x /data/server/bin/start.sh
CMD /data/server/bin/start.sh
