package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 场馆订单核销信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "场馆订单核销信息", description = "包含订单核销码、核销状态等信息")
public class VenueVerificationCodeVO {

    @ApiModelProperty(value = "核销ID", example = "1001", required = true)
    private Long id;

    @ApiModelProperty(value = "订单编号", example = "O2023102312345678901234", required = true)
    private String orderId;

    @ApiModelProperty(value = "核销码 - 12位数字", example = "123456789012", required = true)
    private String verificationCode;

    @ApiModelProperty(value = "核销状态：0-未核销，1-已核销", example = "0", required = true)
    private Integer verificationStatus;

    @ApiModelProperty(value = "核销时间", example = "2023-10-25 15:30:00")
    private LocalDateTime verificationTime;

    @ApiModelProperty(value = "创建时间", example = "2023-10-23 12:34:56", required = true)
    private LocalDateTime createTime;
} 