package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 场馆预约订单详情VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "场馆预约订单详情", description = "场地预订类型订单的详细预订信息")
public class VenueBookingOrderVO {

    @ApiModelProperty(value = "详情ID - 预订订单详情ID", example = "1001", required = true)
    private Long id;

    @ApiModelProperty(value = "订单编号 - 关联的主订单编号", example = "O2023102312345678901234", required = true)
    private String orderId;

    @ApiModelProperty(value = "场地ID - 预订场地的唯一标识", example = "501", required = true)
    private Long siteId;

    @ApiModelProperty(value = "场地名称 - 预订场地的名称", example = "羽毛球场A", required = true)
    private String siteName;

    @ApiModelProperty(value = "场地图片 - 预订场地的图片URL", example = "https://example.com/image.jpg")
    private String siteImage;

    @ApiModelProperty(value = "场馆ID - 场地所属场馆的唯一标识", example = "2001", required = true)
    private Long venueId;

    @ApiModelProperty(value = "场馆名称 - 场地所属场馆的名称", example = "阳光体育馆", required = true)
    private String venueName;

    @ApiModelProperty(value = "场地分区ID - 场地所属分区的唯一标识", example = "301")
    private Long sectionId;

    @ApiModelProperty(value = "场地分区名称 - 场地所属分区的名称", example = "一楼东区")
    private String sectionName;

    @ApiModelProperty(value = "预约日期 - 预订的日期", example = "2023-10-25", required = true)
    private LocalDate bookingDate;

    @ApiModelProperty(value = "开始时间 - 预订的开始时间", example = "14:00:00", required = true)
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间 - 预订的结束时间", example = "16:00:00", required = true)
    private LocalTime endTime;

    @ApiModelProperty(value = "预订价格 - 预订的场地价格", example = "120.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "原价 - 场地原价，未优惠前的价格", example = "150.00")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "创建时间 - 预订订单创建时间", example = "2023-10-23 12:34:56")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间 - 预订订单更新时间", example = "2023-10-23 12:34:56")
    private LocalDateTime updateTime;
} 