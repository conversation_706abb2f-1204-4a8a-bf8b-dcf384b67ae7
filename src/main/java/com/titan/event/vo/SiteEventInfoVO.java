package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 场地活动信息VO
 */
@Data
@ApiModel(value = "场地活动信息")
public class SiteEventInfoVO {

    @ApiModelProperty(value = "活动ID")
    private Long eventId;

    @ApiModelProperty(value = "活动名称")
    private String eventName;

    @ApiModelProperty(value = "活动描述")
    private String eventDesc;
    
    @ApiModelProperty(value = "活动类型")
    private Integer eventType;
    
    @ApiModelProperty(value = "活动状态")
    private Integer eventStatus;
    
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "场地ID")
    private Long siteId;
    
    @ApiModelProperty(value = "场地名称")
    private String siteName;
} 