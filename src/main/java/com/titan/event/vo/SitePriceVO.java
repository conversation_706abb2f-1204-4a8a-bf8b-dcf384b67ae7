package com.titan.event.vo;

import com.titan.event.vo.site.TimeslotPriceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 场地价格信息VO
 */
@Data
@ApiModel(value = "场地价格信息")
public class SitePriceVO {

    @ApiModelProperty(value = "场地ID")
    private Long siteId;

    @ApiModelProperty(value = "场地名称")
    private String siteName;

    @ApiModelProperty(value = "分区ID，全场时为空")
    private Long sectionId;

    @ApiModelProperty(value = "分区名称，全场时为全场")
    private String sectionName;

    @ApiModelProperty(value = "分区编号，仅当为分区时有值")
    private String sectionCode;
    
    @ApiModelProperty(value = "日期，格式YYYY-MM-DD")
    private String date;
    
    @ApiModelProperty(value = "类型：site(全场) 或 section(分区)")
    private String type;
    
    @ApiModelProperty(value = "价格时段列表")
    private List<TimeslotPriceVO> prices = new ArrayList<>();
} 