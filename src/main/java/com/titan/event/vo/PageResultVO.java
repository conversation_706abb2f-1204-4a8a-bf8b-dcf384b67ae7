package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用分页结果VO
 * @param <T> 结果项类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "分页结果", description = "通用分页查询结果")
public class PageResultVO<T> {

    @ApiModelProperty(value = "当前页码", example = "1", required = true)
    private Integer pageNum;

    @ApiModelProperty(value = "每页条数", example = "10", required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "总条数", example = "100", required = true)
    private Long total;

    @ApiModelProperty(value = "总页数", example = "10", required = true)
    private Integer pages;

    @ApiModelProperty(value = "结果列表", required = true)
    private List<T> list;

    @ApiModelProperty(value = "是否有下一页", example = "true", required = true)
    private Boolean hasNextPage;

    /**
     * 构建分页结果
     * 
     * @param pageNum 当前页码
     * @param pageSize 每页条数
     * @param total 总条数
     * @param list 结果列表
     * @param <T> 结果项类型
     * @return 分页结果
     */
    public static <T> PageResultVO<T> build(Integer pageNum, Integer pageSize, Long total, List<T> list) {
        int pages = (int) Math.ceil((double) total / pageSize);
        boolean hasNextPage = pageNum < pages;
        
        return PageResultVO.<T>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .total(total)
                .pages(pages)
                .list(list)
                .hasNextPage(hasNextPage)
                .build();
    }
} 