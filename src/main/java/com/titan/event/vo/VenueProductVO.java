package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 场馆商品VO对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场馆商品VO", description = "场馆商品详情展示对象")
public class VenueProductVO {

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "所属场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "上下架状态", notes = "true:上架 false:下架")
    private Boolean status;

    @ApiModelProperty(value = "商品图片JSON字符串", notes = "多图JSON格式", hidden = true)
    private String productImages;
    
    @ApiModelProperty(value = "商品图片列表")
    private List<String> imageList;
    
    @ApiModelProperty(value = "商品封面图片")
    private String coverImage;

    @ApiModelProperty(value = "总销量", notes = "注水销量+实际销量")
    private Integer totalSales;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "划线价格")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "商品详细介绍", notes = "富文本")
    private String productDetail;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "限购数量")
    private Integer purchaseLimit;

    @ApiModelProperty(value = "购买须知详情", notes = "富文本")
    private String purchaseNotes;

    @ApiModelProperty(value = "有效期(天)")
    private Integer validityDays;

    @ApiModelProperty(value = "排序序号")
    private Integer orderNum;
    
    @ApiModelProperty(value = "场馆联系电话")
    private String contactPhone;
    
    @ApiModelProperty(value = "场馆微信客服ID")
    private String wxCustomerServiceId;
} 