package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@ApiModel(value = "订阅信息响应体",description = "订阅信息响应体")
public class UserPaySubscribeVO {

    @ApiModelProperty(value = "订阅ID")
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "交易ID")
    private String transactionId;

    @ApiModelProperty(value = "原始交易ID")
    private String originalTransactionId;

    @ApiModelProperty(value = "原始购买日期")
    private String originalPurchaseDate;

    @ApiModelProperty(value = "原始购买日期（毫秒）")
    private String originalPurchaseDateMs;

    @ApiModelProperty(value = "购买日期")
    private String purchaseDate;

    @ApiModelProperty(value = "购买日期（毫秒）")
    private String purchaseDateMs;

    @ApiModelProperty(value = "到期日期")
    private String expiresDate;

    @ApiModelProperty(value = "到期日期（毫秒）")
    private String expiresDateMs;

    @ApiModelProperty(value = "数量")
    private String quantity;

    @ApiModelProperty(value = "是否为试用期")
    private String isTrialPeriod;

    @ApiModelProperty(value = "应用内拥有类型")
    private String inAppOwnershipType;

    @ApiModelProperty(value = "是否在介绍优惠期内")
    private String isInIntroOfferPeriod;

    @ApiModelProperty(value = "订阅组标识符")
    private String subscriptionGroupIdentifier;

    @ApiModelProperty(value = "网络订单行项目ID")
    private String webOrderLineItemId;

    @ApiModelProperty(value = "状态：<br>1：有效<br>2：过期<br>3：账号扣费重试<br>4：账号宽限期(这个是开发者设置，比如到期扣费失败时，可以给用户延期多长时间。)<br>5：已退款")
    private Integer status;

    @ApiModelProperty(value = "取消日期")
    private LocalDateTime cancelDate;

    @ApiModelProperty(value = "创建日期，默认为当前时间戳")
    private LocalDateTime createDate;

    public UserPaySubscribeVO() {

    }

}
