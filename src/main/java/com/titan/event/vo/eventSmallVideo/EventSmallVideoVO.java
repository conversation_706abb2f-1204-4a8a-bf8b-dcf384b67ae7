package com.titan.event.vo.eventSmallVideo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.util.LocalDateTimeDeserializer;
import com.titan.event.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 赛事短视频信息VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "赛事短视频信息",description = "赛事短视频信息")
public class EventSmallVideoVO {

    @ApiModelProperty(name = "视频ID", value = "视频ID")
    private Long id;

    @ApiModelProperty(name = "视频标题", value = "视频标题")
    private String videoTitle;

    @ApiModelProperty(name = "视频Token", value = "视频Token")
    private String videoFreedToken;

    @ApiModelProperty(name = "发布时间", value = "发布时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime publishTime;
} 