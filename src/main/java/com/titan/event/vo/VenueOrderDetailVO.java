package com.titan.event.vo;

import com.titan.event.entity.VenueBookingOrder;
import com.titan.event.entity.VenueProductOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 场馆订单详情VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "场馆订单详情", description = "包含订单基本信息、支付信息、退款信息及预订/商品明细的完整订单数据")
public class VenueOrderDetailVO {

    @ApiModelProperty(value = "订单ID - 订单在数据库中的主键ID", example = "1001", required = true)
    private Long id;

    @ApiModelProperty(value = "订单编号 - 系统生成的唯一订单编号，用于后续查询和跟踪", example = "O2023102312345678901234", required = true)
    private String orderId;

    @ApiModelProperty(value = "用户ID - 下单用户的唯一标识", example = "10086", required = true)
    private Long userId;

    @ApiModelProperty(value = "场馆ID - 场馆的唯一标识，非场馆商品订单可能为空", example = "2001")
    private Long venueId;

    @ApiModelProperty(value = "场馆名称 - 场馆名称，非场馆订单可能为空", example = "阳光体育馆")
    private String venueName;
    
    @ApiModelProperty(value = "场馆地址 - 场馆详细地址", example = "北京市朝阳区体育场路1号")
    private String venueAddress;
    
    @ApiModelProperty(value = "场馆微信客服ID - 场馆微信客服ID，用于客户联系场馆客服", example = "wx_123456789")
    private String venueWxCustomerServiceId;
    
    @ApiModelProperty(value = "场馆联系电话 - 场馆联系电话", example = "010-88889999")
    private String venueContactPhone;

    @ApiModelProperty(value = "订单类型 - 1-场馆预约，2-场馆产品，3-卡片购买", example = "1", required = true)
    private Integer orderType;

    @ApiModelProperty(value = "联系人姓名 - 订单联系人姓名", example = "张三")
    private String contactName;

    @ApiModelProperty(value = "联系人电话 - 订单联系人电话", example = "13800138000")
    private String contactPhone;

    @ApiModelProperty(value = "订单总金额 - 订单总金额，未计算优惠前的价格", example = "200.00", required = true)
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "优惠金额 - 订单优惠金额", example = "20.00")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "实付金额 - 用户实际支付的金额，等于总金额减去优惠金额", example = "180.00", required = true)
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "订单状态 - 1-待支付, 2-待核销, 3-已完成, 4-已作废", example = "1", required = true)
    private Integer orderStatus;

    @ApiModelProperty(value = "使用须知 - 订单相关的使用须知说明", example = "请至少提前30分钟到达场馆，入场需出示核销码")
    private String usageNotice;

    @ApiModelProperty(value = "备注 - 订单备注信息", example = "请准时到场")
    private String remark;

    @ApiModelProperty(value = "创建时间 - 订单创建时间", example = "2023-10-23 12:34:56", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间 - 订单最后更新时间", example = "2023-10-23 13:45:23")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "支付截止时间 - 订单支付的截止时间，超过此时间订单将自动取消", example = "2023-10-23 14:36:56")
    private LocalDateTime paymentDeadline;

    @ApiModelProperty(value = "微信支付金额 - 使用微信支付需要支付的金额", example = "180.00")
    private BigDecimal wechatPayAmount;

    @ApiModelProperty(value = "储值卡支付金额 - 使用储值卡支付需要支付的金额", example = "150.00")
    private BigDecimal cardPayAmount;

    @ApiModelProperty(value = "用户储值卡信息 - 用户在该场馆的储值卡信息，如果没有则为null")
    private UserStorageCardInfo userStorageCard;

    @ApiModelProperty(value = "支付信息 - 订单的支付相关信息，包含支付状态、支付时间等")
    private PaymentInfoVO paymentInfo;

    @ApiModelProperty(value = "退款信息 - 订单的退款相关信息，包含退款状态、退款时间等")
    private RefundInfoVO refundInfo;

    @ApiModelProperty(value = "预订订单详情列表 - 场地预订类型订单的详细预订信息列表，订单类型为1时有值")
    private List<VenueBookingOrderVO> bookingDetails;

    @ApiModelProperty(value = "商品订单详情列表 - 商品购买类型订单的详细商品信息列表，订单类型为2时有值")
    private List<VenueProductOrderVO> productDetails;
    
    @ApiModelProperty(value = "核销信息 - 订单的核销相关信息，包含核销码、核销状态等")
    private List<VenueVerificationCodeVO> verificationCodes;

    /**
     * 用户储值卡信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "用户储值卡信息", description = "用户在该场馆的储值卡详细信息")
    public static class UserStorageCardInfo {

        @ApiModelProperty(value = "用户会员卡ID", example = "1")
        private Long userCardId;

        @ApiModelProperty(value = "会员卡ID", example = "1")
        private Long cardId;

        @ApiModelProperty(value = "会员卡名称", example = "储值卡")
        private String cardName;

        @ApiModelProperty(value = "会员卡号", example = "VIP001")
        private String cardNumber;

        @ApiModelProperty(value = "余额", example = "100.50")
        private BigDecimal balance;

        @ApiModelProperty(value = "是否可用", example = "true", notes = "余额充足且在有效期内")
        private Boolean available;
    }
}