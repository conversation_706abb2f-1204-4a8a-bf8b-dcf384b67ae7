package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地信息VO
 */
@Data
@ApiModel(value = "场地信息")
public class SiteInfoVO {

    @ApiModelProperty(value = "场地ID")
    private Long siteId;

    @ApiModelProperty(value = "场地名称")
    private String siteName;

    @ApiModelProperty(value = "所属场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "场地类型")
    private Integer siteType;

    @ApiModelProperty(value = "场地状态")
    private Integer siteStatus;

    @ApiModelProperty(value = "是否允许分割")
    private Boolean isSplitAllowed;

    @ApiModelProperty(value = "最大容纳人数")
    private Integer maxCapacity;

    @ApiModelProperty(value = "是否启用节假日价格")
    private String holidayEnabled;

    @ApiModelProperty(value = "是否启用特殊日期价格")
    private String specialDateEnabled;
} 