package com.titan.event.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 支付信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "支付信息", description = "订单支付相关的详细信息，包含支付状态、支付时间、交易流水等")
public class PaymentInfoVO {

    @ApiModelProperty(value = "支付状态 - SUCCESS-支付成功，REFUND-转入退款，NOTPAY-未支付，CLOSED-已关闭，REVOKED-已撤销，USERPAYING-用户支付中，PAYERROR-支付失败，NONEEDPAY-无需支付", example = "SUCCESS", required = true)
    private String payStatus;

    @ApiModelProperty(value = "支付时间 - 用户完成支付的时间", example = "2023-10-23 14:30:45")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "交易流水号 - 微信支付的交易流水号，支付成功后才有值", example = "4200001234202310231234567890")
    private String transactionId;
    
    @ApiModelProperty(value = "预支付ID - 微信支付生成的预支付ID，用于发起小程序支付", example = "wx123456789012345678901234567890", required = true)
    private String prepayId;
    
    @ApiModelProperty(value = "支付类型 - 支付方式：WECHAT-微信支付", example = "WECHAT", required = true)
    private String payType;
    
    @ApiModelProperty(value = "支付金额 - 支付金额，单位为元", example = "180.00", required = true)
    private Double amount;
    
    @ApiModelProperty(value = "支付描述 - 支付订单的商品描述", example = "阳光体育馆-羽毛球场地预订")
    private String body;
    
    @ApiModelProperty(value = "过期时间 - 支付过期时间", example = "2023-10-23 15:30:45")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime timeExpire;
} 