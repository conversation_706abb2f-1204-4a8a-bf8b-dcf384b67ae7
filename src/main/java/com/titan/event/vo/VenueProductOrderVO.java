package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 场馆商品订单详情VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "场馆商品订单详情", description = "商品购买类型订单的详细商品信息")
public class VenueProductOrderVO {

    @ApiModelProperty(value = "详情ID - 商品订单详情ID", example = "2001", required = true)
    private Long id;

    @ApiModelProperty(value = "订单编号 - 关联的主订单编号", example = "O2023102312345678901234", required = true)
    private String orderId;

    @ApiModelProperty(value = "商品ID - 购买商品的唯一标识", example = "101", required = true)
    private Long productId;

    @ApiModelProperty(value = "商品名称 - 购买商品的名称", example = "运动饮料", required = true)
    private String productName;

    @ApiModelProperty(value = "商品图片 - 购买商品的图片URL", example = "https://example.com/product.jpg")
    private String productImage;

    @ApiModelProperty(value = "场馆ID - 商品所属场馆的唯一标识", example = "2001")
    private Long venueId;

    @ApiModelProperty(value = "场馆名称 - 商品所属场馆的名称", example = "阳光体育馆")
    private String venueName;

    @ApiModelProperty(value = "单价 - 商品购买时的单价", example = "15.00", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "原价 - 商品原价，未优惠前的价格", example = "20.00")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "购买数量 - 商品购买的数量", example = "2", required = true)
    private Integer quantity;

    @ApiModelProperty(value = "小计金额 - 商品购买的小计金额（单价×数量）", example = "30.00", required = true)
    private BigDecimal subtotalAmount;

    @ApiModelProperty(value = "创建时间 - 商品订单创建时间", example = "2023-10-23 12:34:56")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间 - 商品订单更新时间", example = "2023-10-23 12:34:56")
    private LocalDateTime updateTime;
} 