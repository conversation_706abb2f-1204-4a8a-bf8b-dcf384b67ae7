package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件上传信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel(value = "上传文件信息", description = "包含上传文件的相关信息，如文件URL和名称")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadVO implements Serializable{

    /**
     * 文件id
     */
//    @ApiModelProperty(value = "文件id")
//    private Long id;

    /**
     * 文件url
     */
    @ApiModelProperty(value = "文件URL", notes = "上传后的文件访问URL地址", required = true, example = "https://example.com/uploads/file123.jpg")
    private String url;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", notes = "上传文件的原始名称", required = true, example = "profile_image.jpg")
    private String fileName;

}
