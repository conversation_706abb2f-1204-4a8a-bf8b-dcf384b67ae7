package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 每日步数列表VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "DailyStepsVO", description = "每日步数列表VO")
public class DailyStepsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总页数")
    private Integer totalPages;

    @ApiModelProperty(value = "总记录数")
    private Long totalElements;

    @ApiModelProperty(value = "当前页")
    private Integer currentPage;

    @ApiModelProperty(value = "每日步数数据")
    private List<Map<String, Object>> content;
} 