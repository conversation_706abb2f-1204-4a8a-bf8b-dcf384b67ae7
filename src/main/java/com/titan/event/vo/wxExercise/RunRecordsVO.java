package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 运动记录列表VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RunRecordsVO", description = "运动记录列表VO")
public class RunRecordsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总页数")
    private Integer totalPages;

    @ApiModelProperty(value = "总记录数")
    private Integer totalElements;

    @ApiModelProperty(value = "当前页")
    private Integer currentPage;

    @ApiModelProperty(value = "总跑量(公里)")
    private String totalDistance;

    @ApiModelProperty(value = "月度运动记录")
    private List<Map<String, Object>> content;
} 