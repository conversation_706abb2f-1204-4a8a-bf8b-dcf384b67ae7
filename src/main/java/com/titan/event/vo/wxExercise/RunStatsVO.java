package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 运动统计VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RunStatsVO", description = "运动统计VO")
public class RunStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总跑量(公里)")
    private String totalDistance;

    @ApiModelProperty(value = "运动次数")
    private Integer runCount;

    @ApiModelProperty(value = "总时长(格式:HH:MM)")
    private String totalDuration;

    @ApiModelProperty(value = "每日跑量柱状图数据")
    private List<Map<String, Object>> dailyDistanceChart;

    @ApiModelProperty(value = "配速图表数据")
    private Map<String, Object> paceChart;

    @ApiModelProperty(value = "每日步数柱状图数据")
    private List<Map<String, Object>> dailyStepsChart;
} 