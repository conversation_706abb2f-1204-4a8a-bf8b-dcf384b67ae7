package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户运动总量VO
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel(value = "用户运动总量VO")
public class TotalExerciseVO {

    @ApiModelProperty(value = "累计跑量（公里）")
    private String totalDistance;

    @ApiModelProperty(value = "运动次数")
    private Integer runCount;

    @ApiModelProperty(value = "运动时长（时分）")
    private String totalDuration;
} 