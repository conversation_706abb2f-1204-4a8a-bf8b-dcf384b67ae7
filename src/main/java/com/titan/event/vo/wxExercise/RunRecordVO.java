package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 运动记录VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RunRecordVO", description = "运动记录VO")
public class RunRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    private Long id;
} 