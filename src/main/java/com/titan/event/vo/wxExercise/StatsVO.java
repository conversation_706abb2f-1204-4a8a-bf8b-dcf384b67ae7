package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 步数统计VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "StatsVO", description = "步数统计VO")
public class StatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总步数")
    private Integer totalSteps;

    @ApiModelProperty(value = "平均步数")
    private Integer averageSteps;

    @ApiModelProperty(value = "最大步数")
    private Integer maxSteps;

    @ApiModelProperty(value = "柱状图数据")
    private List<Map<String, Object>> chartData;
} 