package com.titan.event.vo.wxExercise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 运动详情VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RunRecordDetailVO", description = "运动详情VO")
public class RunRecordDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户信息")
    private Map<String, Object> userInfo;

    @ApiModelProperty(value = "运动数据")
    private Map<String, Object> runData;
    
    @ApiModelProperty(value = "分享数据")
    private Map<String, Object> shareData;
} 