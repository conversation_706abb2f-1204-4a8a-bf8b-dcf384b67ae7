package com.titan.event.vo.proFile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.enums.*;
import com.titan.event.util.LocalDateDeserializer;
import com.titan.event.util.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Enumerated;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "报名档案信息",description = "报名档案信息")
public class ProfileInfoVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @Enumerated
    @ApiModelProperty(value = "证件类型 0二代身份证 1护照 2港澳居民往来内地通行证 3台湾居民往来大陆通行证")
    private IdCardType idCardType;

    @ApiModelProperty(value = "证件号码")
    private String idCard;

    @ApiModelProperty(value = "年龄")
    private Long age;

    @Enumerated
    @ApiModelProperty(value = "血型0:A 1:B 2:AB 3:O 4:Rh")
    private BloodType bloodType;

    @ApiModelProperty(name = "出生日期 格式：yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate birthday;

    @Enumerated
    @ApiModelProperty(value = "性别，0男 1女")
    private GenderType gender;

    @Enumerated
    @ApiModelProperty(value = "衣服尺码 0:S 1:M 2:L 3:XL 4:XXL 5:XXXL 6:XXXXL")
    private ClothingSizeType clothingSizeType;

    @ApiModelProperty(value = "紧急联系人")
    private String emergencyContactName;

    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyContactPhone;

    @ApiModelProperty(value = "国家地区id")
    private Long countryId;

    @ApiModelProperty(value = "国家地区")
    private String countryName;

    @Enumerated
    @ApiModelProperty(value = "是否为本人 0否 1是")
    private SelfStatus selfStatus;

    @ApiModelProperty(value = "身份验证状态 0未验证 1已验证通过 2验证不通过")
    private Integer verificationStatus;

}
