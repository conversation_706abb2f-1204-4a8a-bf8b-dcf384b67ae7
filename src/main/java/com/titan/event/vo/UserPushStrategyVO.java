package com.titan.event.vo;

import com.titan.event.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "用户推送设置响应体",description = "用户推送设置响应体")
public class UserPushStrategyVO {

    @ApiModelProperty(name = "用户ID",value = "用户ID")
    private Long userId;

    /**
     * 推送震级阈值
     */
    @ApiModelProperty(name = "推送震级阈值",value = "推送震级阈值")
    private Double pushZj;

    /**
     * 关注地区推送震级阈值
     */
    @ApiModelProperty(name = "关注地区推送震级阈值",value = "关注地区推送震级阈值")
    private Double followProvinceZj;

    /**
     * 关注地址推送震级阈值
     */
    @ApiModelProperty(name = "关注地址推送震级阈值",value = "关注地址推送震级阈值")
    private Double followAddressZj;

    /**
     * 关注地址与震中的距离阈值
     */
    @ApiModelProperty(name = "关注地址与震中的距离阈值",value = "关注地址与震中的距离阈值")
    private Integer followAddressDistance;

    /**
     * 关注人推送震级阈值
     */
    @ApiModelProperty(name = "关注人推送震级阈值",value = "关注人推送震级阈值")
    private Double followPersonZj;

    /**
     * 关注人与震中的距离阈值
     */
    @ApiModelProperty(name = "关注人与震中的距离阈值",value = "关注人与震中的距离阈值")
    private Integer followPersonDistance;

    /**
     * 是否开启 0:否 1:是
     */
    @ApiModelProperty(name = "是否开启 0:否 1:是",value = "是否开启 0:否 1:是")
    private DefaultFlag isOpen;
}
