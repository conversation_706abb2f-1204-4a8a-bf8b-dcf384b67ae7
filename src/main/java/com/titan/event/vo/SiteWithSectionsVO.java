package com.titan.event.vo;

import java.util.ArrayList;
import java.util.List;

import com.titan.event.vo.site.SitePriceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地及其分区价格数据VO
 */
@Data
@ApiModel(value = "场地及分区价格数据")
public class SiteWithSectionsVO {

    @ApiModelProperty(value = "查询日期，格式YYYY-MM-DD")
    private String date;

    @ApiModelProperty(value = "场地基本信息")
    private SiteInfoVO site;

    @ApiModelProperty(value = "分区列表")
    private List<SiteSectionInfoVO> sections = new ArrayList<>();

    @ApiModelProperty(value = "价格数据")
    private List<SitePriceVO> priceData = new ArrayList<>();
} 