package com.titan.event.vo;

import com.titan.event.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "用户信息响应体", description = "包含用户的详细个人信息")
public class UserInfoVO {

    @ApiModelProperty(value = "用户ID（系统内用户的唯一数字标识）", required = true, example = "12345")
    private Long userId;

    @ApiModelProperty(value = "UUID（用户的全局唯一标识符）", required = true, example = "550e8400-e29b-41d4-a716-************")
    private String uuid;

    @ApiModelProperty(value = "微信OpenID（用户在当前微信小程序下的唯一标识）", required = false, example = "oQrwT0XAiYMcqsFvIpedIXuLIaLI")
    private String openId;

    @ApiModelProperty(value = "微信UnionID（用户在微信开放平台下的唯一标识，用于跨应用共享用户信息）", required = false, example = "o6_bmasdasdsad6_2sgVt7hMZOPfL")
    private String unionId;

    @ApiModelProperty(value = "用户昵称（用户设置的昵称或显示名称）", required = false, example = "张三")
    private String nickname;

    @ApiModelProperty(value = "用户手机号（用户绑定的手机号码）", required = false, example = "13812345678")
    private String phone;

    @ApiModelProperty(value = "用户头像URL（用户头像的完整URL地址）", required = false, example = "https://example.com/avatar/123.jpg")
    private String avatar;

    @ApiModelProperty(value = "注册天数（用户从注册至今的天数）", required = false, example = "30")
    private Long regDay;

    @ApiModelProperty(value = "游客状态（是否为游客用户：0-非游客，1-游客）", required = true, example = "0")
    private DefaultFlag touristState;

    @ApiModelProperty(value = "管理员状态（是否为管理员：0-否，1-是）", required = true, example = "0")
    private DefaultFlag adminState;

    // 卡数量统计字段
    @ApiModelProperty(value = "权益卡数量（用户持有的权益卡总数量）", required = false, example = "2")
    private Integer benefitCardCount;

    @ApiModelProperty(value = "储值卡数量（用户持有的储值卡总数量）", required = false, example = "1")
    private Integer storageCardCount;

    @ApiModelProperty(value = "计次卡数量（用户持有的计次卡总数量）", required = false, example = "3")
    private Integer countCardCount;

    @ApiModelProperty(value = "会员卡数量（用户持有的会员卡总数量）", required = false, example = "1")
    private Integer memberCardCount;

    @ApiModelProperty(value = "可用权益卡数量（用户持有的可用权益卡数量）", required = false, example = "1")
    private Integer availableBenefitCardCount;

    @ApiModelProperty(value = "可用储值卡数量（用户持有的可用储值卡数量）", required = false, example = "1")
    private Integer availableStorageCardCount;

    @ApiModelProperty(value = "可用计次卡数量（用户持有的可用计次卡数量）", required = false, example = "2")
    private Integer availableCountCardCount;

    @ApiModelProperty(value = "可用会员卡数量（用户持有的可用会员卡数量）", required = false, example = "1")
    private Integer availableMemberCardCount;

}
