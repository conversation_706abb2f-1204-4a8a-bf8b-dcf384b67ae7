package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 场馆设置响应VO
 */
@Data
@ApiModel(value = "VenueSettingVO", description = "场馆设置响应VO")
public class VenueSettingVO {
    
    /**
     * 省份列表
     */
    @ApiModelProperty(value = "省份列表 - 系统支持的省份列表")
    private List<AreaVO> provinces;
    
    @Data
    @ApiModel(value = "VenueSettingVO.AreaVO", description = "区域信息")
    public static class AreaVO {
        /**
         * 区域ID
         */
        @ApiModelProperty(value = "区域ID - 区域的唯一标识", example = "1")
        private Long id;
        
        /**
         * 区域编码
         */
        @ApiModelProperty(value = "区域编码 - 区域的行政区划代码", example = "310000")
        private String code;
        
        /**
         * 区域名称
         */
        @ApiModelProperty(value = "区域名称 - 区域的名称", example = "上海市")
        private String name;
    }
} 