package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地类型信息VO
 */
@Data
@ApiModel(value = "SiteTypeVO", description = "场地类型信息VO")
public class SiteTypeVO {
    
    /**
     * 字典标签
     */
    @ApiModelProperty(value = "字典标签 - 字典的标签名称", example = "羽毛球")
    private String dictLabel;

    @ApiModelProperty(value = "字典值")
    private String dictValue;

} 