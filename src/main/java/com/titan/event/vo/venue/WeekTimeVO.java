package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 一周时间信息VO
 */
@Data
@ApiModel(value = "WeekTimeVO", description = "一周时间信息VO")
public class WeekTimeVO {
    
    /**
     * 日期名称
     */
    @ApiModelProperty(value = "日期名称 - 日期名称，如今日、周一等", example = "今日")
    private String dayName;
    
    /**
     * 日期值
     */
    @ApiModelProperty(value = "日期值 - 日期的值，格式为yyyy-MM-dd", example = "2023-05-01")
    private String date;
} 