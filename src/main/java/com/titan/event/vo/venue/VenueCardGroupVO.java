package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 场馆卡分组信息VO
 */
@Data
@ApiModel(value = "VenueCardGroupVO", description = "场馆卡分组信息VO")
public class VenueCardGroupVO {
    
    /**
     * 卡类型（1储值卡 2计次卡）
     */
    @ApiModelProperty(value = "卡类型", example = "1", notes = "1储值卡 2计次卡")
    private Integer cardType;
    
    /**
     * 卡类型名称
     */
    @ApiModelProperty(value = "卡类型名称", example = "储值卡")
    private String cardTypeName;
    
    /**
     * 该类型下的卡列表
     */
    @ApiModelProperty(value = "该类型下的卡列表")
    private List<VenueCardVO> cards;
}
