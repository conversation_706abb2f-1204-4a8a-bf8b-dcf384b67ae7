package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 场馆列表响应VO
 */
@Data
@ApiModel(value = "VenueListVO", description = "场馆列表响应VO")
public class VenueListVO {
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID - 场馆的唯一标识", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称 - 场馆的名称", example = "市体育中心")
    private String venueName;
    
    /**
     * 场馆地址
     */
    @ApiModelProperty(value = "场馆地址 - 场馆的详细地址", example = "上海市徐汇区天钥桥路100号")
    private String venueAddress;
    
    /**
     * 场馆图片
     */
    @ApiModelProperty(value = "场馆图片 - 场馆的图片URL", example = "http://example.com/images/venue.jpg")
    private String venueImageUrl;
    
    /**
     * 营业状态(0:休息中, 1:营业中)
     */
    @ApiModelProperty(value = "营业状态 - 0:休息中, 1:营业中", example = "1")
    private Integer businessStatus;
    
    /**
     * 省份
     */
    @ApiModelProperty(value = "省份 - 场馆所在省份", example = "上海市")
    private String province;
    
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市 - 场馆所在城市", example = "上海市")
    private String city;
    
    /**
     * 区县
     */
    @ApiModelProperty(value = "区县 - 场馆所在区县", example = "徐汇区")
    private String district;
    
    /**
     * 标签列表
     */
    @ApiModelProperty(value = "标签列表 - 场馆的标签信息")
    private List<TagVO> tags;
    
    @Data
    @ApiModel(value = "VenueListVO.TagVO", description = "场馆标签信息")
    public static class TagVO {
        /**
         * 标签ID
         */
        @ApiModelProperty(value = "标签ID - 标签的唯一标识", example = "1")
        private Long tagId;
        
        /**
         * 标签名称
         */
        @ApiModelProperty(value = "标签名称 - 标签的名称", example = "健身")
        private String tagName;
    }
} 