package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 场馆详情响应VO
 */
@Data
@ApiModel(value = "VenueDetailVO", description = "场馆详情响应VO")
public class VenueDetailVO {
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID - 场馆的唯一标识", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称 - 场馆的名称", example = "市体育中心")
    private String venueName;
    
    /**
     * 场馆地址
     */
    @ApiModelProperty(value = "场馆地址 - 场馆的详细地址", example = "上海市徐汇区天钥桥路100号")
    private String venueAddress;
    
    /**
     * 场馆图片
     */
    @ApiModelProperty(value = "场馆图片 - 场馆的图片URL", example = "http://example.com/images/venue.jpg")
    private String venueImageUrl;
    
    /**
     * 场馆面积(平方米)
     */
    @ApiModelProperty(value = "场馆面积 - 场馆面积，单位：平方米", example = "5000.5")
    private Double venueArea;
    
    /**
     * 场馆容量(人数)
     */
    @ApiModelProperty(value = "场馆容量 - 场馆最大容纳人数", example = "2000")
    private Integer venueCapacity;
    
    /**
     * 场馆类型(1:体育馆, 2:游泳馆, 3:田径场, 4:综合场馆)
     */
    @ApiModelProperty(value = "场馆类型 - 1:体育馆, 2:游泳馆, 3:田径场, 4:综合场馆", example = "1")
    private Integer venueType;
    
    /**
     * 场馆状态(0:闲置中, 1:使用中, 2:维修中)
     */
    @ApiModelProperty(value = "场馆状态 - 0:闲置中, 1:使用中, 2:维修中", example = "1")
    private Integer venueStatus;
    
    /**
     * 营业状态(0:休息中, 1:营业中)
     */
    @ApiModelProperty(value = "营业状态 - 0:休息中, 1:营业中", example = "1")
    private Integer businessStatus;
    
    /**
     * 营业启始时间
     */
    @ApiModelProperty(value = "营业开始时间 - 场馆每日营业开始时间", example = "09:00")
    private String businessStartTime;
    
    /**
     * 营业结束时间
     */
    @ApiModelProperty(value = "营业结束时间 - 场馆每日营业结束时间", example = "21:00")
    private String businessEndTime;
    
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人 - 场馆联系人姓名", example = "张三")
    private String contactPerson;
    
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话 - 场馆联系电话", example = "***********")
    private String contactPhone;
    
    /**
     * 营业时间描述
     */
    @ApiModelProperty(value = "营业时间描述 - 场馆营业时间的文字描述", example = "周一至周日 9:00-21:00")
    private String businessTimeDesc;
    
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度 - 场馆位置的经度坐标", example = "121.47")
    private Double longitude;
    
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度 - 场馆位置的纬度坐标", example = "31.23")
    private Double latitude;
    
    /**
     * 场馆详细介绍(富文本)
     */
    @ApiModelProperty(value = "场馆详细介绍 - 场馆的详细介绍，富文本格式")
    private String venueDescription;
    
    /**
     * 省份
     */
    @ApiModelProperty(value = "省份 - 场馆所在省份", example = "上海市")
    private String province;
    
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市 - 场馆所在城市", example = "上海市")
    private String city;
    
    /**
     * 区县
     */
    @ApiModelProperty(value = "区县 - 场馆所在区县", example = "徐汇区")
    private String district;
    
    /**
     * 微信客服ID
     */
    @ApiModelProperty(value = "微信客服ID - 场馆微信客服的ID", example = "wx123456")
    private String wxCustomerServiceId;
    
    /**
     * 标签列表
     */
    @ApiModelProperty(value = "标签列表 - 场馆的标签信息")
    private List<TagVO> tags;
    
    /**
     * 商品列表
     */
    @ApiModelProperty(value = "商品列表 - 场馆提供的商品信息")
    private List<ProductVO> products;
    
    /**
     * 场地类型列表
     */
    @ApiModelProperty(value = "场地类型列表 - 场馆提供的场地类型信息")
    private List<SiteTypeVO> siteTypes;
    
    /**
     * 一周时间列表
     */
    @ApiModelProperty(value = "一周时间列表 - 场馆一周的时间信息")
    private List<WeekTimeVO> weekTimes;

    /**
     * 场馆卡列表（按卡类型分组）
     */
    @ApiModelProperty(value = "场馆卡列表 - 按卡类型分组的场馆卡信息")
    private List<VenueCardGroupVO> cardGroups;

    /**
     * 储值卡列表
     */
    @ApiModelProperty(value = "储值卡列表 - 场馆的储值卡信息")
    private List<VenueCardVO> storageCards;

    /**
     * 计次卡列表
     */
    @ApiModelProperty(value = "计次卡列表 - 场馆的计次卡信息")
    private List<VenueCardVO> timesCards;

    /**
     * 用户储值卡信息
     */
    @ApiModelProperty(value = "用户储值卡信息 - 当前用户在该场馆的储值卡信息")
    private UserStorageCardVO userStorageCard;
    
    @Data
    @ApiModel(value = "VenueDetailVO.TagVO", description = "场馆标签信息")
    public static class TagVO {
        /**
         * 标签ID
         */
        @ApiModelProperty(value = "标签ID - 标签的唯一标识", example = "1")
        private Long tagId;
        
        /**
         * 标签名称
         */
        @ApiModelProperty(value = "标签名称 - 标签的名称", example = "健身")
        private String tagName;
        
        /**
         * 标签类型
         */
        @ApiModelProperty(value = "标签类型 - 1:运动项目, 2:设施, 3:服务, 4:其他", example = "1")
        private Integer tagType;
        
        /**
         * 标签图标
         */
        @ApiModelProperty(value = "标签图标 - 标签的图标URL", example = "icon_fitness.png")
        private String tagIcon;
    }
    
    @Data
    @ApiModel(value = "VenueDetailVO.ProductVO", description = "场馆商品信息")
    public static class ProductVO {
        /**
         * 商品ID
         */
        @ApiModelProperty(value = "商品ID - 商品的唯一标识", example = "1")
        private Long productId;
        
        /**
         * 商品名称
         */
        @ApiModelProperty(value = "商品名称 - 商品的名称", example = "游泳套票")
        private String productName;
        
        /**
         * 商品编码
         */
        @ApiModelProperty(value = "商品编码 - 商品的唯一编码", example = "SP2023001")
        private String productCode;
        
        /**
         * 商品图片
         */
        @ApiModelProperty(value = "商品图片 - 商品的图片URL列表，JSON数组格式", example = "[\"http://example.com/images/product1.jpg\"]")
        private List<String> productImages;
        
        /**
         * 商品封面图片
         */
        @ApiModelProperty(value = "商品封面图片 - 商品的封面图片URL，取自productImages的第一张图", example = "http://example.com/images/product1.jpg")
        private String coverImage;
        
        /**
         * 价格
         */
        @ApiModelProperty(value = "价格 - 商品的当前价格", example = "199.00")
        private String price;
        
        /**
         * 原价
         */
        @ApiModelProperty(value = "原价 - 商品的原价，用于显示划线价", example = "299.00")
        private String originalPrice;
        
        /**
         * 销量
         */
        @ApiModelProperty(value = "销量 - 商品的销售数量", example = "100")
        private Integer sales;
    }

    @Data
    @ApiModel(value = "VenueDetailVO.UserStorageCardVO", description = "用户储值卡信息")
    public static class UserStorageCardVO {
        /**
         * 用户会员卡ID
         */
        @ApiModelProperty(value = "用户会员卡ID - 用户会员卡的唯一标识", example = "1")
        private Long userCardId;

        /**
         * 会员卡ID
         */
        @ApiModelProperty(value = "会员卡ID - 会员卡的唯一标识", example = "1")
        private Long cardId;

        /**
         * 会员卡名称
         */
        @ApiModelProperty(value = "会员卡名称 - 会员卡的名称", example = "储值卡")
        private String cardName;

        /**
         * 会员卡号
         */
        @ApiModelProperty(value = "会员卡号 - 用户的会员卡号", example = "VIP001")
        private String cardNumber;

        /**
         * 余额
         */
        @ApiModelProperty(value = "余额 - 储值卡的当前余额", example = "100.50")
        private java.math.BigDecimal balance;

        /**
         * 有效期开始时间
         */
        @ApiModelProperty(value = "有效期开始时间 - 储值卡的有效期开始时间")
        private java.time.LocalDateTime validityStartTime;

        /**
         * 有效期结束时间
         */
        @ApiModelProperty(value = "有效期结束时间 - 储值卡的有效期结束时间")
        private java.time.LocalDateTime validityEndTime;

        /**
         * 状态（0正常 1停用）
         */
        @ApiModelProperty(value = "状态 - 0正常 1停用", example = "0")
        private String status;
    }
}