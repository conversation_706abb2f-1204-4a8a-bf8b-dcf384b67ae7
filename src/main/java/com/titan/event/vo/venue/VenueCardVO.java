package com.titan.event.vo.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 场馆卡信息VO
 */
@Data
@ApiModel(value = "VenueCardVO", description = "场馆卡信息VO")
public class VenueCardVO {
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "储值卡")
    private String cardName;
    
    /**
     * 封面图片
     */
    @ApiModelProperty(value = "封面图片", example = "http://example.com/card.jpg")
    private String coverImage;
    
    /**
     * 卡类型（1储值卡 2计次卡）
     */
    @ApiModelProperty(value = "卡类型", example = "1", notes = "1储值卡 2计次卡")
    private Integer cardType;
    
    /**
     * 卡类型名称
     */
    @ApiModelProperty(value = "卡类型名称", example = "储值卡")
    private String cardTypeName;
    
    /**
     * 是否无限次（0否 1是）
     */
    @ApiModelProperty(value = "是否无限次", example = "0", notes = "0否 1是")
    private Integer unlimitedUsage;
    
    /**
     * 有效期类型（1天 2月 3年 4固定时间范围）
     */
    @ApiModelProperty(value = "有效期类型", example = "2", notes = "1天 2月 3年 4固定时间范围")
    private Integer validityType;
    
    /**
     * 有效期值
     */
    @ApiModelProperty(value = "有效期值", example = "12")
    private Integer validityValue;
    
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    private LocalDateTime validityStartTime;
    
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    private LocalDateTime validityEndTime;
    
    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态", example = "0", notes = "0正常 1停用")
    private String status;
    
    /**
     * 卡介绍
     */
    @ApiModelProperty(value = "卡介绍", example = "储值卡介绍")
    private String cardDescription;
    
    /**
     * 卡价格
     */
    @ApiModelProperty(value = "卡价格", example = "100.00")
    private BigDecimal cardPrice;
    
    /**
     * 卡划线价格
     */
    @ApiModelProperty(value = "卡划线价格", example = "120.00")
    private BigDecimal cardOriginalPrice;
    
    /**
     * 总销量（注水销量+实际销量）
     */
    @ApiModelProperty(value = "总销量", example = "100")
    private Integer totalSales;
    
    /**
     * 次数
     */
    @ApiModelProperty(value = "次数", example = "10")
    private Integer usageCount;
    
    /**
     * 用户是否拥有此卡（仅储值卡有效）
     */
    @ApiModelProperty(value = "用户是否拥有此卡", example = "true", notes = "仅储值卡有效")
    private Boolean hasCard;
    
    /**
     * 用户卡余额（仅储值卡有效）
     */
    @ApiModelProperty(value = "用户卡余额", example = "50.00", notes = "仅储值卡有效")
    private BigDecimal userCardBalance;
    
    /**
     * 用户卡号（仅储值卡有效）
     */
    @ApiModelProperty(value = "用户卡号", example = "VIP001", notes = "仅储值卡有效")
    private String userCardNumber;
}
