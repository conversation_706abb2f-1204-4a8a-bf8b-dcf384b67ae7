package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地区域信息VO
 */
@Data
@ApiModel(value = "场地区域信息")
public class SiteSectionInfoVO {

    @ApiModelProperty(value = "区域ID")
    private Long sectionId;

    @ApiModelProperty(value = "区域名称")
    private String sectionName;

    @ApiModelProperty(value = "区域编号")
    private String sectionCode;
    
    @ApiModelProperty(value = "区域状态（0正常 1停用）")
    private String status;
    
    @ApiModelProperty(value = "座位数量")
    private Integer seatCount;
    
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;
    
    @ApiModelProperty(value = "区域类型")
    private Integer sectionType;
    
    @ApiModelProperty(value = "区域行数")
    private Integer rowCount;
    
    @ApiModelProperty(value = "区域列数")
    private Integer columnCount;
    
    @ApiModelProperty(value = "区域描述")
    private String sectionDesc;
    
    @ApiModelProperty(value = "场地ID")
    private Long siteId;
} 