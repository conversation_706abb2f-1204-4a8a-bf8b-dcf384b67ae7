package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 退款信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "退款信息", description = "订单退款相关的详细信息，包含退款状态、退款单号、退款时间等")
public class RefundInfoVO {

    @ApiModelProperty(value = "退款状态 - SUCCESS-退款成功，PROCESSING-退款处理中，ABNORMAL-退款异常，CLOSED-退款关闭", example = "SUCCESS")
    private String refundStatus;
    
    @ApiModelProperty(value = "退款单号 - 微信支付退款单号", example = "R20231023123456789")
    private String refundId;
    
    @ApiModelProperty(value = "退款申请时间 - 退款申请创建时间", example = "2023-10-23 15:45:30")
    private LocalDateTime refundOrderTime;
    
    @ApiModelProperty(value = "退款回调通知时间 - 微信支付退款结果回调通知时间", example = "2023-10-23 15:47:20")
    private LocalDateTime refundNotifyTime;
    
    @ApiModelProperty(value = "是否可退款 - 订单是否允许退款操作：true-可退款，false-不可退款", example = "true", required = true)
    private Boolean refundable;
} 