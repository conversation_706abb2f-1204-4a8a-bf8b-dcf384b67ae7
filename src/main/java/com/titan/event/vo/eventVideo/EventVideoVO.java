package com.titan.event.vo.eventVideo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.util.LocalDateTimeDeserializer;
import com.titan.event.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "赛事视频信息",description = "赛事视频信息")
public class EventVideoVO {

    @ApiModelProperty(name = "视频ID", value = "视频ID")
    private Long id;

    @ApiModelProperty(name = "视频标题", value = "视频标题")
    private String videoTitle;

    @ApiModelProperty(value = "视频封面", name = "视频封面")
    private String videoPic;

    @ApiModelProperty(name = "发布时间", value = "发布时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime publishTime;

    @ApiModelProperty(name = "视频简介", value = "视频简介")
    private String videoContent;

    @ApiModelProperty(name = "预览地址", value = "预览地址")
    private String viewUrl;

    private String videoChannelId;

    private String videoId;

}
