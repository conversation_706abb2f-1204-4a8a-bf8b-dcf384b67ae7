package com.titan.event.vo.countryCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "国家与地区",description = "国家与地区")
public class CountryCodeVO {

    private Long id;

    private String countryEng;

    private String countryChn;

    private String isoCountryCode;

//    private String isoCountryCode2;

}
