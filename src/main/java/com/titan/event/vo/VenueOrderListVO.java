package com.titan.event.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 场馆订单列表项VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "场馆订单列表项", description = "订单列表中的单个订单信息")
public class VenueOrderListVO {

    @ApiModelProperty(value = "订单ID", example = "1001", required = true)
    private Long id;

    @ApiModelProperty(value = "订单编号", example = "O2023102312345678901234", required = true)
    private String orderId;

    @ApiModelProperty(value = "订单类型: 1-场馆预约，2-场馆产品，3-卡片购买", example = "1", required = true)
    private Integer orderType;

    @ApiModelProperty(value = "场馆ID", example = "2001")
    private Long venueId;

    @ApiModelProperty(value = "场馆名称", example = "阳光体育馆")
    private String venueName;

    @ApiModelProperty(value = "订单金额", example = "199.00", required = true)
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "订单状态: 1-待支付, 2-待核销, 3-已完成, 4-已作废", example = "1", required = true)
    private Integer orderStatus;

    @ApiModelProperty(value = "支付状态", example = "NOTPAY", required = true)
    private String payStatus;

    @ApiModelProperty(value = "核销状态: 0-未核销, 1-已核销", example = "0")
    private Integer verificationStatus;

    @ApiModelProperty(value = "核销码", example = "123456789012")
    private String verificationCode;

    @ApiModelProperty(value = "订单创建时间", example = "2023-10-23 12:34:56", required = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订单标题，根据订单类型显示场地名称或商品名称", example = "羽毛球场A / 运动饮料", required = true)
    private String title;

    @ApiModelProperty(value = "订单图片，根据订单类型显示场地图片或商品图片", example = "https://example.com/image.jpg")
    private String image;
    
    @ApiModelProperty(value = "场地类型", example = "羽毛球", notes = "仅场地预订订单返回")
    private String siteType;
    
    @ApiModelProperty(value = "预订时间段列表", notes = "仅场地预订订单返回")
    private List<BookingTimeSlotVO> timeSlots;

    @ApiModelProperty(value = "预支付ID", example = "wx123456789012345678901234567890", notes = "微信支付生成的预支付ID，用于发起小程序支付")
    private String prepayId;
    
    @ApiModelProperty(value = "支付过期时间", example = "2023-10-23 15:30:45", notes = "支付过期时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime timeExpire;
} 