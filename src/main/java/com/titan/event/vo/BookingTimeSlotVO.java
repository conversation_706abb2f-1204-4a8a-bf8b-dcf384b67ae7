package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 预订时间段VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "预订时间段", description = "订单中的预订时间段信息")
public class BookingTimeSlotVO {

    @ApiModelProperty(value = "预订日期", example = "2025-04-01")
    private LocalDate bookingDate;

    @ApiModelProperty(value = "开始时间", example = "13:00")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间", example = "14:00")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "时段价格", example = "100.00")
    private BigDecimal price;
    
    @ApiModelProperty(value = "场地标题", example = "羽毛球2号场", notes = "包含场地类型名称+场地名称+分区名称")
    private String title;
} 