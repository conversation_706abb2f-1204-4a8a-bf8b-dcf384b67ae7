package com.titan.event.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地座位信息VO
 */
@Data
@ApiModel(value = "场地座位信息")
public class SiteSeatInfoVO {

    @ApiModelProperty(value = "座位ID")
    private Long seatId;

    @ApiModelProperty(value = "座位编号")
    private String seatNumber;

    @ApiModelProperty(value = "座位行")
    private String rowNumber;

    @ApiModelProperty(value = "座位列")
    private String columnNumber;

    @ApiModelProperty(value = "座位类型")
    private Integer seatType;

    @ApiModelProperty(value = "座位状态")
    private Integer seatStatus;

    @ApiModelProperty(value = "所属分区ID")
    private Long sectionId;

    @ApiModelProperty(value = "所属分区名称")
    private String sectionName;
} 