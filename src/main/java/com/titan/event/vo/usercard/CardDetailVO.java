package com.titan.event.vo.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片详情VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "卡片详情VO", description = "卡片详情信息，包含卡片信息和场馆客服电话等")
public class CardDetailVO {
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "健身年卡")
    private String cardName;
    
    /**
     * 封面图片
     */
    @ApiModelProperty(value = "封面图片", example = "https://example.com/image.jpg")
    private String coverImage;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 卡类型（1储值卡 2计次卡）
     */
    @ApiModelProperty(value = "卡类型：1储值卡 2计次卡", example = "2")
    private Integer cardType;
    
    /**
     * 卡类型名称
     */
    @ApiModelProperty(value = "卡类型名称", example = "计次卡")
    private String cardTypeName;
    
    /**
     * 是否无限次（0否 1是）
     */
    @ApiModelProperty(value = "是否无限次：0否 1是", example = "0")
    private Integer unlimitedUsage;
    
    /**
     * 卡介绍
     */
    @ApiModelProperty(value = "卡介绍", example = "健身年卡，包含所有健身项目")
    private String cardDescription;
    
    /**
     * 卡价格
     */
    @ApiModelProperty(value = "卡价格", example = "1200.00")
    private BigDecimal cardPrice;
    
    /**
     * 卡划线价格
     */
    @ApiModelProperty(value = "卡划线价格", example = "1500.00")
    private BigDecimal cardOriginalPrice;
    
    /**
     * 销量
     */
    @ApiModelProperty(value = "销量", example = "100")
    private Integer salesVolume;
    
    /**
     * 注水销量
     */
    @ApiModelProperty(value = "注水销量", example = "500")
    private Integer inflatedSalesVolume;
    
    /**
     * 总销量（真实销量+注水销量）
     */
    @ApiModelProperty(value = "总销量", example = "600")
    private Integer totalSales;
    
    /**
     * 次数（计次卡专用）
     */
    @ApiModelProperty(value = "次数", example = "20")
    private Integer usageCount;
    
    /**
     * 有效期类型（1天 2月 3年 4固定时间范围）
     */
    @ApiModelProperty(value = "有效期类型：1天 2月 3年 4固定时间范围", example = "3")
    private Integer validityType;
    
    /**
     * 有效期值
     */
    @ApiModelProperty(value = "有效期值", example = "1")
    private Integer validityValue;
    
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间", example = "2023-01-01T00:00:00")
    private LocalDateTime validityStartTime;
    
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间", example = "2023-12-31T23:59:59")
    private LocalDateTime validityEndTime;
    
    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态：0正常 1停用", example = "0")
    private String status;
    
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称", example = "正常")
    private String statusName;
    
    /**
     * 是否启用副卡（0否 1是）
     */
    @ApiModelProperty(value = "是否启用副卡：0否 1是", example = "1")
    private Boolean subCardEnabled;
    
    /**
     * 副卡数量限制
     */
    @ApiModelProperty(value = "副卡数量限制", example = "2")
    private Integer subCardLimit;
    
    /**
     * 场馆联系人
     */
    @ApiModelProperty(value = "场馆联系人", example = "张经理")
    private String contactPerson;
    
    /**
     * 场馆联系电话
     */
    @ApiModelProperty(value = "场馆联系电话", example = "***********")
    private String contactPhone;
    
    /**
     * 微信客服ID
     */
    @ApiModelProperty(value = "微信客服ID", example = "kf001")
    private String wxCustomerServiceId;
    
    /**
     * 场馆营业时间描述
     */
    @ApiModelProperty(value = "场馆营业时间描述", example = "周一至周日 06:00-22:00")
    private String businessTimeDesc;
    
    /**
     * 场馆地址
     */
    @ApiModelProperty(value = "场馆地址", example = "北京市朝阳区xxx路xxx号")
    private String venueAddress;
}
