package com.titan.event.vo.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户卡详情VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "用户卡详情VO", description = "用户卡的详细信息，包含主副卡信息和场馆联系方式")
public class UserCardDetailVO {
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1")
    private Long userCardId;
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "储值卡")
    private String cardName;
    
    /**
     * 封面图片
     */
    @ApiModelProperty(value = "封面图片", example = "http://example.com/card.jpg")
    private String coverImage;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号", example = "VIP001")
    private String cardNumber;
    
    /**
     * 卡类型（0-权益卡 1-储值卡 2-计次卡 3-会员卡）
     */
    @ApiModelProperty(value = "卡类型：0-权益卡 1-储值卡 2-计次卡 3-会员卡", example = "1")
    private Integer cardType;
    
    /**
     * 卡类型名称
     */
    @ApiModelProperty(value = "卡类型名称", example = "储值卡")
    private String cardTypeName;
    
    /**
     * 是否无限次（0否 1是）
     */
    @ApiModelProperty(value = "是否无限次：0否 1是", example = "0")
    private Integer unlimitedUsage;
    
    /**
     * 余额（储值卡专用）
     */
    @ApiModelProperty(value = "余额（储值卡专用）", example = "100.50")
    private BigDecimal balance;
    
    /**
     * 剩余次数（计次卡专用）
     */
    @ApiModelProperty(value = "剩余次数（计次卡专用）", example = "10")
    private Integer remainingCount;
    
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间", example = "2023-01-01T00:00:00")
    private LocalDateTime validityStartTime;
    
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间", example = "2024-01-01T00:00:00")
    private LocalDateTime validityEndTime;
    
    /**
     * 状态（0正常 1停用 2过期）
     */
    @ApiModelProperty(value = "状态：0正常 1停用 2过期", example = "0")
    private String status;
    
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称", example = "正常")
    private String statusName;
    
    /**
     * 是否可用
     */
    @ApiModelProperty(value = "是否可用（综合状态和有效期判断）", example = "true")
    private Boolean available;
    
    /**
     * 卡介绍
     */
    @ApiModelProperty(value = "卡介绍", example = "这是一张储值卡")
    private String cardDescription;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2023-01-01T00:00:00")
    private LocalDateTime createTime;
    
    /**
     * 场馆联系人
     */
    @ApiModelProperty(value = "场馆联系人", example = "张经理")
    private String contactPerson;
    
    /**
     * 场馆联系电话
     */
    @ApiModelProperty(value = "场馆联系电话", example = "13812345678")
    private String contactPhone;
    
    /**
     * 微信客服ID
     */
    @ApiModelProperty(value = "微信客服ID", example = "wx_service_001")
    private String wxCustomerServiceId;
    
    /**
     * 主副卡信息列表
     */
    @ApiModelProperty(value = "主副卡信息列表")
    private List<CardRelationVO> cardRelations;
    
    /**
     * 主副卡关系信息VO
     */
    @Data
    @ApiModel(value = "主副卡关系信息VO", description = "主副卡关系信息")
    public static class CardRelationVO {
        
        /**
         * 关系ID
         */
        @ApiModelProperty(value = "关系ID", example = "1")
        private Long relationId;
        
        /**
         * 用户会员卡ID
         */
        @ApiModelProperty(value = "用户会员卡ID", example = "1")
        private Long cardUserId;
        
        /**
         * 卡关系类型（1主卡 2副卡）
         */
        @ApiModelProperty(value = "卡关系类型：1主卡 2副卡", example = "1")
        private Integer cardRelationType;
        
        /**
         * 卡关系类型名称
         */
        @ApiModelProperty(value = "卡关系类型名称", example = "主卡")
        private String cardRelationTypeName;
        
        /**
         * 会员卡号
         */
        @ApiModelProperty(value = "会员卡号", example = "VIP001")
        private String cardNumber;
        
        /**
         * 用户名称
         */
        @ApiModelProperty(value = "用户名称", example = "张三")
        private String userName;
        
        /**
         * 状态（0正常 1停用）
         */
        @ApiModelProperty(value = "状态：0正常 1停用", example = "0")
        private String status;
        
        /**
         * 状态名称
         */
        @ApiModelProperty(value = "状态名称", example = "正常")
        private String statusName;
    }
}
