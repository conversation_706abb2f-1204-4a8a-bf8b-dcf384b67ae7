package com.titan.event.vo.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 副卡信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "副卡信息VO", description = "副卡的详细信息")
public class SubCardInfoVO {
    
    /**
     * 关系ID
     */
    @ApiModelProperty(value = "关系ID", example = "1")
    private Long relationId;
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1")
    private Long cardUserId;
    
    /**
     * 副卡用户手机号
     */
    @ApiModelProperty(value = "副卡用户手机号", example = "13812345678")
    private String userPhone;
    
    /**
     * 副卡用户姓名
     */
    @ApiModelProperty(value = "副卡用户姓名", example = "张三")
    private String userName;
    
    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号", example = "VIP002")
    private String cardNumber;
    
    /**
     * 卡关系类型（固定为2-副卡）
     */
    @ApiModelProperty(value = "卡关系类型：2-副卡", example = "2")
    private Integer cardRelationType = 2;
    
    /**
     * 卡关系类型名称
     */
    @ApiModelProperty(value = "卡关系类型名称", example = "副卡")
    private String cardRelationTypeName = "副卡";
    
    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态：0正常 1停用", example = "0")
    private String status;
    
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称", example = "正常")
    private String statusName;
}
