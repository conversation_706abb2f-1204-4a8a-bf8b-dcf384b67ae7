package com.titan.event.vo.usercard;

import com.titan.event.vo.PaymentInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片购买响应VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "卡片购买响应VO", description = "卡片购买响应信息，包含支付信息和订单信息")
public class CardPurchaseVO {
    
    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID", example = "CARD_1640995200000_abc12345")
    private String orderId;
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "健身年卡")
    private String cardName;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 卡类型（1储值卡 2计次卡）
     */
    @ApiModelProperty(value = "卡类型：1储值卡 2计次卡", example = "2")
    private Integer cardType;
    
    /**
     * 卡类型名称
     */
    @ApiModelProperty(value = "卡类型名称", example = "计次卡")
    private String cardTypeName;
    
    /**
     * 购买数量（固定为1）
     */
    @ApiModelProperty(value = "购买数量（固定为1）", example = "1")
    private Integer quantity;
    
    /**
     * 单价
     */
    @ApiModelProperty(value = "单价", example = "1200.00")
    private BigDecimal unitPrice;
    
    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额", example = "1200.00")
    private BigDecimal totalAmount;
    
    /**
     * 次数（计次卡专用）
     */
    @ApiModelProperty(value = "次数", example = "20")
    private Integer usageCount;
    
    /**
     * 有效期类型（1天 2月 3年 4固定时间范围）
     */
    @ApiModelProperty(value = "有效期类型：1天 2月 3年 4固定时间范围", example = "3")
    private Integer validityType;
    
    /**
     * 有效期值
     */
    @ApiModelProperty(value = "有效期值", example = "1")
    private Integer validityValue;
    
    /**
     * 支付信息
     */
    @ApiModelProperty(value = "支付信息")
    private PaymentInfoVO paymentInfo;
    
    /**
     * 购买时间
     */
    @ApiModelProperty(value = "购买时间", example = "2023-01-01T10:00:00")
    private LocalDateTime purchaseTime;
}
