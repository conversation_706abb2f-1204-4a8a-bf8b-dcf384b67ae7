package com.titan.event.vo.usercard;

import com.titan.event.vo.PaymentInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 储值卡充值响应VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "储值卡充值响应VO", description = "储值卡充值响应信息，包含支付信息和卡片信息")
public class StorageCardRechargeVO {
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1")
    private Long userCardId;
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "储值卡")
    private String cardName;
    
    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号", example = "VIP001")
    private String cardNumber;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 充值前余额
     */
    @ApiModelProperty(value = "充值前余额", example = "50.00")
    private BigDecimal beforeBalance;
    
    /**
     * 充值金额
     */
    @ApiModelProperty(value = "充值金额", example = "100.00")
    private BigDecimal rechargeAmount;
    
    /**
     * 充值后余额
     */
    @ApiModelProperty(value = "充值后余额", example = "150.00")
    private BigDecimal afterBalance;
    
    /**
     * 是否为新开卡
     */
    @ApiModelProperty(value = "是否为新开卡", example = "false")
    private Boolean isNewCard;
    
    /**
     * 支付信息
     */
    @ApiModelProperty(value = "支付信息")
    private PaymentInfoVO paymentInfo;
    
    /**
     * 充值时间
     */
    @ApiModelProperty(value = "充值时间", example = "2023-01-01T10:00:00")
    private LocalDateTime rechargeTime;
}
