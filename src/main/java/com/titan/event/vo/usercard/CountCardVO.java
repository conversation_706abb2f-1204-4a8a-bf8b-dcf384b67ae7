package com.titan.event.vo.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 计次卡信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "计次卡信息VO", description = "计次卡的基本信息和剩余次数")
public class CountCardVO {
    
    /**
     * 用户卡ID
     */
    @ApiModelProperty(value = "用户卡ID", example = "1")
    private Long userCardId;
    
    /**
     * 卡号
     */
    @ApiModelProperty(value = "卡号", example = "CARD1640995200000")
    private String cardNumber;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "健身计次卡")
    private String cardName;
    
    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名", example = "张三")
    private String userName;
    
    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号", example = "13800138000")
    private String userPhone;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 剩余次数
     */
    @ApiModelProperty(value = "剩余次数", example = "15")
    private Integer remainingCount;
    
    /**
     * 总次数
     */
    @ApiModelProperty(value = "总次数", example = "20")
    private Integer totalCount;
    
    /**
     * 已使用次数
     */
    @ApiModelProperty(value = "已使用次数", example = "5")
    private Integer usedCount;
    
    /**
     * 卡状态（0正常 1冻结 2过期）
     */
    @ApiModelProperty(value = "卡状态：0正常 1冻结 2过期", example = "0")
    private String status;
    
    /**
     * 卡状态名称
     */
    @ApiModelProperty(value = "卡状态名称", example = "正常")
    private String statusName;
    
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间", example = "2023-01-01T00:00:00")
    private LocalDateTime validityStartTime;
    
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间", example = "2023-12-31T23:59:59")
    private LocalDateTime validityEndTime;
    
    /**
     * 是否过期
     */
    @ApiModelProperty(value = "是否过期", example = "false")
    private Boolean isExpired;
    
    /**
     * 是否可用（未过期且有剩余次数）
     */
    @ApiModelProperty(value = "是否可用", example = "true")
    private Boolean isAvailable;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2023-01-01T10:00:00")
    private LocalDateTime createTime;
}
