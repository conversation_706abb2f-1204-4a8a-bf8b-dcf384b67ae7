package com.titan.event.vo.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户卡使用明细VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "用户卡使用明细VO", description = "用户卡的使用明细信息")
public class UserCardUsageDetailVO {
    
    /**
     * 明细ID
     */
    @ApiModelProperty(value = "明细ID", example = "1")
    private Long detailId;
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;
    
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称", example = "张三")
    private String userName;
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 会员卡名称
     */
    @ApiModelProperty(value = "会员卡名称", example = "储值卡")
    private String cardName;

    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1")
    private Long userCardId;

    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 操作类型（1消费 2充值 3退款）
     */
    @ApiModelProperty(value = "操作类型：1消费 2充值 3退款", example = "1")
    private Integer operationType;
    
    /**
     * 操作类型名称
     */
    @ApiModelProperty(value = "操作类型名称", example = "消费")
    private String operationTypeName;
    
    /**
     * 操作金额
     */
    @ApiModelProperty(value = "操作金额", example = "50.00")
    private BigDecimal amount;
    
    /**
     * 操作次数
     */
    @ApiModelProperty(value = "操作次数", example = "1")
    private Integer usageCount;
    
    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间", example = "2023-01-01T10:00:00")
    private LocalDateTime operationTime;
    
    /**
     * 操作说明
     */
    @ApiModelProperty(value = "操作说明", example = "场地预订消费")
    private String operationDescription;
    
    /**
     * 操作人员
     */
    @ApiModelProperty(value = "操作人员", example = "系统")
    private String operator;
    
    /**
     * 操作来源（1管理端 2用户端）
     */
    @ApiModelProperty(value = "操作来源：1管理端 2用户端", example = "2")
    private Integer operationSource;
    
    /**
     * 操作来源名称
     */
    @ApiModelProperty(value = "操作来源名称", example = "用户端")
    private String operationSourceName;
    
    /**
     * 操作后余额
     */
    @ApiModelProperty(value = "操作后余额", example = "450.00")
    private BigDecimal balance;
    
    /**
     * 操作后剩余次数
     */
    @ApiModelProperty(value = "操作后剩余次数", example = "9")
    private Integer remainingCount;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "备注信息")
    private String remark;
}
