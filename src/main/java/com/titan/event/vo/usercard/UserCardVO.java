package com.titan.event.vo.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户卡信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "用户卡信息VO", description = "用户持有的卡信息")
public class UserCardVO {
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1")
    private Long userCardId;
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1")
    private Long cardId;
    
    /**
     * 卡名称
     */
    @ApiModelProperty(value = "卡名称", example = "储值卡")
    private String cardName;
    
    /**
     * 封面图片
     */
    @ApiModelProperty(value = "封面图片", example = "http://example.com/card.jpg")
    private String coverImage;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1")
    private Long venueId;
    
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称", example = "体育馆")
    private String venueName;
    
    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号", example = "VIP001")
    private String cardNumber;
    
    /**
     * 卡类型（0-权益卡 1-储值卡 2-计次卡 3-会员卡）
     */
    @ApiModelProperty(value = "卡类型：0-权益卡 1-储值卡 2-计次卡 3-会员卡", example = "1")
    private Integer cardType;

    /**
     * 卡类型名称
     */
    @ApiModelProperty(value = "卡类型名称", example = "储值卡")
    private String cardTypeName;

    /**
     * 是否无限次（0否 1是）
     */
    @ApiModelProperty(value = "是否无限次：0否 1是", example = "0")
    private Integer unlimitedUsage;

    /**
     * 余额（储值卡专用）
     */
    @ApiModelProperty(value = "余额（储值卡专用）", example = "100.50")
    private BigDecimal balance;

    /**
     * 剩余次数（计次卡专用）
     */
    @ApiModelProperty(value = "剩余次数（计次卡专用）", example = "10")
    private Integer remainingCount;
    
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间", example = "2023-01-01T00:00:00")
    private LocalDateTime validityStartTime;
    
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间", example = "2024-01-01T00:00:00")
    private LocalDateTime validityEndTime;
    
    /**
     * 状态（0正常 1停用 2过期）
     */
    @ApiModelProperty(value = "状态：0正常 1停用 2过期", example = "0")
    private String status;

    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称", example = "正常")
    private String statusName;

    /**
     * 是否可用
     */
    @ApiModelProperty(value = "是否可用（综合状态和有效期判断）", example = "true")
    private Boolean available;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2023-01-01T00:00:00")
    private LocalDateTime createTime;
}
