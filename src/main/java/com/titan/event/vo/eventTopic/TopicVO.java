package com.titan.event.vo.eventTopic;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.util.LocalDateTimeDeserializer;
import com.titan.event.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "专题信息",description = "专题信息")
public class TopicVO {


    @ApiModelProperty(name = "专题名称", value = "专题名称")
    private String topicName;

    @ApiModelProperty(value = "专题封面", name = "专题封面")
    private String topicPic;


}
