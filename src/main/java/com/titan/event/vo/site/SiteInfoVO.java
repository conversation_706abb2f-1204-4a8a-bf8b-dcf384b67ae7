package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地详细信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地详细信息", description = "场地的详细信息")
public class SiteInfoVO {
    
    /**
     * 场地ID
     */
    @ApiModelProperty(value = "场地ID")
    private Long siteId;
    
    /**
     * 场地名称
     */
    @ApiModelProperty(value = "场地名称")
    private String siteName;
    
    /**
     * 所属场馆ID
     */
    @ApiModelProperty(value = "所属场馆ID")
    private Long venueId;
    
    /**
     * 场地类型(1:羽毛球, 2:篮球, 3:乒乓球, 4:网球, 5:足球, 6:其他)
     */
    @ApiModelProperty(value = "场地类型")
    private Integer siteType;
    
    /**
     * 场地状态(0:正常, 1:维修中, 2:已关闭)
     */
    @ApiModelProperty(value = "场地状态")
    private Integer siteStatus;
    
    /**
     * 是否允许分割(0:不允许, 1:允许)
     */
    @ApiModelProperty(value = "是否允许分割")
    private Boolean isSplitAllowed;
    
    /**
     * 最大容纳人数
     */
    @ApiModelProperty(value = "最大容纳人数")
    private Integer maxCapacity;
    
    /**
     * 是否启用节假日价格
     */
    @ApiModelProperty(value = "是否启用节假日价格")
    private String holidayEnabled;
    
    /**
     * 是否启用特殊日期价格
     */
    @ApiModelProperty(value = "是否启用特殊日期价格")
    private String specialDateEnabled;
} 