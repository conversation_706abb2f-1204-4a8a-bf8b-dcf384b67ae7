package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地简要信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地简要信息", description = "场地基本信息")
public class SiteSimpleVO {
    
    /**
     * 场地ID
     */
    @ApiModelProperty(value = "场地ID")
    private Long siteId;
    
    /**
     * 所属场馆ID
     */
    @ApiModelProperty(value = "所属场馆ID")
    private Long venueId;
    
    /**
     * 场地名称
     */
    @ApiModelProperty(value = "场地名称")
    private String siteName;
    
    /**
     * 场地类型(1:羽毛球, 2:篮球, 3:乒乓球, 4:网球, 5:足球, 6:其他)
     */
    @ApiModelProperty(value = "场地类型")
    private Integer siteType;
} 