package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 场地及分区价格信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地及分区价格信息", description = "包含场地信息、分区列表和价格数据")
public class SiteWithSectionsVO {
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期，格式YYYY-MM-DD")
    private String date;
    
    /**
     * 场地信息
     */
    @ApiModelProperty(value = "场地基本信息")
    private SiteInfoVO site;
    
    /**
     * 分区列表
     */
    @ApiModelProperty(value = "分区列表")
    private List<SiteSectionInfoVO> sections = new ArrayList<>();
    
    /**
     * 价格数据
     */
    @ApiModelProperty(value = "价格数据")
    private List<SitePriceVO> priceData = new ArrayList<>();
} 