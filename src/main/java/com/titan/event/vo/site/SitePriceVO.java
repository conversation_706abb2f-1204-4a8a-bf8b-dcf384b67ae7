package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 场地价格信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地价格信息", description = "场地价格数据")
public class SitePriceVO {
    
    /**
     * 场地ID
     */
    @ApiModelProperty(value = "场地ID")
    private Long siteId;
    
    /**
     * 场地名称
     */
    @ApiModelProperty(value = "场地名称")
    private String siteName;
    
    /**
     * 分区ID，全场时为空
     */
    @ApiModelProperty(value = "分区ID，全场时为空")
    private Long sectionId;
    
    /**
     * 分区名称，全场时为"全场"
     */
    @ApiModelProperty(value = "分区名称，全场时为全场")
    private String sectionName;
    
    /**
     * 分区编号，仅当为分区时有值
     */
    @ApiModelProperty(value = "分区编号，仅当为分区时有值")
    private String sectionCode;
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期，格式YYYY-MM-DD")
    private String date;
    
    /**
     * 类型：site(全场) 或 section(分区)
     */
    @ApiModelProperty(value = "类型：site(全场) 或 section(分区)")
    private String type;
    
    /**
     * 价格时段列表
     */
    @ApiModelProperty(value = "价格时段列表")
    private List<TimeslotPriceVO> prices = new ArrayList<>();
} 