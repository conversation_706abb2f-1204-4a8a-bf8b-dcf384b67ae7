package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地分区简要信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地分区简要信息", description = "场地分区基本信息")
public class SiteSectionSimpleVO {
    
    /**
     * 分区ID
     */
    @ApiModelProperty(value = "分区ID")
    private Long sectionId;
    
    /**
     * 所属场地ID
     */
    @ApiModelProperty(value = "所属场地ID")
    private Long siteId;
    
    /**
     * 分区名称
     */
    @ApiModelProperty(value = "分区名称")
    private String sectionName;
    
    /**
     * 分区编号
     */
    @ApiModelProperty(value = "分区编号")
    private String sectionCode;
} 