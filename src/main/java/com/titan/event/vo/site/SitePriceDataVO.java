package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 场地价格信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地价格信息", description = "场地价格数据")
public class SitePriceDataVO {
    /**
     * 价格数据，key为场地ID，value为该场地的价格数据列表
     */
    @ApiModelProperty(value = "价格数据，key为场地ID")
    private Map<Long, List<Map<String, Object>>> priceData = new HashMap<>();
} 