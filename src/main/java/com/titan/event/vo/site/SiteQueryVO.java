package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 场地查询响应结果
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地查询响应", description = "场地信息及其分区信息")
public class SiteQueryVO {
    
    /**
     * 场地列表
     */
    @ApiModelProperty(value = "场地列表")
    private List<SiteSimpleVO> siteList = new ArrayList<>();
    
    /**
     * 场地分区信息，key为场地ID，value为该场地下的分区列表
     */
    @ApiModelProperty(value = "场地分区信息")
    private Map<Long, List<SiteSectionSimpleVO>> siteSections = new HashMap<>();
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String date;
    
    /**
     * 是否节假日
     */
    @ApiModelProperty(value = "是否节假日")
    private Boolean isHoliday;
    
    /**
     * 是否特殊日期
     */
    @ApiModelProperty(value = "是否特殊日期")
    private Boolean isSpecialDate;
    
    /**
     * 特殊日期ID
     */
    @ApiModelProperty(value = "特殊日期ID")
    private Long specialDateId;
    
    /**
     * 场地价格数据，key为场地ID，value为该场地的价格数据列表
     */
    @ApiModelProperty(value = "场地价格数据")
    private Map<Long, List<SitePriceVO>> priceData = new HashMap<>();
    
    /**
     * 捆绑订场数据，key为场地ID，value为该场地的捆绑订场数据列表
     */
    @ApiModelProperty(value = "捆绑订场数据")
    private Map<Long, List<SitePriceVO>> bundleBookingData = new HashMap<>();
} 