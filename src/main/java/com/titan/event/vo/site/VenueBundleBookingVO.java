package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 捆绑订场信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "捆绑订场信息", description = "捆绑订场数据")
public class VenueBundleBookingVO {
    
    /**
     * 捆绑订场ID
     */
    @ApiModelProperty(value = "捆绑订场ID")
    private Long id;
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID")
    private Long venueId;
    
    /**
     * 场地类型
     */
    @ApiModelProperty(value = "场地类型")
    private Integer siteType;
    
    /**
     * 场地ID列表
     */
    @ApiModelProperty(value = "场地ID列表")
    private List<Long> siteIds = new ArrayList<>();
    
    /**
     * 场地名称
     */
    @ApiModelProperty(value = "场地名称")
    private String siteName;
    
    /**
     * 分区ID列表
     */
    @ApiModelProperty(value = "分区ID列表")
    private List<Long> sectionIds = new ArrayList<>();
    
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;
    
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;
    
    /**
     * 星期几列表
     */
    @ApiModelProperty(value = "星期几列表")
    private List<Integer> weekDays = new ArrayList<>();
    
    /**
     * 时间段列表
     */
    @ApiModelProperty(value = "时间段列表")
    private List<String> timeRanges = new ArrayList<>();
} 