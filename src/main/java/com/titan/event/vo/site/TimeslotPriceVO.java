package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 时段价格信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "时段价格信息", description = "场地时段价格数据")
public class TimeslotPriceVO {
    
    /**
     * 时段ID (0-23，表示0点到23点)
     */
    @ApiModelProperty(value = "时段ID (0-23，表示0点到23点)")
    private Integer timeSlot;
    
    /**
     * 时段价格
     */
    @ApiModelProperty(value = "时段价格")
    private BigDecimal timeslotPrice;
    
    /**
     * 价格来源 (1:regular, 2:holiday, 3:special, 0:none)
     */
    @ApiModelProperty(value = "价格来源 (1:regular, 2:holiday, 3:special, 0:none)")
    private Integer priceSource;
    
    /**
     * 时段显示名称，格式如 "00:00 ~ 01:00"
     */
    @ApiModelProperty(value = "时段显示名称，格式如 00:00 ~ 01:00")
    private String timeDisplay;
    
    /**
     * 开始时间，格式如 "00:00"
     */
    @ApiModelProperty(value = "开始时间，格式如 00:00")
    private String start;
    
    /**
     * 结束时间，格式如 "01:00"
     */
    @ApiModelProperty(value = "结束时间，格式如 01:00")
    private String end;
    
    /**
     * 价格来源名称 (regular, holiday, special, none)
     */
    @ApiModelProperty(value = "价格来源名称 (regular, holiday, special, none)")
    private String sourceDisplay;
    
    /**
     * 时段是否已过期
     */
    @ApiModelProperty(value = "时段是否已过期")
    private Boolean isExpired;
    
    /**
     * 预订状态 (available, booked)
     */
    @ApiModelProperty(value = "预订状态 (available, booked)")
    private String bookingStatus;
} 