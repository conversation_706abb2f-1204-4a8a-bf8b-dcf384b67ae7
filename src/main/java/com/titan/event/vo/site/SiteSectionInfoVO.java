package com.titan.event.vo.site;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地分区详细信息VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地分区详细信息", description = "场地分区的详细信息")
public class SiteSectionInfoVO {
    
    /**
     * 分区ID
     */
    @ApiModelProperty(value = "分区ID")
    private Long sectionId;
    
    /**
     * 分区名称
     */
    @ApiModelProperty(value = "分区名称")
    private String sectionName;
    
    /**
     * 分区编号
     */
    @ApiModelProperty(value = "分区编号")
    private String sectionCode;
    
    /**
     * 分区状态（0正常 1停用）
     */
    @ApiModelProperty(value = "分区状态（0正常 1停用）")
    private String status;
    
    /**
     * 座位数量
     */
    @ApiModelProperty(value = "座位数量")
    private Integer seatCount;
    
    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer orderNum;
} 