package com.titan.event.vo.carouselchart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "轮播图信息响应体",description = "轮播图信息响应体")
public class CarouselChartVO {

    @ApiModelProperty(name = "类型 0资讯 1资讯专题 2赛事 3赛事专题 4小程序页面 5公众号文章 6视频号主页 7视频号视频 8商城 9其他小程序", value = "类型 0资讯 1资讯专题 2赛事 3赛事专题 4小程序页面 5公众号文章 6视频号主页 7视频号视频 8商城 9其他小程序")
    private Long carouselType;

    @ApiModelProperty(name = "轮播图片", value = "轮播图片")
    private String carouselPic;

    @ApiModelProperty(name = "内容", value = "内容")
    private Long contentId;

    @ApiModelProperty(name = "文本1", value = "文本1")
    private String contentText1;

    @ApiModelProperty(name = "文本2", value = "文本2")
    private String contentText2;


}
