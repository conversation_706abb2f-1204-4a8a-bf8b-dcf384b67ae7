package com.titan.event.vo.eventAlbum;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.util.LocalDateTimeDeserializer;
import com.titan.event.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "赛事相册信息",description = "赛事相册信息")
public class EventAlbumVO {

    @ApiModelProperty(name = "相册ID", value = "相册ID")
    private Long id;

    @ApiModelProperty(name = "相册封面", value = "相册封面")
    private String albumPic;

    @ApiModelProperty(value = "相册标题", name = "相册标题")
    private String albumTitle;

    @ApiModelProperty(name = "发布时间", value = "发布时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime publishTime;

    @ApiModelProperty(name = "相册简介", value = "相册简介")
    private String albumContent;

    @ApiModelProperty(name = "相册图片数", value = "相册图片数")
    private Long albumNum;

}
