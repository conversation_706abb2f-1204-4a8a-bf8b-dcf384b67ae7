package com.titan.event.vo.eventAlbum;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.util.LocalDateTimeDeserializer;
import com.titan.event.util.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "赛事相册图片信息",description = "赛事相册图片信息")
public class EventAlbumInfoVO {

    @ApiModelProperty(name = "图片ID", value = "图片ID")
    private Long id;

//    @ApiModelProperty(name = "相册id", value = "相册id")
//    private String albumId;

    @ApiModelProperty(value = "图片链接", name = "图片链接")
    private String picUrl;

    @ApiModelProperty(value = "图片链接", name = "图片链接")
    private String squareThumbnail;

    @ApiModelProperty(value = "图片链接", name = "图片链接")
    private String autoHeightThumbnail;

    @ApiModelProperty(name = "图片标题", value = "图片标题")
    private String wideThumbnail;

    @ApiModelProperty(name = "图片高度", value = "图片高度")
    private Long height;

    @ApiModelProperty(name = "图片宽度", value = "图片宽度")
    private Long width;

}
