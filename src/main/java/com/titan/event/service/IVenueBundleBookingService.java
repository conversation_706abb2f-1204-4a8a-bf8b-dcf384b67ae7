package com.titan.event.service;

import com.titan.event.entity.VenueBundleBooking;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Date;
import java.util.List;

/**
 * 捆绑订场服务接口
 * 
 * <AUTHOR>
 */
public interface IVenueBundleBookingService {
    
    /**
     * 新增捆绑订场记录
     * 
     * @param venueBundleBooking 捆绑订场信息
     * @return 操作结果
     */
    VenueBundleBooking insertVenueBundleBooking(VenueBundleBooking venueBundleBooking);
    
    /**
     * 修改捆绑订场记录
     * 
     * @param venueBundleBooking 捆绑订场信息
     * @return 操作结果
     */
    VenueBundleBooking updateVenueBundleBooking(VenueBundleBooking venueBundleBooking);
    
    /**
     * 删除捆绑订场记录
     * 
     * @param id 捆绑订场ID
     * @return 操作结果
     */
    boolean deleteVenueBundleBooking(Long id);
    
    /**
     * 根据ID查询捆绑订场记录
     * 
     * @param id 捆绑订场ID
     * @return 捆绑订场信息
     */
    VenueBundleBooking selectVenueBundleBookingById(Long id);
    
    /**
     * 根据条件查询捆绑订场列表
     * 
     * @param spec 查询条件
     * @param pageable 分页参数
     * @return 捆绑订场列表分页数据
     */
    Page<VenueBundleBooking> findVenueBundleBookings(Specification<VenueBundleBooking> spec, Pageable pageable);
    
    /**
     * 根据场馆ID查询捆绑订场记录
     * 
     * @param venueId 场馆ID
     * @return 捆绑订场记录列表
     */
    List<VenueBundleBooking> selectVenueBundleBookingsByVenueId(Long venueId);
    
    /**
     * 查询指定日期范围内的捆绑订场记录
     * 
     * @param venueId 场馆ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 捆绑订场记录列表
     */
    List<VenueBundleBooking> selectVenueBundleBookingsByDateRange(Long venueId, Date startDate, Date endDate);
    
    /**
     * 根据场地ID查询相关的捆绑订场记录
     * 
     * @param siteId 场地ID
     * @return 捆绑订场记录列表
     */
    List<VenueBundleBooking> selectVenueBundleBookingsBySiteId(Long siteId);
} 