package com.titan.event.service;

import com.titan.event.entity.VenueSpecialDatePrice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 特殊日期价格表 服务层
 * 
 * <AUTHOR>
 */
public interface IVenueSpecialDatePriceService {
    

} 