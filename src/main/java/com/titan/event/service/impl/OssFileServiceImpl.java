package com.titan.event.service.impl;

import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.titan.event.enums.DeleteFlag;
import com.titan.event.response.StsResponse;
import com.titan.event.service.IOssFileService;
import com.titan.event.util.CommonUtil;
import com.titan.event.util.oConvertUtils;
import com.titan.event.util.oss.OssBootUtil;
import com.titan.event.vo.UploadVO;
import com.titan.event.vo.UserInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Description: OSS云存储实现类
 * @author: jeecg-boot
 */
@Service("ossFileService")
public class OssFileServiceImpl implements IOssFileService {

	@Value("${oss.staticDomain:}")
	private String staticDomain;

	@Value("${oss.resourceDomain:}")
	private String resourceDomain;

	@Override
	public UploadVO upload(MultipartFile multipartFile) throws Exception {
		String fileName = multipartFile.getOriginalFilename();
		fileName = CommonUtil.getFileName(fileName);

		// 定义日期格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
		// 将当前日期格式化为 "年/月/日" 的格式
		String dateStr = LocalDate.now().format(formatter);
		String url = OssBootUtil.upload(multipartFile,"attach/" + dateStr);
		if(oConvertUtils.isEmpty(url)){
			throw new Exception("上传文件失败! ");
		}
		String fullUrl = OssBootUtil.getOriginalUrl(url);

		return UploadVO.builder()
				.url(fullUrl.replace(staticDomain,resourceDomain))
				.fileName(fileName)
				.build();
	}

	@Override
	public StsResponse sts() {
		AssumeRoleResponse assumeRoleResponse = OssBootUtil.getTemporaryCredentials();
		AssumeRoleResponse.Credentials credentials = assumeRoleResponse.getCredentials();
		return StsResponse.builder()
				.AccessKeyId(credentials.getAccessKeyId())
				.AccessKeySecret(credentials.getAccessKeySecret())
				.securityToken(credentials.getSecurityToken())
				.Expiration(credentials.getExpiration())
				.build();
	}

}
