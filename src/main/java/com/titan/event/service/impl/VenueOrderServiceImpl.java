package com.titan.event.service.impl;

import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.core.enums.AuthTypeEnum;
import com.ijpay.core.enums.RequestMethodEnum;
import com.ijpay.core.kit.PayKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.enums.WxDomainEnum;
import com.ijpay.wxpay.enums.v3.BasePayApiEnum;
import com.ijpay.wxpay.model.v3.*;
import com.titan.event.constant.OrderStatusConstant;
import com.titan.event.constant.RedisKeyConstant;
import com.titan.event.dto.price.SiteDatePriceDTO;
import com.titan.event.entity.Venue;
import com.titan.event.entity.VenueBooking;
import com.titan.event.entity.VenueBookingOrder;
import com.titan.event.entity.VenueOrder;
import com.titan.event.entity.VenueProduct;
import com.titan.event.entity.VenueProductOrder;
import com.titan.event.entity.VenueSite;
import com.titan.event.entity.VenueVerificationCode;
import com.titan.event.entity.VenueMemberCardUser;
import com.titan.event.entity.VenueMemberCardPrice;
import com.titan.event.entity.VenueMemberCardDetail;
import com.titan.event.entity.pay.PayOrder;
import com.titan.event.entity.pay.WxPayV3Bean;
import com.titan.event.mapper.PayOrderMapper;
import com.titan.event.mapper.VenueBookingMapper;
import com.titan.event.mapper.VenueBookingOrderMapper;
import com.titan.event.mapper.VenueMapper;
import com.titan.event.mapper.VenueOrderMapper;
import com.titan.event.mapper.VenueProductMapper;
import com.titan.event.mapper.VenueProductOrderMapper;
import com.titan.event.mapper.VenueSiteMapper;
import com.titan.event.mapper.VenueVerificationCodeMapper;
import com.titan.event.mapper.VenueMemberCardUserMapper;
import com.titan.event.mapper.VenueMemberCardPriceMapper;
import com.titan.event.mapper.VenueMemberCardDetailMapper;
import com.titan.event.redis.RedisService;
import com.titan.event.request.ProductOrderRequest;
import com.titan.event.request.VenueBookingRequest;
import com.titan.event.request.VenueOrderListRequest;
import com.titan.event.request.VenueOrderRefundRequest;
import com.titan.event.request.VenueOrderVerifyRequest;
import com.titan.event.request.VenueRefundOrderListRequest;
import com.titan.event.request.VenueOrderPayRequest;
import com.titan.event.response.CreateOrderResponse;
import com.titan.event.response.VenueOrderPayResponse;
import com.titan.event.service.ISysDictDataService;
import com.titan.event.service.IUserInfoService;
import com.titan.event.service.IVenueSiteService;
import com.titan.event.service.VenueOrderService;
import com.titan.event.util.DateTimeZoneUtil;
import com.titan.event.util.CommonUtil;
import com.titan.event.util.VerificationCodeGenerator;
import com.titan.event.util.base.Operator;
import com.titan.event.vo.BookingTimeSlotVO;
import com.titan.event.vo.PaymentInfoVO;
import com.titan.event.vo.RefundInfoVO;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;
import com.titan.event.vo.VenueOrderDetailVO;
import com.titan.event.vo.VenueBookingOrderVO;
import com.titan.event.vo.VenueProductOrderVO;
import com.titan.event.vo.VenueVerificationCodeVO;
import com.titan.event.vo.PageResultVO;
import com.titan.event.vo.VenueOrderListVO;
import com.titan.event.vo.VenueRefundOrderListVO;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.api.RDelayedQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Expression;
import java.math.BigDecimal;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 场馆订单服务实现类
 *
 * 场地预订锁定策略说明：
 * 1. 场地在预订后立即锁定（状态设为0=待支付）
 * 2. 预订记录会一直保留在数据库中，直到支付超时、取消或支付成功
 * 3. 如果用户取消或超时未支付，仅将状态更新为2=已取消，不删除记录
 * 4. 在检查场地可用性时，只会排除状态为2=已取消的记录
 * 5. 这确保场地在创建订单到最终处理之前始终处于锁定状态
 *
 * 预订状态常量说明：
 * - bookingStatus：0=待支付 1=已支付 2=已取消 3=已核销
 * - payStatus：NOTPAY=未支付 SUCCESS=支付成功 REFUND=已退款 CLOSED=已关闭
 */
@Service
@Slf4j
public class VenueOrderServiceImpl implements VenueOrderService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private VenueBookingMapper venueBookingMapper;

    @Autowired
    private VenueBookingOrderMapper venueBookingOrderMapper;

    @Autowired
    private VenueProductMapper venueProductMapper;

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Autowired
    private VenueOrderMapper venueOrderMapper;

    @Autowired
    private VenueProductOrderMapper venueProductOrderMapper;

    @Autowired
    private VenueMapper venueMapper;

    @Autowired
    private VenueMemberCardUserMapper venueMemberCardUserMapper;

    @Autowired
    private VenueMemberCardPriceMapper venueMemberCardPriceMapper;

    @Autowired
    private VenueMemberCardDetailMapper venueMemberCardDetailMapper;

    @Autowired
    private WxPayV3Bean wxPayV3Bean;

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private VenueVerificationCodeMapper venueVerificationCodeMapper;

    @Autowired
    private VerificationCodeGenerator verificationCodeGenerator;

    @Resource
    private IVenueSiteService venueSiteService;

    @Resource
    private ISysDictDataService sysDictDataService;

    @Resource
    private VenueSiteMapper venueSiteMapper;

    private static final String PRODUCT_LOCK_PREFIX = "product:stock:lock:";
    private static final String REFUND_LOCK_PREFIX = "venue:order:refund:lock:";
    private static final String PAYMENT_TIMEOUT_LOCK_PREFIX = "venue:order:payment:timeout:lock:";
    private static final String VERIFY_LOCK_PREFIX = "venue:order:verify:lock:";
    private static final int LOCK_WAIT_TIME = 10;
    private static final int LOCK_LEASE_TIME = 30;

    // 超时订单延迟队列key
    private static final String ORDER_TIMEOUT_QUEUE_KEY = "venue:order:timeout:queue";

    private String serialNo; // 证书序列号缓存

    /**
     * 创建场地预订订单
     *
     * @param request 场地预订请求
     * @return 订单创建响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createBookingOrder(VenueBookingRequest request) {
        try {
            // 生成订单号
            String orderId = generateOrderId();

            // 处理场地预订
            CreateOrderResponse response = processVenueBooking(request, orderId);

            // 生成核销码
            generateVerificationCodes(orderId);

            return response;
        } catch (Exception e) {
            log.error("创建场地预订订单失败", e);
            throw new RuntimeException("创建场地预订订单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建商品购买订单
     *
     * @param request 商品购买请求
     * @return 订单创建响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createProductOrder(ProductOrderRequest request) {
        try {
            // 获取当前用户ID
            String customerId = commonUtil.getOperatorId();
            if (customerId == null) {
                throw new RuntimeException("获取用户ID失败");
            }

            Long userId = Long.parseLong(customerId);

            // 生成订单号
            String orderId = generateOrderId();

            // 处理商品订单 - 使用原有逻辑处理
            UserInfoVO userInfoVO = userInfoService.getByUserId(userId);

            // 创建商品锁键值集合
            List<String> lockKeys = new ArrayList<>();
            List<RLock> locks = new ArrayList<>();

            try {
                // 1. 商品库存加锁，保证并发安全
                for (ProductOrderRequest.ProductOrderItem item : request.getItems()) {
                    Long productId = item.getProductId();
                    String lockKey = PRODUCT_LOCK_PREFIX + productId;
                    lockKeys.add(lockKey);

                    // 获取分布式锁
                    RLock lock = redissonClient.getLock(lockKey);
                    if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                        // 释放已获取的锁
                        releaseAllLocks(locks);
                        throw new RuntimeException("商品正在被其他用户购买，请稍后再试");
                    }
                    locks.add(lock);
                }

                // 3. 验证商品信息并计算价格
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal originalTotalAmount = BigDecimal.ZERO;
                BigDecimal discountAmount = BigDecimal.ZERO;

                List<VenueProduct> productList = new ArrayList<>();

                // 处理每个商品项
                for (ProductOrderRequest.ProductOrderItem item : request.getItems()) {
                    Long productId = item.getProductId();
                    int quantity = item.getNum();

                    // 查询商品信息
                    Optional<VenueProduct> productOpt = venueProductMapper.findById(productId);
                    if (!productOpt.isPresent()) {
                        throw new RuntimeException("商品不存在");
                    }
                    VenueProduct product = productOpt.get();
                    productList.add(product);

                    // 检查商品上架状态
                    if (product.getStatus() == null || !product.getStatus()) {
                        throw new RuntimeException("商品已下架");
                    }

                    // 检查库存
                    if (product.getStock() < quantity) {
                        throw new RuntimeException("商品库存不足");
                    }

                    // 计算商品金额
                    BigDecimal itemPrice = product.getPrice().multiply(new BigDecimal(quantity));
                    BigDecimal itemOriginalPrice = product.getOriginalPrice() != null
                            ? product.getOriginalPrice().multiply(new BigDecimal(quantity))
                            : itemPrice;

                    // 累加总金额
                    totalAmount = totalAmount.add(itemPrice);
                    originalTotalAmount = originalTotalAmount.add(itemOriginalPrice);

                    // 计算优惠金额
                    BigDecimal itemDiscount = itemOriginalPrice.subtract(itemPrice);
                    discountAmount = discountAmount.add(itemDiscount);

                    // 扣减库存
                    product.setStock(product.getStock() - quantity);
                    product.setRealSales(product.getRealSales() != null ? product.getRealSales() + quantity : quantity);
                    venueProductMapper.save(product);
                }

                // 4. 创建支付订单
                // 计算过期时间 - 商品订单30分钟过期
                String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 30);

                // 支付金额，单位分
                int amount = totalAmount.multiply(new BigDecimal("100")).intValue();

                // 创建支付描述
                String body;
                if (productList.size() == 1) {
                    body = productList.get(0).getProductName();
                } else {
                    body = productList.get(0).getProductName() + "等" + productList.size() + "件商品";
                }

                // 5. 创建商品订单详情记录
                VenueOrder venueOrder = saveProductOrderDetails(orderId, userId, request, productList, totalAmount, originalTotalAmount, discountAmount);

                // 6. 获取prepayId
                String prepayId = "";

                if (amount > 0) {
                    try {
                        // 创建支付描述
                        String description = "商品购买：" + body;

                        // 统一下单获取prepayId
                        prepayId = createWxPayOrder(description, orderId, amount, timeExpire, userInfoVO.getOpenId());
                    } catch (Exception e) {
                        log.error("微信支付统一下单失败", e);
                        throw new RuntimeException("获取prepayId失败：" + e.getMessage());
                    }
                }

                // 创建支付订单
                PayOrder payOrder = PayOrder.builder()
                        .orderId(orderId)
                        .userId(userId)
                        .openId(userInfoVO.getOpenId())
                        .amount(amount)
                        .body(body)
                        .timeExpire(timeExpire)
                        .payStatus("NOTPAY") // 未支付状态
                        .payType("WECHAT")
                        .createTime(LocalDateTime.now())
                        .prepayId(prepayId)
                        .build();

                // 保存支付订单
                payOrderMapper.save(payOrder);

                // 生成核销码
                generateVerificationCodes(orderId);

                // 7. 构建响应对象
                CreateOrderResponse response = CreateOrderResponse.builder()
                        .orderId(orderId)
                        .venueOrderId(venueOrder.getId())
                        .prepayId(prepayId)
                        .build();

                return response;

            } finally {
                // 确保锁一定被释放
                releaseAllLocks(locks);
            }
        } catch (Exception e) {
            log.error("创建商品购买订单失败", e);
            throw new RuntimeException("创建商品购买订单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存商品订单详情
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param request 商品购买请求
     * @param productList 商品列表
     * @param totalAmount 总金额
     * @param originalTotalAmount 原始总金额
     * @param discountAmount 优惠金额
     * @return 创建的VenueOrder实体
     */
    private VenueOrder saveProductOrderDetails(String orderId, Long userId, ProductOrderRequest request,
                                               List<VenueProduct> productList, BigDecimal totalAmount, BigDecimal originalTotalAmount,
                                               BigDecimal discountAmount) {

        try {
            // 创建场馆ID到场馆名称的映射，避免重复查询
            Map<Long, String> venueNameMap = new HashMap<>();

            // 1. 创建主订单记录
            VenueOrder venueOrder = VenueOrder.builder()
                    .orderId(orderId)
                    .userId(userId)
                    .orderType(OrderStatusConstant.OrderType.PRODUCT) // 商品购买类型
                    .totalAmount(totalAmount)
                    .discountAmount(discountAmount)
                    .actualAmount(totalAmount)
                    .createTime(LocalDateTime.now())
                    .build();

            // 如果只有一个商品，设置场馆信息
            if (productList.size() == 1) {
                VenueProduct product = productList.get(0);
                Long venueId = product.getVenueId();
                venueOrder.setVenueId(venueId);

                // 从数据库获取场馆名称
                String venueName = getVenueName(venueId, venueNameMap);
                venueOrder.setVenueName(venueName);
            }

            // 保存主订单
            venueOrder = venueOrderMapper.save(venueOrder);

            // 2. 创建订单详情记录
            Map<Long, VenueProduct> productMap = new HashMap<>();
            for (VenueProduct product : productList) {
                productMap.put(product.getProductId(), product);
            }

            // 处理每个商品项
            for (ProductOrderRequest.ProductOrderItem item : request.getItems()) {
                Long productId = item.getProductId();
                int quantity = item.getNum();

                VenueProduct product = productMap.get(productId);
                if (product == null) {
                    continue; // 安全检查，虽然前面已经验证过
                }

                // 计算商品小计金额
                BigDecimal price = product.getPrice();
                BigDecimal originalPrice = product.getOriginalPrice() != null ? product.getOriginalPrice() : price;
                BigDecimal subtotalAmount = price.multiply(new BigDecimal(quantity));

                // 从数据库获取场馆名称
                Long venueId = product.getVenueId();
                String venueName = getVenueName(venueId, venueNameMap);

                // 创建商品订单详情
                VenueProductOrder productOrder = VenueProductOrder.builder()
                        .orderId(orderId)
                        .productId(productId)
                        .productName(product.getProductName())
                        .productImage(product.getProductImages()) // 使用productImages字段
                        .venueId(venueId)
                        .venueName(venueName) // 使用实际场馆名称
                        .price(price)
                        .originalPrice(originalPrice)
                        .quantity(quantity)
                        .subtotalAmount(subtotalAmount)
                        .createTime(LocalDateTime.now())
                        .build();

                // 保存商品订单详情
                venueProductOrderMapper.save(productOrder);
            }

            return venueOrder;
        } catch (Exception e) {
            log.error("保存商品订单详情失败", e);
            throw new RuntimeException("保存商品订单详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理场地预订类型订单
     *
     * 注意：本方法会进行以下检查确保场地不被重复预订
     * 1. 检查VenueBookingOrder表中非取消状态的预订
     * 2. 检查VenueBooking表中非取消状态的预订
     * 3. 使用分布式锁保证并发安全
     *
     * 场地在整个订单生命周期中保持锁定直到支付成功、取消或超时
     * 注意：此方法只进行场地锁定和订单创建，不创建微信支付订单
     */
    private CreateOrderResponse processVenueBooking(VenueBookingRequest request, String orderId) {
        if (request.getItems() == null || request.getItems().isEmpty()) {
            throw new IllegalArgumentException("预订信息不能为空");
        }

        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));

        log.info("开始处理场地预订订单，订单ID: {}, 用户ID: {}", orderId, customerId);

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal originalTotalAmount = BigDecimal.ZERO;
        // 场地预订没有优惠金额，固定为0
        BigDecimal discountAmount = BigDecimal.ZERO;
        List<String> lockKeys = new ArrayList<>();
        List<VenueBooking> bookings = new ArrayList<>();
        List<RLock> locks = new ArrayList<>();

        // 场地级别的锁缓存，防止重复加锁
        Map<Long, RLock> siteLocks = new HashMap<>();

        // 场馆ID和名称，用于主订单记录
        Long mainVenueId = null;
        String mainVenueName = null;
        Map<Long, String> venueNameMap = new HashMap<>();

        try {
            // 处理每个预订项
            for (VenueBookingRequest.VenueBookingItem item : request.getItems()) {
                Long siteId = item.getSiteId();
                Long sectionId = item.getSectionId();
                String dateStr = item.getDate();
                String startTime = item.getStart();
                String endTime = item.getEnd();

                log.info("处理预订项: 场地ID={}, 分区ID={}, 日期={}, 时间段={}~{}", siteId, sectionId, dateStr, startTime, endTime);

                // 日期格式转换
                LocalDate bookingDate = LocalDate.parse(dateStr);
                LocalTime startLocalTime = LocalTime.parse(startTime);
                LocalTime endLocalTime = LocalTime.parse(endTime);

                // 检查预订日期是否合法（不能是过去的日期）
                if (bookingDate.isBefore(LocalDate.now())) {
                    throw new RuntimeException("不能预订过去的日期");
                }

                // 检查预订时间是否合法（开始时间必须早于结束时间）
                if (!startLocalTime.isBefore(endLocalTime)) {
                    throw new RuntimeException("开始时间必须早于结束时间");
                }

                // 先对场地级别进行加锁
                String siteLockKey = RedisKeyConstant.VENUE_BOOKING_LOCK_PREFIX + ":" + siteId;
                log.info("尝试获取场地级别的锁: {}", siteLockKey);

                if (!siteLocks.containsKey(siteId)) {
                    RLock siteLock = redissonClient.getLock(siteLockKey);
                    if (!siteLock.tryLock(5, 10, TimeUnit.SECONDS)) {
                        // 释放已获取的锁
                        releaseAllLocks(locks);
                        releaseAllLocks(new ArrayList<>(siteLocks.values()));
                        throw new RuntimeException("该场地正在被预订，请稍后再试");
                    }
                    log.info("成功获取场地级别的锁: {}", siteLockKey);
                    siteLocks.put(siteId, siteLock);
                    locks.add(siteLock);
                }

                // 生成具体时段的锁定键
                String lockKey = String.format("%s:%s:%s:%s:%s",
                        RedisKeyConstant.VENUE_BOOKING_LOCK_PREFIX,
                        siteId, sectionId, dateStr, startTime + "-" + endTime);
                lockKeys.add(lockKey);
                log.info("尝试获取时段级别的锁: {}", lockKey);

                // 获取分布式锁 - 增加锁的持有时间
                RLock lock = redissonClient.getLock(lockKey);
                if (!lock.tryLock(5, 2, TimeUnit.SECONDS)) {
                    // 释放已获取的锁
                    releaseAllLocks(locks);
                    throw new RuntimeException("该时段已被预订，请选择其他时段");
                }
                log.info("成功获取时段级别的锁: {}", lockKey);
                locks.add(lock);

                // 检查该时段是否已被预订（数据库层面检查）
                // 1. 先检查VenueBookingOrder表
                List<VenueBookingOrder> existingBookings = new ArrayList<>();
                // 使用findAll方法并指定查询条件
                log.info("检查VenueBookingOrder表中是否有重叠预订, 场地ID: {}, 分区ID: {}, 日期: {}", siteId, sectionId, bookingDate);
                List<VenueBookingOrder> allBookingOrders = venueBookingOrderMapper.findAll((root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(cb.equal(root.get("siteId"), siteId));

                    // 处理sectionId可能为null的情况
                    if (sectionId != null) {
                        predicates.add(cb.equal(root.get("sectionId"), sectionId));
                    } else {
                        predicates.add(cb.isNull(root.get("sectionId")));
                    }

                    predicates.add(cb.equal(root.get("bookingDate"), bookingDate));

                    // 只检查非取消状态的订单（0=待支付, 1=已支付, 3=已核销）
                    predicates.add(cb.notEqual(root.get("bookingStatus"), 2)); // 2表示已取消

                    return cb.and(predicates.toArray(new Predicate[0]));
                });

                log.info("VenueBookingOrder表中找到 {} 条有效记录", allBookingOrders.size());

                if (allBookingOrders != null && !allBookingOrders.isEmpty()) {
                    for (VenueBookingOrder booking : allBookingOrders) {
                        // 检查时间是否重叠
                        if (!(booking.getEndTime().isBefore(startLocalTime) ||
                                booking.getStartTime().isAfter(endLocalTime))) {
                            existingBookings.add(booking);
                            log.info("找到重叠的VenueBookingOrder记录, ID: {}, 状态: {}, 时间: {}~{}",
                                    booking.getId(), booking.getBookingStatus(), booking.getStartTime(), booking.getEndTime());
                        }
                    }
                }

                // 2. 同时检查VenueBooking表中的未支付订单
                log.info("检查VenueBooking表中是否有重叠预订, 场地ID: {}, 分区ID: {}, 日期: {}", siteId, sectionId, dateStr);
                List<VenueBooking> existingVenueBookings = venueBookingMapper.findAll((root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(cb.equal(root.get("siteId"), siteId));
                    // 检查section_id (可能为null)
                    if (sectionId != null) {
                        predicates.add(cb.equal(root.get("sectionId"), sectionId));
                    } else {
                        predicates.add(cb.isNull(root.get("sectionId")));
                    }
                    // 使用字符串形式的日期进行比较
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    try {
                        Date parsedDate = sdf.parse(dateStr);
                        predicates.add(cb.equal(root.get("bookingDate"), parsedDate));
                    } catch (Exception e) {
                        log.error("日期格式转换失败", e);
                    }
                    // 检查有效的预订状态 - 包括待支付(0)和已支付(1)，排除已取消(2)
                    predicates.add(cb.notEqual(root.get("bookingStatus"), 2));
                    return cb.and(predicates.toArray(new Predicate[0]));
                });

                log.info("VenueBooking表中找到 {} 条记录", existingVenueBookings.size());

                // 检查VenueBooking表中是否有重叠的时间段
                if (existingVenueBookings != null && !existingVenueBookings.isEmpty()) {
                    for (VenueBooking vb : existingVenueBookings) {
                        LocalTime vbStartTime = LocalTime.parse(vb.getStartTime());
                        LocalTime vbEndTime = LocalTime.parse(vb.getEndTime());

                        log.info("检查VenueBooking记录: ID={}, 状态={}, 时间={}~{}",
                                vb.getBookingId(), vb.getBookingStatus(), vb.getStartTime(), vb.getEndTime());

                        // 检查时间是否重叠
                        if (!(vbEndTime.isBefore(startLocalTime) || vbStartTime.isAfter(endLocalTime))) {
                            // 找到重叠的时间段，抛出异常
                            log.warn("在VenueBooking表中找到重叠的预订: ID={}, 状态={}, 订单ID={}",
                                    vb.getBookingId(), vb.getBookingStatus(), vb.getOrderId());
                            releaseAllLocks(locks);
                            throw new RuntimeException("该时段已被预订，请选择其他时段");
                        }
                    }
                }

                // 3. 检查已有订单中是否有非取消状态的
                if (!existingBookings.isEmpty()) {
                    log.warn("在VenueBookingOrder表中找到重叠的有效预订记录数: {}", existingBookings.size());
                    releaseAllLocks(locks);
                    throw new RuntimeException("该时段已被预订，请选择其他时段");
                }

                // 变量初始化
                String siteName = null;
                String sectionName = null;
                Long venueId = null;

                // 通过场地价格查询服务获取场地信息
                try {
                    // 获取场地及其分区在指定日期的价格数据
                    Map<String, Object> siteInfo = venueSiteService.getSiteAndSectionsPricesByDate(siteId, dateStr);

                    if (siteInfo == null || siteInfo.isEmpty()) {
                        throw new RuntimeException("获取场地信息失败：场地ID=" + siteId);
                    }

                    // 从返回结果中提取场地基本信息
                    if (!siteInfo.containsKey("site")) {
                        throw new RuntimeException("获取场地信息失败：缺少场地基本信息");
                    }

                    @SuppressWarnings("unchecked")
                    Map<String, Object> site = (Map<String, Object>) siteInfo.get("site");
                    if (site == null) {
                        throw new RuntimeException("获取场地信息失败：场地数据为空");
                    }

                    // 获取场地名称
                    if (!site.containsKey("siteName")) {
                        throw new RuntimeException("获取场地信息失败：缺少场地名称");
                    }
                    siteName = (String) site.get("siteName");

                    // 获取场馆ID
                    if (!site.containsKey("venueId")) {
                        throw new RuntimeException("获取场地信息失败：缺少场馆ID");
                    }
                    venueId = Long.valueOf(site.get("venueId").toString());

                    // 从返回结果中获取分区名称
                    if (sectionId != null) {
                        if (!siteInfo.containsKey("sections")) {
                            throw new RuntimeException("获取场地分区信息失败：缺少分区数据");
                        }

                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> sections = (List<Map<String, Object>>) siteInfo.get("sections");
                        if (sections == null || sections.isEmpty()) {
                            throw new RuntimeException("获取场地分区信息失败：分区列表为空");
                        }

                        boolean sectionFound = false;
                        for (Map<String, Object> section : sections) {
                            if (sectionId.equals(Long.valueOf(section.getOrDefault("sectionId", 0L).toString()))) {
                                if (!section.containsKey("sectionName")) {
                                    throw new RuntimeException("获取分区信息失败：缺少分区名称，分区ID=" + sectionId);
                                }
                                sectionName = (String) section.get("sectionName");
                                sectionFound = true;
                                break;
                            }
                        }

                        if (!sectionFound) {
                            throw new RuntimeException("未找到指定的分区信息：分区ID=" + sectionId);
                        }
                    }
                } catch (Exception e) {
                    log.error("获取场地信息失败: {}", e.getMessage(), e);
                    throw new RuntimeException("获取场地信息失败: " + e.getMessage());
                }

                // 验证场馆信息
                if (venueId == null) {
                    throw new RuntimeException("无法获取场馆ID，请检查场地配置");
                }

                if (siteName == null) {
                    throw new RuntimeException("无法获取场地名称，请检查场地配置");
                }

                if (sectionId != null && sectionName == null) {
                    throw new RuntimeException("无法获取分区名称，请检查分区配置");
                }

                // 获取场馆名称
                String venueName = getVenueName(venueId, venueNameMap);
                if (venueName == null || venueName.startsWith("场馆ID:")) {
                    throw new RuntimeException("无法获取场馆名称：场馆ID=" + venueId);
                }

                // 设置主订单的场馆信息（使用第一个项目的场馆信息）
                if (mainVenueId == null) {
                    mainVenueId = venueId;
                    mainVenueName = venueName;
                }

                // 根据预订时间计算价格
                BigDecimal price = null;

                // 调用价格计算逻辑
                try {
                    // 尝试获取场地分区在指定日期的价格数据
                    Map<Integer, BigDecimal> timeslotPrices = null;

                    // 尝试获取场地时段价格
                    if (sectionId == null) {
                        // 全场价格
                        SiteDatePriceDTO priceDTO = venueSiteService.getSiteSectionPricesByDate(siteId, null, dateStr);
                        if (priceDTO == null) {
                            throw new RuntimeException("获取场地价格失败：价格数据为空");
                        }
                        timeslotPrices = priceDTO.getTimeslotPrices();
                    } else {
                        // 分区价格
                        SiteDatePriceDTO priceDTO = venueSiteService.getSiteSectionPricesByDate(siteId, sectionId, dateStr);
                        if (priceDTO == null) {
                            throw new RuntimeException("获取分区价格失败：价格数据为空");
                        }
                        timeslotPrices = priceDTO.getTimeslotPrices();
                    }

                    if (timeslotPrices == null || timeslotPrices.isEmpty()) {
                        throw new RuntimeException("该场地未设置价格，无法预订");
                    }

                    // 计算预订时段的总价
                    price = calculatePriceForTimeRange(timeslotPrices, startLocalTime, endLocalTime);

                    // 验证价格有效性
                    if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new RuntimeException("计算价格无效：价格必须大于零");
                    }

                } catch (Exception e) {
                    log.error("计算价格失败: {}", e.getMessage(), e);
                    throw new RuntimeException("计算价格失败: " + e.getMessage());
                }

                // 计算累计金额
                totalAmount = totalAmount.add(price);
                originalTotalAmount = originalTotalAmount.add(price); // 场地预订中原价等于售价

                // 创建预订记录（注意：这里还不保存到数据库，下面会统一处理）
                VenueBooking booking = new VenueBooking();
                booking.setSiteId(siteId);
                booking.setSectionId(sectionId);
                booking.setVenueId(venueId);

                // 根据实际的VenueBooking类定义调整日期和时间字段
                // 如果VenueBooking使用的是java.util.Date而不是LocalDate/LocalTime
                Date bookingDateUtil = java.sql.Date.valueOf(bookingDate);
                booking.setBookingDate(bookingDateUtil);
                booking.setStartTime(startTime); // 使用字符串格式
                booking.setEndTime(endTime); // 使用字符串格式

                booking.setBookingStatus(0); // 待支付状态
                booking.setBookingType(1); // 普通预订
                booking.setActualPrice(price);
                booking.setOriginalPrice(price); // 场地预订中原价等于售价

                // 设置创建时间，根据实际的VenueBooking类定义调整
                booking.setCreateTime(new Date());
                booking.setUserId(Long.parseLong(customerId));

                // 设置订单ID关联字段
                booking.setOrderId(orderId);

                bookings.add(booking);
            }

            // 创建场馆主订单
            VenueOrder venueOrder = VenueOrder.builder()
                    .orderId(orderId)
                    .userId(userInfoVO.getUserId())
                    .orderType(OrderStatusConstant.OrderType.BOOKING) // 场地预订类型
                    .totalAmount(totalAmount)
                    .discountAmount(discountAmount) // 场地预订没有优惠金额，固定为0
                    .actualAmount(totalAmount)
                    .createTime(LocalDateTime.now())
                    .venueId(mainVenueId) // 设置场馆ID
                    .venueName(mainVenueName) // 设置场馆名称
                    .build();

            // 保存主订单
            venueOrder = venueOrderMapper.save(venueOrder);

            // 创建预订记录和支付关联
            // 将VenueBooking对象转换为VenueBookingOrder对象并保存
            for (VenueBooking booking : bookings) {
                // 查询场馆和场地信息，补充订单中需要的数据
                String venueName = getVenueName(booking.getVenueId(), venueNameMap);

                // 获取预订日期和时间
                LocalDate bookingLocalDate = LocalDate.parse(
                        new SimpleDateFormat("yyyy-MM-dd").format(booking.getBookingDate()));
                LocalTime startLocalTime = LocalTime.parse(booking.getStartTime());
                LocalTime endLocalTime = LocalTime.parse(booking.getEndTime());

                // 计算预订时长（分钟）
                int durationMinutes = (int) java.time.Duration.between(startLocalTime, endLocalTime).toMinutes();

                // 创建预订订单关联记录
                VenueBookingOrder.VenueBookingOrderBuilder orderBuilder = VenueBookingOrder.builder()
                        .orderId(orderId)
                        // 如果没有userId方法，使用合适的替代方法
                        // .userId(booking.getUserId())
                        .siteId(booking.getSiteId())
                        .venueId(booking.getVenueId())
                        .venueName(venueName)
                        .sectionId(booking.getSectionId())
                        // 根据VenueBookingOrder的构建器调整日期和时间字段
                        .bookingDate(bookingLocalDate)
                        .startTime(startLocalTime)
                        .endTime(endLocalTime)
                        .duration(durationMinutes)
                        .price(booking.getActualPrice())
                        .originalPrice(booking.getOriginalPrice())
                        .bookingStatus(0) // 待支付状态
                        .createTime(LocalDateTime.now());

                // 从上一步查询的场地信息中获取
                // 查询场地信息
                try {
                    Map<String, Object> siteInfo = venueSiteService.getSiteAndSectionsPricesByDate(booking.getSiteId(),
                            new SimpleDateFormat("yyyy-MM-dd").format(booking.getBookingDate()));

                    if (siteInfo != null && !siteInfo.isEmpty() && siteInfo.containsKey("site")) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> site = (Map<String, Object>) siteInfo.get("site");
                        if (site != null) {
                            String siteName = (String) site.getOrDefault("siteName", null);
                            if (siteName != null) {
                                orderBuilder.siteName(siteName);
                            } else {
                                throw new RuntimeException("无法获取场地名称");
                            }

                            // 获取场地图片
                            if (site.containsKey("siteImageUrl")) {
                                Object siteImageObj = site.get("siteImageUrl");
                                if (siteImageObj != null) {
                                    orderBuilder.siteImage(siteImageObj.toString());
                                }
                            }
                        }
                    }

                    // 如果是分区预订，获取分区名称
                    if (booking.getSectionId() != null) {
                        if (siteInfo != null && siteInfo.containsKey("sections")) {
                            @SuppressWarnings("unchecked")
                            List<Map<String, Object>> sections = (List<Map<String, Object>>) siteInfo.get("sections");
                            if (sections != null) {
                                for (Map<String, Object> section : sections) {
                                    if (booking.getSectionId().equals(Long.valueOf(section.getOrDefault("sectionId", 0L).toString()))) {
                                        String sectionName = (String) section.getOrDefault("sectionName", null);
                                        if (sectionName != null) {
                                            orderBuilder.sectionName(sectionName);
                                        } else {
                                            throw new RuntimeException("无法获取分区名称");
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // 获取场地类型名称
                    String siteTypeName = "";
                    if (siteInfo != null && siteInfo.containsKey("site")) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> site = (Map<String, Object>) siteInfo.get("site");
                        if (site != null && site.containsKey("siteType")) {
                            Integer siteType = Integer.valueOf(site.get("siteType").toString());
                            // 通过字典服务获取场地类型名称
                            siteTypeName = sysDictDataService.selectDictLabel("venue_site_type", String.valueOf(siteType));
                        }
                    }

                    // 生成完整标题：场地类型名称 + 场地名称 + 分区名称
                    String siteName = orderBuilder.build().getSiteName();
                    String sectionName = orderBuilder.build().getSectionName() != null ? orderBuilder.build().getSectionName() : "";
                    String title = siteTypeName + siteName + sectionName;

                    // 设置标题
                    orderBuilder.title(title);
                } catch (Exception e) {
                    log.error("获取场地信息失败: {}", e.getMessage(), e);
                    throw new RuntimeException("获取场地信息失败: " + e.getMessage());
                }

                // 构建并保存订单
                VenueBookingOrder bookingOrder = orderBuilder.build();
                venueBookingOrderMapper.save(bookingOrder);

                // 还可以保存VenueBooking记录，如果需要追踪更详细的预订信息
                venueBookingMapper.save(booking);
            }

            // 安排订单超时自动取消任务
            try {
                // 场地预订默认2分钟超时
                long delaySeconds = 120;

                // 安排超时取消任务
                scheduleOrderTimeoutTask(orderId, delaySeconds);
            } catch (Exception e) {
                // 仅记录日志，不影响主流程
                log.error("安排订单[{}]的超时取消任务失败：{}", orderId, e.getMessage());
            }

            // 构建并返回响应 - 只返回venue_order表的id，不返回prepayId
            return CreateOrderResponse.builder()
                    .orderId(orderId)
                    .venueOrderId(venueOrder.getId())
                    .build();
        } catch (Exception e) {
            log.error("处理场地预订失败", e);
            // 释放所有锁
            releaseAllLocks(locks);
            throw new RuntimeException("处理场地预订失败: " + e.getMessage(), e);
        } finally {
            // 确保锁一定被释放
            releaseAllLocks(locks);
        }
    }

    /**
     * 安排订单超时自动取消任务
     * 精确到订单过期时间自动触发取消操作，不依赖定时任务扫描
     *
     * @param orderId 订单ID
     * @param delaySeconds 延迟秒数
     */
    private void scheduleOrderTimeoutTask(String orderId, long delaySeconds) {
        try {
            log.info("安排订单[{}]的超时取消任务，将在{}秒后执行", orderId, delaySeconds);

            // 使用Redisson的DelayedQueue实现延迟执行
            RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(
                    redissonClient.getQueue(ORDER_TIMEOUT_QUEUE_KEY));

            // 将订单ID放入延迟队列，到期后自动进入目标队列
            delayedQueue.offer(orderId, delaySeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            // 仅记录日志，不影响主流程，超时订单会由定时任务兜底处理
            log.error("安排订单[{}]的超时取消任务失败，将依赖定时任务处理", orderId, e);
        }
    }

    /**
     * 根据时间范围计算价格
     *
     * @param timeslotPrices 时段价格映射表，键为小时（0-23），值为该小时的价格
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 该时间范围内的总价格
     * @throws RuntimeException 当价格数据无效时抛出异常
     */
    private BigDecimal calculatePriceForTimeRange(Map<Integer, BigDecimal> timeslotPrices, LocalTime startTime, LocalTime endTime) {
        if (timeslotPrices == null || timeslotPrices.isEmpty()) {
            throw new RuntimeException("价格数据为空，无法计算费用");
        }

        // 验证开始时间小于结束时间
        if (!startTime.isBefore(endTime)) {
            throw new RuntimeException("开始时间必须早于结束时间");
        }

        BigDecimal totalPrice = BigDecimal.ZERO;

        // 获取开始和结束的小时
        int startHour = startTime.getHour();
        int endHour = endTime.getHour();

        // 计算每个小时的价格总和
        for (int hour = startHour; hour < endHour; hour++) {
            BigDecimal hourPrice = timeslotPrices.get(hour);

            // 如果价格为null或负数，表示该时段不可预订
            if (hourPrice == null || hourPrice.compareTo(BigDecimal.ZERO) <= 0) {
                throw new RuntimeException("时间段 " + hour + " 点未设置有效价格或不可预订");
            }

            totalPrice = totalPrice.add(hourPrice);
        }

        // 验证总价是否有效
        if (totalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("计算的总价格无效，必须大于零");
        }

        return totalPrice;
    }

    /**
     * 生成订单ID
     */
    private String generateOrderId() {
        return "O" + DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").format(LocalDateTime.now())
                + String.format("%04d", new Random().nextInt(10000));
    }

    /**
     * 释放所有锁
     */
    private void releaseAllLocks(List<RLock> locks) {
        if (locks == null || locks.isEmpty()) {
            return;
        }

        for (RLock lock : locks) {
            try {
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.info("成功释放锁: {}", lock.getName());
                }
            } catch (Exception e) {
                log.error("释放锁失败: {}", lock.getName(), e);
                // 继续处理其他锁，不影响循环
            }
        }

        // 清空锁列表以防止重复释放
        locks.clear();
    }

    /**
     * 获取场馆名称，优先从缓存获取，不存在则查询数据库
     *
     * @param venueId 场馆ID
     * @param venueNameMap 场馆名称缓存Map
     * @return 场馆名称
     * @throws RuntimeException 当无法获取场馆名称时抛出异常
     */
    private String getVenueName(Long venueId, Map<Long, String> venueNameMap) {
        if (venueId == null) {
            throw new RuntimeException("场馆ID不能为空");
        }

        // 先从缓存获取
        if (venueNameMap.containsKey(venueId)) {
            return venueNameMap.get(venueId);
        }

        // 缓存不存在，查询数据库
        try {
            Optional<Venue> venueOpt = venueMapper.findById(venueId);

            if (!venueOpt.isPresent()) {
                throw new RuntimeException("未找到场馆信息：场馆ID=" + venueId);
            }

            String venueName = venueOpt.get().getVenueName();

            if (venueName == null || venueName.trim().isEmpty()) {
                throw new RuntimeException("场馆名称为空：场馆ID=" + venueId);
            }

            // 加入缓存
            venueNameMap.put(venueId, venueName);
            return venueName;
        } catch (Exception e) {
            log.error("获取场馆名称失败，venueId: {}", venueId, e);
            throw new RuntimeException("获取场馆名称失败：" + e.getMessage());
        }
    }

    /**
     * 创建微信支付订单并获取prepayId
     *
     * @param description 商品描述
     * @param outTradeNo 订单号
     * @param amount 支付金额（单位：分）
     * @param timeExpire 过期时间
     * @param openId 用户openId
     * @return prepayId 预支付ID
     */
    private String createWxPayOrder(String description, String outTradeNo, int amount, String timeExpire, String openId) {
        try {
            // 构建统一下单模型
            UnifiedOrderModel unifiedOrderModel = new UnifiedOrderModel()
                    .setAppid(wxPayV3Bean.getAppId())
                    .setMchid(wxPayV3Bean.getMchId())
                    .setDescription(description)
                    .setOut_trade_no(outTradeNo)
                    .setTime_expire(timeExpire)
                    .setAttach(description)
                    .setPayer(Payer.builder()
                            .openid(openId)
                            .build())
                    .setNotify_url(wxPayV3Bean.getDomain().concat("/wxPay/payNotify"))
                    .setAmount(new Amount().setTotal(amount));

            log.info("统一下单参数 {}", JSONUtil.toJsonStr(unifiedOrderModel));

            // 获取证书序列号
            if (StringUtils.isBlank(serialNo)) {
                X509Certificate certificate = PayKit.getCertificate(wxPayV3Bean.getCertPath());
                if (certificate != null) {
                    serialNo = certificate.getSerialNumber().toString(16).toUpperCase();
                }
            }

            // 发起统一下单请求
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.POST,
                    WxDomainEnum.CHINA.toString(),
                    BasePayApiEnum.JS_API_PAY.toString(),
                    wxPayV3Bean.getMchId(),
                    serialNo,
                    null,
                    wxPayV3Bean.getKeyPath(),
                    JSONUtil.toJsonStr(unifiedOrderModel),
                    AuthTypeEnum.RSA.getCode()
            );

            log.info("统一下单响应 {}", response);

            // 解析响应获取prepay_id
            JSON json = JSONUtil.parse(response.getBody());
            return (String) json.getByPath("prepay_id");

        } catch (Exception e) {
            log.error("创建微信支付订单失败", e);
            throw new RuntimeException("创建支付订单失败", e);
        }
    }

    /**
     * 场馆订单退款
     *
     * @param request 退款请求
     * @return 退款处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result refundOrder(VenueOrderRefundRequest request) {
        String orderId = request.getOrderId();
        String userId = commonUtil.getOperatorId();

        // 获取分布式锁，保证退款操作的原子性
        String lockKey = REFUND_LOCK_PREFIX + orderId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁
            if (!lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                return Result.error("订单正在处理中，请稍后再试");
            }

            // 先查询支付订单，通过支付状态判断订单状态
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                return Result.error("支付订单不存在");
            }

            // 查询订单信息 - 需要先通过orderId查找
            List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) ->
                    cb.equal(root.get("orderId"), orderId));

            if (venueOrderList.isEmpty()) {
                return Result.error("订单不存在");
            }

            VenueOrder venueOrder = venueOrderList.get(0);

            // 检查是否为当前用户的订单
            if (!userId.equals(String.valueOf(venueOrder.getUserId()))) {
                return Result.error("无法操作他人的订单");
            }

            // 检查订单状态是否为已支付状态
            if (!OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
                // 如果是未支付状态，直接取消订单
                if (OrderStatusConstant.PayStatus.NOTPAY.equals(payOrder.getPayStatus())) {
                    return cancelUnpaidOrder(venueOrder, payOrder);
                }
                return Result.error("订单状态不允许退款");
            }

            // 检查是否可以退款
            if (!isOrderRefundable(venueOrder, payOrder)) {
                return Result.error("订单不满足退款条件");
            }

            // 执行退款操作
            String refundId = processRefund(payOrder, request.getRefundReason());

            // 更新订单状态
            updateOrderRefundStatus(venueOrder, payOrder, refundId);

            // 根据订单类型恢复库存
            restoreStock(venueOrder);

            return Result.ok(refundId);
        } catch (Exception e) {
            log.error("订单退款失败，订单ID: " + orderId, e);
            return Result.error("退款失败: " + e.getMessage());
        } finally {
            // 释放锁
            unlockSafely(lock);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result refundApiOrder(VenueOrderRefundRequest request) {
        String orderId = request.getOrderId();

        // 获取分布式锁，保证退款操作的原子性
        String lockKey = REFUND_LOCK_PREFIX + orderId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁
            if (!lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                return Result.error("订单正在处理中，请稍后再试");
            }

            // 先查询支付订单，通过支付状态判断订单状态
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                return Result.error("支付订单不存在");
            }

            // 查询订单信息 - 需要先通过orderId查找
            List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) ->
                    cb.equal(root.get("orderId"), orderId));

            if (venueOrderList.isEmpty()) {
                return Result.error("订单不存在");
            }

            VenueOrder venueOrder = venueOrderList.get(0);

            // 检查订单状态是否为已支付状态
            if (!OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
                // 如果是未支付状态，直接取消订单
                if (OrderStatusConstant.PayStatus.NOTPAY.equals(payOrder.getPayStatus())) {
                    return cancelUnpaidOrder(venueOrder, payOrder);
                }
                return Result.error("订单状态不允许退款");
            }

            // 检查是否可以退款
//            if (!isOrderRefundable(venueOrder, payOrder)) {
//                return Result.error("订单不满足退款条件");
//            }

            // 执行退款操作
            String refundId = processRefund(payOrder, request.getRefundReason());

            // 更新订单状态
            updateOrderRefundStatus(venueOrder, payOrder, refundId);

            // 根据订单类型恢复库存
            restoreStock(venueOrder);

            return Result.ok(refundId);
        } catch (Exception e) {
            log.error("订单退款失败，订单ID: " + orderId, e);
            return Result.error("退款失败: " + e.getMessage());
        } finally {
            // 释放锁
            unlockSafely(lock);
        }
    }

    /**
     * 安全释放锁
     */
    private void unlockSafely(RLock lock) {
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.error("释放锁失败", e);
            }
        }
    }

    /**
     * 取消未支付订单
     *
     * 注意：此方法仅将订单状态更新为已关闭，不会物理删除订单记录
     * 场地会继续显示在数据库中，但状态为已取消(2)，确保在查询时能正确排除
     */
    private Result cancelUnpaidOrder(VenueOrder venueOrder, PayOrder payOrder) {
        // 更新支付订单状态为已关闭
        payOrder.setPayStatus(OrderStatusConstant.PayStatus.CLOSED);
        payOrder.setUpdateTime(LocalDateTime.now());
        payOrderMapper.save(payOrder);

        // 更新主订单更新时间
        venueOrder.setUpdateTime(LocalDateTime.now());
        venueOrderMapper.save(venueOrder);

        // 更新场地预订状态为已取消，但保留记录
        restoreStock(venueOrder);

        log.info("订单[{}]已取消，场地预订记录状态已更新为已取消", venueOrder.getOrderId());

        return Result.ok("订单取消成功");
    }

    /**
     * 检查订单是否可以退款
     */
    private boolean isOrderRefundable(VenueOrder venueOrder, PayOrder payOrder) {
        // 检查订单状态
        if (!OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
            return false;
        }

        // 场地预订订单需要检查预订时间
        if (venueOrder.getOrderType() == OrderStatusConstant.OrderType.BOOKING) {
            // 获取场地预订信息 - 使用VenueBookingOrderMapper而不是VenueBookingMapper
            List<VenueBookingOrder> bookings = venueBookingOrderMapper.findByOrderId(venueOrder.getOrderId());
            if (bookings.isEmpty()) {
                return false;
            }

            // 获取时间最早的预订记录
            VenueBookingOrder earliestBooking = bookings.stream()
                    .min(Comparator.comparing(VenueBookingOrder::getBookingDate)
                            .thenComparing(VenueBookingOrder::getStartTime))
                    .orElse(bookings.get(0)); // 如果无法比较，则使用第一条记录

            // 检查预订时间是否已过（如果距离预订时间不足24小时，不允许退款）
            LocalDate bookingDate = earliestBooking.getBookingDate();
            LocalTime startTime = earliestBooking.getStartTime();

            // 构建预订时间
            LocalDateTime bookingDateTime = LocalDateTime.of(bookingDate, startTime);
            LocalDateTime now = LocalDateTime.now();
            long diffHours = java.time.Duration.between(now, bookingDateTime).toHours();

            log.info("订单[{}]最早的预订时间为[{}]，距离当前时间[{}]小时", venueOrder.getOrderId(), bookingDateTime, diffHours);

            return diffHours >= 24; // 距离预订时间超过24小时才可退款
        }

        // 商品订单需要检查是否已核销
        if (venueOrder.getOrderType() == OrderStatusConstant.OrderType.PRODUCT) {
            // 查询订单核销码状态
            List<VenueVerificationCode> codes = venueVerificationCodeMapper.findByOrderId(venueOrder.getOrderId());
            if (!codes.isEmpty()) {
                // 检查是否有已核销的核销码
                boolean hasVerified = codes.stream().anyMatch(code -> code.getVerificationStatus() == 1);
                if (hasVerified) {
                    log.info("订单[{}]已核销，不可退款", venueOrder.getOrderId());
                    return false; // 已核销的商品订单不可退款
                }
            }

            return true; // 未核销的商品订单可以退款
        }

        return false; // 默认不可退款
    }

    /**
     * 处理退款操作
     * 支持微信支付和储值卡支付的退款
     */
    private String processRefund(PayOrder payOrder, String refundReason) throws Exception {
        // 生成退款单号
        String refundId = "R" + System.currentTimeMillis();

        // 根据支付方式处理退款
        if ("WECHAT".equals(payOrder.getPayType())) {
            // 微信支付退款
            processWechatRefund(payOrder, refundId, refundReason);
        } else if ("CARD".equals(payOrder.getPayType())) {
            // 储值卡支付退款
            processCardRefund(payOrder, refundId, refundReason);
        } else {
            // 其他支付方式的退款处理
            log.warn("不支持的支付方式退款: {}", payOrder.getPayType());
            throw new RuntimeException("不支持的支付方式退款");
        }

        return refundId;
    }

    /**
     * 处理微信支付退款
     */
    private void processWechatRefund(PayOrder payOrder, String refundId, String refundReason) throws Exception {
        // 获取证书序列号
        if (StringUtils.isBlank(serialNo)) {
            X509Certificate certificate = PayKit.getCertificate(wxPayV3Bean.getCertPath());
            serialNo = certificate.getSerialNumber().toString(16).toUpperCase();
        }

        // 构建退款请求参数
        RefundModel refundModel = new RefundModel()
                .setOut_refund_no(payOrder.getOrderId())
                .setTransaction_id(payOrder.getTransactionId())
                .setReason(StringUtils.isBlank(refundReason) ? "用户申请退款" : refundReason)
                .setNotify_url(wxPayV3Bean.getDomain().concat("/wxPay/refundNotify"))
                .setAmount(new RefundAmount().setRefund((int) payOrder.getAmount())
                        .setTotal((int) payOrder.getAmount()).setCurrency("CNY"));
        log.info("微信退款请求参数: {}", refundModel);

        // 实际调用微信退款接口
        IJPayHttpResponse response = WxPayApi.v3(
                RequestMethodEnum.POST,
                WxDomainEnum.CHINA.toString(),
                BasePayApiEnum.REFUND.toString(),
                wxPayV3Bean.getMchId(),
                serialNo,
                null,
                wxPayV3Bean.getKeyPath(),
                JSONUtil.toJsonStr(refundModel)
        );

        // 解析退款响应结果
        String body = response.getBody();
        log.info("微信退款响应结果: {}", body);

        if (response.getStatus() == 200) {
            JSON json = JSONUtil.parse(body);
            String status = (String) json.getByPath("status");

            // 更新退款状态，根据退款结果进行处理
            if ("SUCCESS".equals(status) || "PROCESSING".equals(status)) {
                // 退款成功或处理中，记录退款单号
                String wxRefundId = (String) json.getByPath("refund_id");
                if (StringUtils.isNotBlank(wxRefundId)) {
                    // 记录微信平台的退款单号
                    log.info("微信退款单号: {}, 退款状态: {}", wxRefundId, status);
                }

                // 更新退款时间
                payOrder.setRefundOrderTime(LocalDateTime.now());

                // 如果是异步退款，可以保存退款状态为处理中
                if ("PROCESSING".equals(status)) {
                    payOrder.setRefundStatus("PROCESSING");
                }
            } else {
                // 退款失败，记录错误信息
                log.error("微信退款失败，状态: {}, 原因: {}", status, json.getByPath("message"));
                throw new RuntimeException("退款申请失败: " + json.getByPath("message"));
            }
        } else {
            log.error("微信退款接口调用失败，HTTP状态码: {}", response.getStatus());
            throw new RuntimeException("退款接口调用失败，HTTP状态码: " + response.getStatus());
        }
    }

    /**
     * 处理储值卡支付退款
     */
    private void processCardRefund(PayOrder payOrder, String refundId, String refundReason) throws Exception {
        log.info("开始处理储值卡退款，订单ID: {}, 退款单号: {}, 退款金额: {}",
                payOrder.getOrderId(), refundId, payOrder.getAmount());

        // 查询用户储值卡信息
        if (payOrder.getUserCardId() == null) {
            throw new RuntimeException("储值卡支付订单缺少用户卡ID");
        }

        Optional<VenueMemberCardUser> cardOpt = venueMemberCardUserMapper.findById(payOrder.getUserCardId());
        if (!cardOpt.isPresent()) {
            throw new RuntimeException("储值卡不存在，无法退款");
        }

        VenueMemberCardUser userCard = cardOpt.get();
        BigDecimal refundAmount = BigDecimal.valueOf(payOrder.getAmount());

        // 验证储值卡状态
        if (!"0".equals(userCard.getDelFlag())) {
            throw new RuntimeException("储值卡已删除，无法退款");
        }

        // 计算退款后的余额
        BigDecimal currentBalance = userCard.getBalance() != null ? userCard.getBalance() : BigDecimal.ZERO;
        BigDecimal newBalance = currentBalance.add(refundAmount);

        log.info("储值卡退款详情，卡号: {}, 当前余额: {}, 退款金额: {}, 退款后余额: {}",
                userCard.getCardNumber(), currentBalance, refundAmount, newBalance);

        // 更新储值卡余额
        userCard.setBalance(newBalance);
        userCard.setUpdateTime(LocalDateTime.now());
        venueMemberCardUserMapper.save(userCard);

        // 记录储值卡操作明细
        VenueMemberCardDetail cardDetail = VenueMemberCardDetail.builder()
                .userId(userCard.getUserId())
                .userName(userCard.getUserName())
                .userCardId(userCard.getUserCardId())
                .cardId(userCard.getCardId())
                .cardName(userCard.getCardName())
                .venueId(userCard.getVenueId())
                .venueName(userCard.getVenueName())
                .operationType(3) // 3表示退款
                .amount(refundAmount)
                .operationTime(LocalDateTime.now())
                .operationSource(2) // 2表示用户端
                .balance(newBalance)
                .remainingCount(userCard.getRemainingCount())
                .delFlag("0")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .remark(StringUtils.isBlank(refundReason) ? "用户申请退款" : refundReason)
                .build();

        venueMemberCardDetailMapper.save(cardDetail);

        // 更新支付订单退款状态
        payOrder.setRefundId(refundId);
        payOrder.setRefundStatus("SUCCESS");
        payOrder.setRefundOrderTime(LocalDateTime.now());

        log.info("储值卡退款完成，订单ID: {}, 退款单号: {}, 退款金额: {}, 卡余额: {} -> {}",
                payOrder.getOrderId(), refundId, refundAmount, currentBalance, newBalance);
    }

    /**
     * 更新订单退款状态
     */
    private void updateOrderRefundStatus(VenueOrder venueOrder, PayOrder payOrder, String refundId) {
        // 更新支付订单状态
        payOrder.setPayStatus(OrderStatusConstant.PayStatus.REFUND);
        payOrder.setRefundId(refundId);
        payOrder.setUpdateTime(LocalDateTime.now());
        payOrderMapper.save(payOrder);

        // 更新场馆订单更新时间
        venueOrder.setUpdateTime(LocalDateTime.now());
        venueOrderMapper.save(venueOrder);
    }

    /**
     * 根据订单类型恢复库存
     *
     * 注意：对于场地预订，仅更新状态为已取消(2)，保留记录以便锁定检查
     * 这确保了场地在整个预订过程中都能保持一致的锁定状态，包括：
     * - 已下单未支付期间
     * - 取消订单前
     * - 超时自动关闭订单前
     */
    private void restoreStock(VenueOrder venueOrder) {
        // 场地预订订单恢复场地预订时段库存
        if (venueOrder.getOrderType() == OrderStatusConstant.OrderType.BOOKING) {
            // 更新VenueBookingOrder表状态
            List<VenueBookingOrder> bookings = venueBookingOrderMapper.findByOrderId(venueOrder.getOrderId());
            if (!bookings.isEmpty()) {
                log.info("开始更新VenueBookingOrder表状态为已取消, 订单号: {}, 记录数: {}", venueOrder.getOrderId(), bookings.size());
                for (VenueBookingOrder booking : bookings) {
                    try {
                        Integer oldStatus = booking.getBookingStatus();
                        booking.setBookingStatus(2); // 2表示已取消
                        booking.setUpdateTime(LocalDateTime.now());
                        venueBookingOrderMapper.save(booking);
                        log.info("VenueBookingOrder记录状态更新成功, 记录ID: {}, 状态从 {} 更新为 2", booking.getId(), oldStatus);
                    } catch (Exception e) {
                        log.error("更新VenueBookingOrder记录状态失败, 记录ID: {}", booking.getId(), e);
                    }
                }
                log.info("VenueBookingOrder表状态更新完成, 订单号: {}", venueOrder.getOrderId());
            }

            // 同时更新VenueBooking表状态
            List<VenueBooking> venueBookings = venueBookingMapper.findByOrderId(venueOrder.getOrderId());
            if (!venueBookings.isEmpty()) {
                log.info("开始更新VenueBooking表状态为已取消, 订单号: {}, 记录数: {}", venueOrder.getOrderId(), venueBookings.size());
                for (VenueBooking booking : venueBookings) {
                    try {
                        Integer oldStatus = booking.getBookingStatus();
                        booking.setBookingStatus(2); // 2表示已取消
                        venueBookingMapper.save(booking);
                        log.info("VenueBooking记录状态更新成功, 预订ID: {}, 状态从 {} 更新为 2", booking.getBookingId(), oldStatus);
                    } catch (Exception e) {
                        log.error("更新VenueBooking记录状态失败, 预订ID: {}", booking.getBookingId(), e);
                        // 尝试重试一次
                        try {
                            VenueBooking refreshBooking = venueBookingMapper.findById(booking.getBookingId()).orElse(null);
                            if (refreshBooking != null) {
                                refreshBooking.setBookingStatus(2);
                                venueBookingMapper.save(refreshBooking);
                                log.info("VenueBooking记录状态重试更新成功, 预订ID: {}", booking.getBookingId());
                            }
                        } catch (Exception retryEx) {
                            log.error("VenueBooking记录状态重试更新失败, 预订ID: {}", booking.getBookingId(), retryEx);
                        }
                    }
                }
                log.info("VenueBooking表状态更新完成, 订单号: {}", venueOrder.getOrderId());
            } else {
                log.warn("未找到VenueBooking预订记录, 订单号: {}", venueOrder.getOrderId());
            }

            // 注意：这里故意不删除记录，仅更新状态为已取消
            // 这样可以确保在查询场地可用性时，能正确排除已取消的预订
        }

        // 商品订单恢复商品库存
        else if (venueOrder.getOrderType() == OrderStatusConstant.OrderType.PRODUCT) {
            List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderId(venueOrder.getOrderId());
            if (!productOrders.isEmpty()) {
                log.info("开始恢复商品库存, 订单号: {}, 商品数: {}", venueOrder.getOrderId(), productOrders.size());
                for (VenueProductOrder productOrder : productOrders) {
                    Optional<VenueProduct> productOpt = venueProductMapper.findById(productOrder.getProductId());
                    if (productOpt.isPresent()) {
                        VenueProduct product = productOpt.get();
                        // 记录原始库存数量
                        int oldStock = product.getStock();
                        // 恢复库存
                        product.setStock(product.getStock() + productOrder.getQuantity());
                        // 减少实际销量
                        if (product.getRealSales() != null) {
                            product.setRealSales(product.getRealSales() - productOrder.getQuantity());
                        }
                        venueProductMapper.save(product);
                        log.info("商品库存恢复成功, 商品ID: {}, 商品名称: {}, 库存从 {} 增加到 {}",
                                product.getProductId(), product.getProductName(), oldStock, product.getStock());
                    } else {
                        log.warn("商品不存在, 无法恢复库存, 商品ID: {}", productOrder.getProductId());
                    }
                }
                log.info("商品库存恢复完成, 订单号: {}", venueOrder.getOrderId());
            }
        }
    }

    /**
     * 查询订单详情
     * 支持通过订单ID或核销码查询
     *
     * @param orderId 订单ID，可为空
     * @param verificationCode 核销码，可为空
     * @param refundId 退款单ID，可为空
     * @return 订单详情，包含订单状态(1-待支付, 2-待核销, 3-已完成, 4-已作废)和使用须知等信息
     */
    @Override
    public VenueOrderDetailVO getOrderDetail(String orderId, String verificationCode, String refundId) {
        // 查询逻辑优先级：orderId > refundId > verificationCode
        String finalOrderId = orderId;

        // 如果orderId为空但refundId不为空，则通过refundId查询orderId
        if (finalOrderId == null && refundId != null && !refundId.isEmpty()) {
            // 通过refundId查询支付订单
            List<PayOrder> payOrders = payOrderMapper.findAll((root, query, cb) ->
                    cb.equal(root.get("refundId"), refundId));

            if (!payOrders.isEmpty()) {
                finalOrderId = payOrders.get(0).getOrderId();
                log.info("通过退款单ID[{}]查询到订单ID[{}]", refundId, finalOrderId);
            } else {
                log.warn("未找到退款单ID[{}]对应的订单", refundId);
                throw new RuntimeException("未找到对应的退款订单");
            }
        }

        // 如果orderId和refundId都为空，但verificationCode不为空，则通过核销码查询orderId
        if (finalOrderId == null && verificationCode != null) {
            Optional<VenueVerificationCode> codeOpt = venueVerificationCodeMapper.findByVerificationCode(verificationCode);
            if (!codeOpt.isPresent()) {
                throw new RuntimeException("核销码不存在");
            }
            finalOrderId = codeOpt.get().getOrderId();
        }

        // 如果经过上述查询后orderId仍为空，则抛出异常
        if (finalOrderId == null) {
            throw new RuntimeException("订单ID、退款单ID和核销码不能同时为空");
        }

        // 查询主订单表数据
        final String queryOrderId = finalOrderId;
        List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) ->
                cb.equal(root.get("orderId"), queryOrderId));

        if (venueOrderList.isEmpty()) {
            throw new RuntimeException("订单不存在");
        }

        VenueOrder venueOrder = venueOrderList.get(0);

        // 查询支付订单信息
        PayOrder payOrder = payOrderMapper.findByOrderId(queryOrderId);

        // 查询核销码信息
        List<VenueVerificationCode> codes = venueVerificationCodeMapper.findByOrderId(queryOrderId);
        boolean allVerified = !codes.isEmpty() && codes.stream().allMatch(code -> code.getVerificationStatus() == 1);

        // 计算实际金额（考虑venue_member_card_price表的价格）
        BigDecimal calculatedActualAmount = calculateActualAmount(venueOrder);

        // 构建订单详情VO
        VenueOrderDetailVO orderDetailVO = VenueOrderDetailVO.builder()
                .id(venueOrder.getId())
                .orderId(venueOrder.getOrderId())
                .userId(venueOrder.getUserId())
                .venueId(venueOrder.getVenueId())
                .venueName(venueOrder.getVenueName())
                .orderType(venueOrder.getOrderType())
                .contactName(venueOrder.getContactName())
                .contactPhone(venueOrder.getContactPhone())
                .totalAmount(venueOrder.getTotalAmount())
                .discountAmount(venueOrder.getDiscountAmount())
                .actualAmount(calculatedActualAmount)
                .remark(venueOrder.getRemark())
                .createTime(venueOrder.getCreateTime())
                .updateTime(venueOrder.getUpdateTime())
                .build();

        // 设置订单状态
        // 根据支付状态和核销状态确定订单状态: 1-待支付, 2-待核销, 3-已完成, 4-已作废
        int orderStatus = 1; // 默认待支付

        if (payOrder != null) {
            if (OrderStatusConstant.PayStatus.NOTPAY.equals(payOrder.getPayStatus())) {
                orderStatus = 1; // 待支付
            } else if (OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
                if (allVerified) {
                    orderStatus = 3; // 已完成
                } else {
                    orderStatus = 2; // 待核销
                }
            } else if (OrderStatusConstant.PayStatus.REFUND.equals(payOrder.getPayStatus())
                    || OrderStatusConstant.PayStatus.CLOSED.equals(payOrder.getPayStatus())) {
                orderStatus = 4; // 已作废
            }
        }
        orderDetailVO.setOrderStatus(orderStatus);

        // 设置使用须知
        // 只有商品订单才设置使用须知字段，且从商品的购买须知获取
        if (OrderStatusConstant.OrderType.PRODUCT == venueOrder.getOrderType()) {
            // 查询商品订单详情，获取商品ID
            List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderId(queryOrderId);
            if (!productOrders.isEmpty()) {
                VenueProductOrder productOrder = productOrders.get(0);
                Long productId = productOrder.getProductId();

                // 根据商品ID查询商品详情，获取购买须知
                try {
                    Optional<VenueProduct> productOpt = venueProductMapper.findById(productId);
                    if (productOpt.isPresent()) {
                        VenueProduct product = productOpt.get();
                        // 设置商品的购买须知作为订单使用须知
                        orderDetailVO.setUsageNotice(product.getPurchaseNotes());
                    }
                } catch (Exception e) {
                    log.warn("获取商品使用须知失败，商品ID: {}", productId, e);
                }
            }
        } else {
            // 对于非商品订单（如场地预订），不设置使用须知
            orderDetailVO.setUsageNotice(null);
        }

        // 如果联系人手机号为空，则从用户信息中获取
        if (StringUtils.isEmpty(orderDetailVO.getContactPhone()) && venueOrder.getUserId() != null) {
            try {
                UserInfoVO userInfo = userInfoService.getByUserId(venueOrder.getUserId());
                if (userInfo != null && StringUtils.isNotEmpty(userInfo.getPhone())) {
                    orderDetailVO.setContactPhone(userInfo.getPhone());
                }
            } catch (Exception e) {
                log.warn("获取用户手机号失败，用户ID: {}", venueOrder.getUserId(), e);
            }
        }

        // 如果场馆ID不为空，查询场馆地址信息
        if (venueOrder.getVenueId() != null) {
            try {
                Optional<Venue> venueOptional = venueMapper.findById(venueOrder.getVenueId());
                if (venueOptional.isPresent()) {
                    Venue venue = venueOptional.get();
                    orderDetailVO.setVenueAddress(venue.getVenueAddress());
                    orderDetailVO.setVenueWxCustomerServiceId(venue.getWxCustomerServiceId());
                    orderDetailVO.setVenueContactPhone(venue.getContactPhone());
                }
            } catch (Exception e) {
                log.warn("获取场馆信息失败，场馆ID: {}", venueOrder.getVenueId(), e);
            }
        }

        // 设置支付相关信息
        if (payOrder != null) {
            // 将String类型的timeExpire转换为LocalDateTime类型
            LocalDateTime timeExpire = null;
            if (payOrder.getTimeExpire() != null && !payOrder.getTimeExpire().isEmpty()) {
                timeExpire = DateTimeZoneUtil.parseTimeZone(payOrder.getTimeExpire());
            }

            // 构建支付信息VO
            PaymentInfoVO paymentInfo = PaymentInfoVO.builder()
                    .payStatus(payOrder.getPayStatus())
                    .payTime(payOrder.getPayNotifyTime())
                    .transactionId(payOrder.getTransactionId())
                    .prepayId(payOrder.getPrepayId())
                    .payType(payOrder.getPayType())
                    .amount(payOrder.getAmount())
                    .body(payOrder.getBody())
                    .timeExpire(timeExpire)
                    .build();
            orderDetailVO.setPaymentInfo(paymentInfo);

            // 构建退款信息VO
            RefundInfoVO refundInfo = RefundInfoVO.builder()
                    .refundStatus(payOrder.getRefundStatus())
                    .refundId(payOrder.getRefundId())
                    .refundOrderTime(payOrder.getRefundOrderTime())
                    .refundNotifyTime(payOrder.getRefundNotifyTime())
                    .refundable(isOrderRefundable(venueOrder, payOrder))
                    .build();
            if(OrderStatusConstant.PayStatus.REFUND.equals(payOrder.getPayStatus())){
                refundInfo.setRefundStatus("SUCCESS");
            }
            orderDetailVO.setRefundInfo(refundInfo);
        }

        // 根据订单类型查询关联详情
        if (OrderStatusConstant.OrderType.BOOKING == venueOrder.getOrderType()) {
            // 场地预订订单，查询预订详情
            List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderId(queryOrderId);
            // 转换为VO列表
            List<VenueBookingOrderVO> bookingDetails = bookingOrders.stream()
                    .map(order -> VenueBookingOrderVO.builder()
                            .id(order.getId())
                            .orderId(order.getOrderId())
                            .siteId(order.getSiteId())
                            .siteName(order.getTitle())
                            .siteImage(order.getSiteImage())
                            .venueId(order.getVenueId())
                            .venueName(order.getVenueName())
                            .sectionId(order.getSectionId())
                            .sectionName(order.getSectionName())
                            .bookingDate(order.getBookingDate())
                            .startTime(order.getStartTime())
                            .endTime(order.getEndTime())
                            .price(order.getPrice())
                            .originalPrice(order.getOriginalPrice())
                            .createTime(order.getCreateTime())
                            .updateTime(order.getUpdateTime())
                            .build())
                    .collect(Collectors.toList());
            orderDetailVO.setBookingDetails(bookingDetails);
        } else if (OrderStatusConstant.OrderType.PRODUCT == venueOrder.getOrderType()) {
            // 商品订单，查询商品详情
            List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderId(queryOrderId);
            // 转换为VO列表
            List<VenueProductOrderVO> productDetails = productOrders.stream()
                    .map(order -> VenueProductOrderVO.builder()
                            .id(order.getId())
                            .orderId(order.getOrderId())
                            .productId(order.getProductId())
                            .productName(order.getProductName())
                            .productImage(order.getProductImage())
                            .venueId(order.getVenueId())
                            .venueName(order.getVenueName())
                            .price(order.getPrice())
                            .originalPrice(order.getOriginalPrice())
                            .quantity(order.getQuantity())
                            .subtotalAmount(order.getSubtotalAmount())
                            .createTime(order.getCreateTime())
                            .updateTime(order.getUpdateTime())
                            .build())
                    .collect(Collectors.toList());
            orderDetailVO.setProductDetails(productDetails);
        } else if (OrderStatusConstant.OrderType.CARD == venueOrder.getOrderType()) {
            // 卡片购买订单，无需查询额外详情，订单信息已包含在主订单中
            log.info("卡片购买订单详情查询，订单ID: {}", queryOrderId);
        }

        // 设置核销码信息
        if (!codes.isEmpty()) {
            List<VenueVerificationCodeVO> codeVOs = codes.stream()
                    .map(code -> VenueVerificationCodeVO.builder()
                            .id(code.getId())
                            .orderId(code.getOrderId())
                            .verificationCode(code.getVerificationCode())
                            .verificationStatus(code.getVerificationStatus())
                            .verificationTime(code.getVerificationTime())
                            .createTime(code.getCreateTime())
                            .build())
                    .collect(Collectors.toList());
            orderDetailVO.setVerificationCodes(codeVOs);
        }

        // 设置支付截止时间（场地预订订单创建时间+2分钟）
        if (OrderStatusConstant.OrderType.BOOKING == venueOrder.getOrderType()) {
            LocalDateTime paymentDeadline = venueOrder.getCreateTime().plusMinutes(2);
            orderDetailVO.setPaymentDeadline(paymentDeadline);
        }

        // 设置微信支付金额和储值卡支付金额
        orderDetailVO.setWechatPayAmount(calculatedActualAmount);

        // 查询用户储值卡信息并设置储值卡支付金额
        if (venueOrder.getVenueId() != null && venueOrder.getUserId() != null) {
            try {
                // 查询用户在该场馆的储值卡
                List<VenueMemberCardUser> userCards = venueMemberCardUserMapper.findByVenueIdAndUserIdAndDelFlag(
                        venueOrder.getVenueId(), venueOrder.getUserId(), "0");
                Optional<VenueMemberCardUser> storageCardOpt = userCards.stream()
                        .filter(card -> card.getCardType() == 1) // 只查找储值卡
                        .findFirst();

                if (storageCardOpt.isPresent()) {
                    VenueMemberCardUser storageCard = storageCardOpt.get();

                    // 构建用户储值卡信息
                    VenueOrderDetailVO.UserStorageCardInfo userStorageCard = VenueOrderDetailVO.UserStorageCardInfo.builder()
                            .userCardId(storageCard.getUserCardId())
                            .cardId(storageCard.getCardId())
                            .cardName(storageCard.getCardName())
                            .cardNumber(storageCard.getCardNumber())
                            .balance(storageCard.getBalance())
                            .available(storageCard.getBalance().compareTo(calculatedActualAmount) >= 0)
                            .build();
                    orderDetailVO.setUserStorageCard(userStorageCard);

                    // 计算储值卡支付金额（根据储值卡价格表）
                    BigDecimal cardPayAmount = calculateCardPayAmount(venueOrder, storageCard.getCardId());
                    orderDetailVO.setCardPayAmount(cardPayAmount);
                }
            } catch (Exception e) {
                log.warn("查询用户储值卡信息失败，用户ID: {}, 场馆ID: {}", venueOrder.getUserId(), venueOrder.getVenueId(), e);
            }
        }

        return orderDetailVO;
    }

    /**
     * 计算储值卡支付金额
     * 根据venue_member_card_price表匹配储值卡价格，如果价格是-1或没有对应时段价格则使用原价格
     *
     * @param venueOrder 订单信息
     * @param cardId 储值卡ID
     * @return 储值卡支付金额
     */
    private BigDecimal calculateCardPayAmount(VenueOrder venueOrder, Long cardId) {
        // 如果不是场地预订订单，直接返回实付金额
        if (venueOrder.getOrderType() != OrderStatusConstant.OrderType.BOOKING) {
            return venueOrder.getActualAmount();
        }

        try {
            // 查询场地预订详情
            List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderId(venueOrder.getOrderId());
            if (bookingOrders.isEmpty()) {
                return venueOrder.getActualAmount();
            }

            BigDecimal totalCardAmount = BigDecimal.ZERO;

            // 遍历每个预订项，计算储值卡价格
            for (VenueBookingOrder bookingOrder : bookingOrders) {
                BigDecimal cardPrice = getCardPriceForBooking(cardId, bookingOrder);
                if (cardPrice != null && cardPrice.compareTo(new BigDecimal("-1")) != 0) {
                    // 如果有储值卡价格且不是-1，使用储值卡价格
                    totalCardAmount = totalCardAmount.add(cardPrice);
                } else {
                    // 如果没有储值卡价格或价格是-1，使用原价格
                    totalCardAmount = totalCardAmount.add(bookingOrder.getPrice());
                }
            }

            return totalCardAmount;
        } catch (Exception e) {
            log.warn("计算储值卡支付金额失败，使用原价格", e);
            return venueOrder.getActualAmount();
        }
    }

    /**
     * 获取单个预订项的储值卡价格
     * 使用场馆关联的储值卡ID，根据场地ID、场地类型和时段查询venue_member_card_price表
     *
     * @param cardId 场馆关联的储值卡ID（venue_member_card表的cardId）
     * @param bookingOrder 预订订单
     * @return 储值卡价格，如果没有则返回null
     */
    private BigDecimal getCardPriceForBooking(Long cardId, VenueBookingOrder bookingOrder) {
        try {
            // 构建时间段字符串（格式：HH:mm ~ HH:mm，注意是空格+波浪号+空格）
            String timeSlot = bookingOrder.getStartTime().toString() + " ~ " + bookingOrder.getEndTime().toString();

            // 确定场地类型：section_id为null是全场(0)，否则为半场(1)
            Integer siteSection = 0; // 默认全场
            if (bookingOrder.getSectionId() != null) {
                siteSection = 1; // 半场
            }

            log.debug("查询储值卡价格，cardId: {}, siteId: {}, timeSlot: {}, siteSection: {}, sectionId: {}",
                    cardId, bookingOrder.getSiteId(), timeSlot, siteSection, bookingOrder.getSectionId());

            // 查询venue_member_card_price表
            VenueMemberCardPrice cardPrice = venueMemberCardPriceMapper.findByCardIdAndTimeSlotAndSiteIdAndSiteSection(
                    cardId, timeSlot, bookingOrder.getSiteId(), siteSection);

            if (cardPrice != null) {
                log.debug("找到储值卡价格，cardId: {}, siteId: {}, timeSlot: {}, siteSection: {}, price: {}",
                        cardId, bookingOrder.getSiteId(), timeSlot, siteSection, cardPrice.getPrice());
                return cardPrice.getPrice();
            } else {
                log.debug("未找到储值卡价格，cardId: {}, siteId: {}, timeSlot: {}, siteSection: {}",
                        cardId, bookingOrder.getSiteId(), timeSlot, siteSection);
                return null;
            }
        } catch (Exception e) {
            log.warn("查询储值卡价格失败，cardId: {}, 场地ID: {}, 时间段: {} ~ {}, sectionId: {}",
                    cardId, bookingOrder.getSiteId(), bookingOrder.getStartTime(), bookingOrder.getEndTime(),
                    bookingOrder.getSectionId(), e);
            return null;
        }
    }

    /**
     * 生成并保存订单核销码
     *
     * @param orderId 订单ID
     * @return 生成的核销码
     */
    private String generateAndSaveVerificationCode(String orderId) {
        // 生成唯一的12位数字核销码
        String verificationCode = verificationCodeGenerator.generateVerificationCode();

        try {
            // 保存核销码信息
            VenueVerificationCode code = VenueVerificationCode.builder()
                    .orderId(orderId)
                    .verificationCode(verificationCode)
                    .verificationStatus(0) // 0-未核销
                    .createTime(LocalDateTime.now())
                    .build();

            venueVerificationCodeMapper.save(code);

            return verificationCode;
        } catch (Exception e) {
            // 捕获异常，可能是由于核销码重复造成的
            log.error("保存核销码失败，可能是核销码重复，尝试重新生成: " + e.getMessage());

            // 递归调用自身，重试生成核销码
            return generateAndSaveVerificationCode(orderId);
        }
    }

    /**
     * 为订单生成核销码
     *
     * @param orderId 订单ID
     */
    private void generateVerificationCodes(String orderId) {
        // 查询订单类型
        Optional<VenueOrder> orderOpt = venueOrderMapper.findByOrderId(orderId);
        if (orderOpt.isPresent()) {
            VenueOrder order = orderOpt.get();
            // 卡片购买订单不需要核销码
            if (order.getOrderType() == OrderStatusConstant.OrderType.CARD) {
                log.info("卡片购买订单无需生成核销码，订单ID: {}", orderId);
                return;
            }
        }

        // 为订单生成一个核销码
        generateAndSaveVerificationCode(orderId);
    }

    /**
     * 查询订单列表
     * 根据指定状态查询当前用户的订单，并按创建时间倒序排列
     *
     * @param request 查询请求，包含状态和分页参数
     * @return 订单列表分页结果
     */
    @Override
    public PageResultVO<VenueOrderListVO> getOrderList(VenueOrderListRequest request) {
        // 获取当前用户ID
        String userIdStr = commonUtil.getOperatorId();
        if (userIdStr == null) {
            throw new RuntimeException("获取用户ID失败");
        }
        Long userId = Long.parseLong(userIdStr);

        // 查询条件 - 按用户ID查询主表
        Specification<VenueOrder> spec = (root, query, cb) ->
                cb.equal(root.get("userId"), userId);

        // 设置排序字段为创建时间，降序排列
        request.setSortKey("createTime");
        request.setSortMethod("desc");

        // 使用BaseSearchRequest的getPageInfo方法获取分页和排序信息
        Pageable pageable = request.getPageInfo();

        // 执行主表查询
        Page<VenueOrder> orderPage = venueOrderMapper.findAll(spec, pageable);

        // 如果没有数据，返回空结果
        if (orderPage.isEmpty()) {
            return PageResultVO.build(
                    request.getPageNum(),
                    request.getPageSize(),
                    0L,
                    new ArrayList<>()
            );
        }

        // 获取所有订单的ID列表
        List<String> orderIds = orderPage.getContent().stream()
                .map(VenueOrder::getOrderId)
                .collect(Collectors.toList());

        // 查询这些订单对应的支付信息
        List<PayOrder> payOrders = payOrderMapper.findByOrderIdIn(orderIds);
        Map<String, PayOrder> payOrderMap = payOrders.stream()
                .collect(Collectors.toMap(PayOrder::getOrderId, po -> po, (existing, replacement) -> existing));

        // 查询这些订单对应的核销信息
        List<VenueVerificationCode> verificationCodes = venueVerificationCodeMapper.findByOrderIdIn(orderIds);
        Map<String, List<VenueVerificationCode>> verificationMap = verificationCodes.stream()
                .collect(Collectors.groupingBy(VenueVerificationCode::getOrderId));

        // 查询预订订单和商品订单的详情，用于填充标题和图片
        List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderIdIn(orderIds);
        Map<String, List<VenueBookingOrder>> bookingMap = bookingOrders.stream()
                .collect(Collectors.groupingBy(VenueBookingOrder::getOrderId));

        List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderIdIn(orderIds);
        Map<String, List<VenueProductOrder>> productMap = productOrders.stream()
                .collect(Collectors.groupingBy(VenueProductOrder::getOrderId));

        // 转换为VO列表
        List<VenueOrderListVO> voList = new ArrayList<>();

        for (VenueOrder order : orderPage.getContent()) {
            // 构建订单VO基本信息
            VenueOrderListVO vo = VenueOrderListVO.builder()
                    .id(order.getId())
                    .orderId(order.getOrderId())
                    .orderType(order.getOrderType())
                    .venueId(order.getVenueId())
                    .venueName(order.getVenueName())
                    .actualAmount(order.getActualAmount())
                    .createTime(order.getCreateTime())
                    .build();

            // 设置支付状态
            PayOrder payOrder = payOrderMap.get(order.getOrderId());
            if (payOrder != null) {
                vo.setPayStatus(payOrder.getPayStatus());
                vo.setPrepayId(payOrder.getPrepayId());

                // 将String类型的timeExpire转换为LocalDateTime类型
                String timeExpireStr = payOrder.getTimeExpire();
                if (timeExpireStr != null && !timeExpireStr.isEmpty()) {
                    LocalDateTime timeExpire = DateTimeZoneUtil.parseTimeZone(timeExpireStr);
                    vo.setTimeExpire(timeExpire);
                }
            } else {
                // 如果没有找到支付订单，默认为未支付
                vo.setPayStatus(OrderStatusConstant.PayStatus.NOTPAY);
            }

            // 设置核销状态和核销码
            List<VenueVerificationCode> codes = verificationMap.get(order.getOrderId());
            if (codes != null && !codes.isEmpty()) {
                vo.setVerificationStatus(codes.get(0).getVerificationStatus());
                vo.setVerificationCode(codes.get(0).getVerificationCode());
            } else {
                vo.setVerificationStatus(0); // 默认未核销
            }

            // 设置订单状态 - 根据支付状态和核销状态确定
            if (OrderStatusConstant.PayStatus.NOTPAY.equals(vo.getPayStatus())) {
                vo.setOrderStatus(1); // 待支付
            } else if (OrderStatusConstant.PayStatus.SUCCESS.equals(vo.getPayStatus())) {
                if (vo.getVerificationStatus() != null && vo.getVerificationStatus() == 1) {
                    vo.setOrderStatus(3); // 已完成
                } else {
                    vo.setOrderStatus(2); // 待核销
                }
            } else if (OrderStatusConstant.PayStatus.REFUND.equals(vo.getPayStatus())
                    || OrderStatusConstant.PayStatus.CLOSED.equals(vo.getPayStatus())) {
                vo.setOrderStatus(4); // 已作废
            } else {
                // 其他状态默认为待支付
                vo.setOrderStatus(1);
            }

            // 设置标题和图片
            if (OrderStatusConstant.OrderType.BOOKING == order.getOrderType()) {
                // 场地预订订单
                List<VenueBookingOrder> bookings = bookingMap.get(order.getOrderId());
                if (bookings != null && !bookings.isEmpty()) {
                    VenueBookingOrder booking = bookings.get(0);

                    // 设置图片信息
                    vo.setImage(booking.getSiteImage());

                    // 设置场地类型
                    String siteTypeName = "";
                    Long siteId = booking.getSiteId();
                    VenueSite venueSite = venueSiteMapper.selectVenueSiteBySiteId(siteId);
                    if (venueSite != null && venueSite.getSiteType() != null) {
                        Integer siteType = venueSite.getSiteType();
                        // 通过字典服务获取场地类型名称
                        siteTypeName = sysDictDataService.selectDictLabel("venue_site_type", String.valueOf(siteType));
                    }
                    vo.setSiteType(siteTypeName);

                    // 设置订单级别的标题为第一个预订记录的标题
                    vo.setTitle(booking.getTitle());

                    // 设置预订时间段列表
                    List<BookingTimeSlotVO> timeSlots = new ArrayList<>();
                    for (VenueBookingOrder bookingItem : bookings) {
                        BookingTimeSlotVO timeSlot = BookingTimeSlotVO.builder()
                                .bookingDate(bookingItem.getBookingDate())
                                .startTime(bookingItem.getStartTime())
                                .endTime(bookingItem.getEndTime())
                                .price(bookingItem.getPrice())
                                .title(bookingItem.getTitle()) // 直接使用存储的标题，不需要重新计算
                                .build();
                        timeSlots.add(timeSlot);
                    }
                    // 按照时间排序
                    timeSlots.sort(Comparator.comparing(BookingTimeSlotVO::getBookingDate)
                            .thenComparing(BookingTimeSlotVO::getStartTime));
                    vo.setTimeSlots(timeSlots);
                }
            } else if (OrderStatusConstant.OrderType.PRODUCT == order.getOrderType()) {
                // 商品订单
                List<VenueProductOrder> products = productMap.get(order.getOrderId());
                if (products != null && !products.isEmpty()) {
                    VenueProductOrder product = products.get(0);
                    if (products.size() > 1) {
                        vo.setTitle(product.getProductName() + "等" + products.size() + "件商品");
                    } else {
                        vo.setTitle(product.getProductName());
                    }
                    vo.setImage(product.getProductImage());
                }
            }

            // 根据请求中的状态过滤
            if (request.getStatus() == 0 || vo.getOrderStatus().equals(request.getStatus())) {
                voList.add(vo);
            }
        }

        // 构建并返回分页结果
        return PageResultVO.build(
                request.getPageNum(),
                request.getPageSize(),
                (long) voList.size(), // 使用过滤后的列表大小作为总数量
                voList
        );
    }

    /**
     * 处理微信支付超时订单
     * 更新订单状态并恢复场地/商品状态及相关库存
     *
     * @param orderId 订单ID
     * @return 处理结果，true表示成功处理，false表示处理失败
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentTimeout(String orderId) {
        // 获取分布式锁，避免并发处理同一个订单
        String lockKey = PAYMENT_TIMEOUT_LOCK_PREFIX + orderId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁
            if (!lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS)) {
                log.warn("订单[{}]已被其他进程处理，跳过超时处理", orderId);
                return false;
            }

            // 查询支付订单
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                log.warn("支付订单[{}]不存在，无法处理超时", orderId);
                return false;
            }

            // 判断订单是否已过期且为未支付状态
            if (!OrderStatusConstant.PayStatus.NOTPAY.equals(payOrder.getPayStatus())) {
                log.info("订单[{}]当前状态为[{}]，不需要处理超时", orderId, payOrder.getPayStatus());
                return false;
            }

            // 判断订单是否已超时
            // 检查timeExpire字段（微信支付过期时间）
            String timeExpireStr = payOrder.getTimeExpire();
            if (StringUtils.isBlank(timeExpireStr)) {
                log.warn("订单[{}]未设置支付过期时间，无法判断是否超时，跳过处理", orderId);
                return false;
            }

            try {
                // 微信支付的过期时间格式为 yyyy-MM-dd'T'HH:mm:ss+TIMEZONE
                // 使用DateTimeFormatter解析时间字符串
                DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                LocalDateTime timeExpire = LocalDateTime.parse(timeExpireStr, formatter);

                if (timeExpire.isAfter(LocalDateTime.now())) {
                    log.info("订单[{}]未超时，当前时间[{}]，过期时间[{}]",
                            orderId, LocalDateTime.now(), timeExpire);
                    return false;
                }

                log.info("订单[{}]已超时，当前时间[{}]，过期时间[{}]",
                        orderId, LocalDateTime.now(), timeExpire);
            } catch (Exception e) {
                log.error("解析订单[{}]过期时间[{}]失败，跳过处理", orderId, timeExpireStr, e);
                return false;
            }

            // 查询主订单
            List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) ->
                    cb.equal(root.get("orderId"), orderId));

            if (venueOrderList.isEmpty()) {
                log.warn("未找到订单[{}]对应的场馆订单", orderId);
                return false;
            }

            VenueOrder venueOrder = venueOrderList.get(0);

            // 调用已有的取消未支付订单方法
            cancelUnpaidOrder(venueOrder, payOrder);

            log.info("成功处理支付超时订单[{}]，订单类型[{}]，已更新状态并恢复库存",
                    orderId, venueOrder.getOrderType());
            return true;
        } catch (Exception e) {
            log.error("处理支付超时订单[{}]失败", orderId, e);
            return false;
        } finally {
            // 释放锁
            unlockSafely(lock);
        }
    }

    /**
     * 批量处理超时未支付订单
     * 可以由定时任务调用此方法
     *
     * 注意：超时处理会将订单状态更新为已关闭，预订记录状态更新为已取消(2)
     * 但不会物理删除预订记录，这样可以保持数据完整性和查询的一致性
     */
    @Transactional(rollbackFor = Exception.class)
    public int handleTimeoutOrders(int minutes) {
        int count = 0;
        try {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            String nowStr = now.format(DateTimeFormatter.ISO_DATE_TIME);

            log.info("开始检查超时订单，当前时间：{}", nowStr);

            // 查询状态为未支付且time_expire已过期的订单
            List<PayOrder> timeoutOrders = payOrderMapper.findAll((root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("payStatus"), OrderStatusConstant.PayStatus.NOTPAY));

                // 对time_expire进行处理
                // 1. 不为空
                // 2. 过期时间早于当前时间
                Expression<String> timeExpireExpr = root.get("timeExpire");
                predicates.add(cb.isNotNull(timeExpireExpr));
                predicates.add(cb.lessThan(timeExpireExpr, nowStr));

                return cb.and(predicates.toArray(new Predicate[0]));
            });

            log.info("找到{}个已过期未支付订单", timeoutOrders.size());

            // 处理每个超时订单
            for (PayOrder payOrder : timeoutOrders) {
                String orderId = payOrder.getOrderId();
                log.info("开始处理超时订单[{}]，过期时间[{}]", orderId, payOrder.getTimeExpire());
                boolean success = handlePaymentTimeout(orderId);
                if (success) {
                    count++;
                    log.info("订单[{}]超时处理成功", orderId);
                } else {
                    log.warn("订单[{}]超时处理失败", orderId);
                }
            }

            log.info("批量处理超时订单完成，共处理{}个订单", count);
        } catch (Exception e) {
            log.error("批量处理超时订单失败", e);
        }
        return count;
    }

    /**
     * 取消孤立的场地预订记录
     * 当PayOrder不存在但存在venue booking记录时，直接取消这些预订记录
     *
     * @param orderId 订单ID
     * @return 处理结果，true表示成功处理，false表示处理失败
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrphanedVenueBookings(String orderId) {
        try {
            log.info("开始取消孤立的场地预订记录，订单ID: {}", orderId);

            // 更新VenueBookingOrder表状态
            List<VenueBookingOrder> bookings = venueBookingOrderMapper.findByOrderId(orderId);
            if (!bookings.isEmpty()) {
                log.info("开始更新VenueBookingOrder表状态为已取消, 订单号: {}, 记录数: {}", orderId, bookings.size());
                for (VenueBookingOrder booking : bookings) {
                    try {
                        Integer oldStatus = booking.getBookingStatus();
                        booking.setBookingStatus(2); // 2表示已取消
                        booking.setUpdateTime(LocalDateTime.now());
                        venueBookingOrderMapper.save(booking);
                        log.info("VenueBookingOrder记录状态更新成功, 预订ID: {}, 状态从 {} 更新为 2", booking.getId(), oldStatus);
                    } catch (Exception e) {
                        log.error("更新VenueBookingOrder记录状态失败, 预订ID: {}", booking.getId(), e);
                        return false;
                    }
                }
                log.info("VenueBookingOrder表状态更新完成, 订单号: {}", orderId);
            } else {
                log.info("未找到VenueBookingOrder预订记录, 订单号: {}", orderId);
            }

            // 同时更新VenueBooking表状态
            List<VenueBooking> venueBookings = venueBookingMapper.findByOrderId(orderId);
            if (!venueBookings.isEmpty()) {
                log.info("开始更新VenueBooking表状态为已取消, 订单号: {}, 记录数: {}", orderId, venueBookings.size());
                for (VenueBooking booking : venueBookings) {
                    try {
                        Integer oldStatus = booking.getBookingStatus();
                        booking.setBookingStatus(2); // 2表示已取消
                        venueBookingMapper.save(booking);
                        log.info("VenueBooking记录状态更新成功, 预订ID: {}, 状态从 {} 更新为 2", booking.getBookingId(), oldStatus);
                    } catch (Exception e) {
                        log.error("更新VenueBooking记录状态失败, 预订ID: {}", booking.getBookingId(), e);
                        // 尝试重试一次
                        try {
                            VenueBooking refreshBooking = venueBookingMapper.findById(booking.getBookingId()).orElse(null);
                            if (refreshBooking != null) {
                                refreshBooking.setBookingStatus(2);
                                venueBookingMapper.save(refreshBooking);
                                log.info("VenueBooking记录状态重试更新成功, 预订ID: {}", booking.getBookingId());
                            }
                        } catch (Exception retryEx) {
                            log.error("VenueBooking记录状态重试更新失败, 预订ID: {}", booking.getBookingId(), retryEx);
                            return false;
                        }
                    }
                }
                log.info("VenueBooking表状态更新完成, 订单号: {}", orderId);
            } else {
                log.info("未找到VenueBooking预订记录, 订单号: {}", orderId);
            }

            log.info("成功取消孤立的场地预订记录，订单ID: {}", orderId);
            return true;

        } catch (Exception e) {
            log.error("取消孤立的场地预订记录失败，订单ID: {}", orderId, e);
            return false;
        }
    }

    /**
     * 从场地名称中提取场地类型
     * 例如"羽毛球2号场" -> "羽毛球"
     *
     * @param siteName 场地名称
     * @return 场地类型
     */
    private String extractSiteType(String siteName) {
        if (siteName == null || siteName.isEmpty()) {
            return "";
        }

        // 匹配中文字符，直到遇到数字或非中文字符
        Matcher matcher = Pattern.compile("^[\\u4e00-\\u9fa5]+").matcher(siteName);
        if (matcher.find()) {
            return matcher.group();
        }
        return siteName; // 如果无法提取，返回原始名称
    }

    /**
     * 从场地名称中提取场地位置/编号
     * 例如"羽毛球2号场" -> "2号场"
     *
     * @param siteName 场地名称
     * @return 场地位置/编号
     */
    private String extractSiteLocation(String siteName) {
        if (siteName == null || siteName.isEmpty()) {
            return "";
        }

        // 匹配数字及其后面的文字
        Matcher matcher = Pattern.compile("\\d+[\\u4e00-\\u9fa5]+").matcher(siteName);
        if (matcher.find()) {
            return matcher.group();
        }
        return ""; // 如果无法提取，返回空字符串
    }

    /**
     * 核销订单
     * 通过核销码验证并完成订单核销，记录核销人员ID
     *
     * @param request 包含核销码的请求
     * @param operatorId 操作人员ID
     * @return 核销结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result verifyOrder(VenueOrderVerifyRequest request, String operatorId) {
        // 获取核销码
        String verificationCode = request.getVerificationCode();
        if (StringUtils.isBlank(verificationCode)) {
            return Result.error("核销码不能为空");
        }

        // 通过核销码查询核销码信息
        Optional<VenueVerificationCode> codeOpt = venueVerificationCodeMapper.findByVerificationCode(verificationCode);
        if (!codeOpt.isPresent()) {
            return Result.error("核销码不存在");
        }

        VenueVerificationCode code = codeOpt.get();
        String orderId = code.getOrderId();

        // 获取分布式锁，防止并发核销
        RLock lock = redissonClient.getLock(VERIFY_LOCK_PREFIX + orderId);
        try {
            // 尝试获取锁
            boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!locked) {
                return Result.error("订单正在被处理，请稍后再试");
            }

            // 检查核销码状态
            if (code.getVerificationStatus() == 1) {
                return Result.error("该核销码已使用");
            }

            // 查询订单信息
            Optional<VenueOrder> orderOpt = venueOrderMapper.findByOrderId(orderId);
            if (!orderOpt.isPresent()) {
                return Result.error("订单不存在");
            }

            VenueOrder order = orderOpt.get();

            // 检查订单类型，卡片购买订单不需要核销
            if (order.getOrderType() == OrderStatusConstant.OrderType.CARD) {
                return Result.error("卡片购买订单无需核销");
            }

            // 查询支付订单信息
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                return Result.error("支付订单不存在");
            }

            // 检查支付状态
            if (!OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
                if (OrderStatusConstant.PayStatus.NOTPAY.equals(payOrder.getPayStatus())) {
                    return Result.error("订单未支付，无法核销");
                } else if (OrderStatusConstant.PayStatus.REFUND.equals(payOrder.getPayStatus())) {
                    return Result.error("订单已退款，无法核销");
                } else if (OrderStatusConstant.PayStatus.CLOSED.equals(payOrder.getPayStatus())) {
                    return Result.error("订单已关闭，无法核销");
                } else {
                    return Result.error("订单状态异常，无法核销");
                }
            }

            // 更新核销码状态
            code.setVerificationStatus(1);
            code.setVerificationTime(LocalDateTime.now());
            code.setVerifierId(Long.valueOf(operatorId));
            code.setUpdateTime(LocalDateTime.now());
            venueVerificationCodeMapper.save(code);

            // 更新订单和支付订单信息
            order.setUpdateTime(LocalDateTime.now());
            venueOrderMapper.save(order);

            // 根据订单类型更新子订单状态
            if (order.getOrderType() == OrderStatusConstant.OrderType.BOOKING) {
                // 更新场地预订子订单状态
                List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderId(orderId);
                for (VenueBookingOrder bookingOrder : bookingOrders) {
                    bookingOrder.setBookingStatus(3); // 已核销状态
                    bookingOrder.setUpdateTime(LocalDateTime.now());
                    venueBookingOrderMapper.save(bookingOrder);
                }
            } else if (order.getOrderType() == OrderStatusConstant.OrderType.PRODUCT) {
                // 更新商品购买子订单状态
                List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderId(orderId);
                for (VenueProductOrder productOrder : productOrders) {
                    productOrder.setUpdateTime(LocalDateTime.now());
                    venueProductOrderMapper.save(productOrder);
                }
            }

            log.info("订单核销成功，订单ID: {}, 核销码: {}, 操作人ID: {}", orderId, verificationCode, operatorId);

            return Result.ok("核销成功");
        } catch (Exception e) {
            log.error("订单核销失败，核销码: " + verificationCode, e);
            throw new RuntimeException("订单核销失败: " + e.getMessage(), e);
        } finally {
            unlockSafely(lock);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result verifyApiOrder(VenueOrderVerifyRequest request) {
        // 获取核销码
        String verificationCode = request.getVerificationCode();
        if (StringUtils.isBlank(verificationCode)) {
            return Result.error("核销码不能为空");
        }

        // 通过核销码查询核销码信息
        Optional<VenueVerificationCode> codeOpt = venueVerificationCodeMapper.findByVerificationCode(verificationCode);
        if (!codeOpt.isPresent()) {
            return Result.error("核销码不存在");
        }

        VenueVerificationCode code = codeOpt.get();
        String orderId = code.getOrderId();

        // 获取分布式锁，防止并发核销
        RLock lock = redissonClient.getLock(VERIFY_LOCK_PREFIX + orderId);
        try {
            // 尝试获取锁
            boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!locked) {
                return Result.error("订单正在被处理，请稍后再试");
            }

            // 检查核销码状态
            if (code.getVerificationStatus() == 1) {
                return Result.error("该核销码已使用");
            }

            // 查询订单信息
            Optional<VenueOrder> orderOpt = venueOrderMapper.findByOrderId(orderId);
            if (!orderOpt.isPresent()) {
                return Result.error("订单不存在");
            }

            VenueOrder order = orderOpt.get();

            // 检查订单类型，卡片购买订单不需要核销
            if (order.getOrderType() == OrderStatusConstant.OrderType.CARD) {
                return Result.error("卡片购买订单无需核销");
            }

            // 查询支付订单信息
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                return Result.error("支付订单不存在");
            }

            // 检查支付状态
            if (!OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
                if (OrderStatusConstant.PayStatus.NOTPAY.equals(payOrder.getPayStatus())) {
                    return Result.error("订单未支付，无法核销");
                } else if (OrderStatusConstant.PayStatus.REFUND.equals(payOrder.getPayStatus())) {
                    return Result.error("订单已退款，无法核销");
                } else if (OrderStatusConstant.PayStatus.CLOSED.equals(payOrder.getPayStatus())) {
                    return Result.error("订单已关闭，无法核销");
                } else {
                    return Result.error("订单状态异常，无法核销");
                }
            }

            // API核销时使用固定的核销人ID
            String operatorId = "0"; // API 系统核销

            // 更新核销码状态
            code.setVerificationStatus(1);
            code.setVerificationTime(LocalDateTime.now());
            code.setVerifierId(Long.valueOf(operatorId));
            code.setUpdateTime(LocalDateTime.now());
            venueVerificationCodeMapper.save(code);

            // 更新订单和支付订单信息
            order.setUpdateTime(LocalDateTime.now());
            venueOrderMapper.save(order);

            // 根据订单类型更新子订单状态
            if (order.getOrderType() == OrderStatusConstant.OrderType.BOOKING) {
                // 更新场地预订子订单状态
                List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderId(orderId);
                for (VenueBookingOrder bookingOrder : bookingOrders) {
                    bookingOrder.setBookingStatus(3); // 已核销状态
                    bookingOrder.setUpdateTime(LocalDateTime.now());
                    venueBookingOrderMapper.save(bookingOrder);
                }
            } else if (order.getOrderType() == OrderStatusConstant.OrderType.PRODUCT) {
                // 更新商品购买子订单状态
                List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderId(orderId);
                for (VenueProductOrder productOrder : productOrders) {
                    productOrder.setUpdateTime(LocalDateTime.now());
                    venueProductOrderMapper.save(productOrder);
                }
            }

            log.info("API订单核销成功，订单ID: {}, 核销码: {}", orderId, verificationCode);

            return Result.ok("核销成功");
        } catch (Exception e) {
            log.error("API订单核销失败，核销码: " + verificationCode, e);
            return Result.error("API订单核销失败: " + e.getMessage());
        } finally {
            unlockSafely(lock);
        }
    }

    @Override
    public PageResultVO<VenueRefundOrderListVO> getRefundOrderList(VenueRefundOrderListRequest request) {
        // 获取当前用户ID
        String userIdStr = commonUtil.getOperatorId();
        if (userIdStr == null) {
            throw new RuntimeException("获取用户ID失败");
        }
        Long userId = Long.parseLong(userIdStr);

        // 对于待审核和已作废状态，直接返回空列表
        if (request.getRefundStatus() == 3 || request.getRefundStatus() == 4) {
            return PageResultVO.build(
                    request.getPageNum(),
                    request.getPageSize(),
                    0L,
                    new ArrayList<>()
            );
        }

        // 查询条件 - 按用户ID和有退款ID的订单进行查询
        Specification<PayOrder> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户ID条件
            predicates.add(cb.equal(root.get("userId"), userId));

            // 退款ID不为空 - 只在查询全部状态时需要
            if (request.getRefundStatus() == 0) {
                predicates.add(cb.isNotNull(root.get("refundId")));
            }

            // 根据退款状态筛选
            if (request.getRefundStatus() != null && request.getRefundStatus() != 0) {
                switch (request.getRefundStatus()) {
                    case 1: // 已完成 - platform_pay_order表 pay_status=REFUND refund_status=PROCESSING
                        predicates.add(cb.equal(root.get("payStatus"), OrderStatusConstant.PayStatus.REFUND));
                        predicates.add(cb.equal(root.get("refundStatus"), "PROCESSING"));
                        break;
                    case 2: // 待退款 - platform_pay_order表 pay_status=SUCCESS refund_status=PROCESSING
                        predicates.add(cb.equal(root.get("payStatus"), OrderStatusConstant.PayStatus.SUCCESS));
                        predicates.add(cb.equal(root.get("refundStatus"), "PROCESSING"));
                        break;
                    default:
                        break;
                }
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 设置排序字段为创建时间，降序排列
        request.setSortKey("createTime");
        request.setSortMethod("desc");

        // 使用BaseSearchRequest的getPageInfo方法获取分页和排序信息
        Pageable pageable = request.getPageInfo();

        // 执行查询platform_pay_order表
        Page<PayOrder> payOrderPage = payOrderMapper.findAll(spec, pageable);

        // 如果没有数据，返回空结果
        if (payOrderPage.isEmpty()) {
            return PageResultVO.build(
                    request.getPageNum(),
                    request.getPageSize(),
                    0L,
                    new ArrayList<>()
            );
        }

        // 获取所有支付订单的订单ID列表
        List<String> orderIds = payOrderPage.getContent().stream()
                .map(PayOrder::getOrderId)
                .collect(Collectors.toList());

        // 查询这些订单对应的主订单信息
        List<VenueOrder> venueOrders = venueOrderMapper.findByOrderIdIn(orderIds);
        Map<String, VenueOrder> venueOrderMap = venueOrders.stream()
                .collect(Collectors.toMap(VenueOrder::getOrderId, vo -> vo, (existing, replacement) -> existing));

        // 查询这些订单对应的核销信息
        List<VenueVerificationCode> verificationCodes = venueVerificationCodeMapper.findByOrderIdIn(orderIds);
        Map<String, List<VenueVerificationCode>> verificationMap = verificationCodes.stream()
                .collect(Collectors.groupingBy(VenueVerificationCode::getOrderId));

        // 查询预订订单和商品订单的详情，用于填充标题和图片
        List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderIdIn(orderIds);
        Map<String, List<VenueBookingOrder>> bookingMap = bookingOrders.stream()
                .collect(Collectors.groupingBy(VenueBookingOrder::getOrderId));

        List<VenueProductOrder> productOrders = venueProductOrderMapper.findByOrderIdIn(orderIds);
        Map<String, List<VenueProductOrder>> productMap = productOrders.stream()
                .collect(Collectors.groupingBy(VenueProductOrder::getOrderId));

        // 转换为VO列表
        List<VenueRefundOrderListVO> voList = new ArrayList<>();

        for (PayOrder payOrder : payOrderPage.getContent()) {
            // 获取订单信息
            VenueOrder venueOrder = venueOrderMap.get(payOrder.getOrderId());
            if (venueOrder == null) {
                continue; // 跳过没有主订单信息的记录
            }

            // 构建退单VO基本信息
            VenueRefundOrderListVO vo = VenueRefundOrderListVO.builder()
                    .id(payOrder.getId())
                    .orderId(payOrder.getOrderId())
                    .orderType(venueOrder.getOrderType())
                    .venueId(venueOrder.getVenueId())
                    .venueName(venueOrder.getVenueName())
                    .actualAmount(venueOrder.getActualAmount())
                    .createTime(venueOrder.getCreateTime())
                    .refundId(payOrder.getRefundId())
                    .payStatus(payOrder.getPayStatus())
                    .refundStatus(payOrder.getRefundStatus())
                    .refundOrderTime(payOrder.getRefundOrderTime())
                    .refundNotifyTime(payOrder.getRefundNotifyTime())
                    .userId(payOrder.getUserId())
                    .prepayId(payOrder.getPrepayId())
                    .build();

            // 如果timeExpire不为空，转换为LocalDateTime
            String timeExpireStr = payOrder.getTimeExpire();
            if (timeExpireStr != null && !timeExpireStr.isEmpty()) {
                LocalDateTime timeExpire = DateTimeZoneUtil.parseTimeZone(timeExpireStr);
                vo.setTimeExpire(timeExpire);
            }

            // 设置核销状态和核销码
            List<VenueVerificationCode> codes = verificationMap.get(payOrder.getOrderId());
            if (codes != null && !codes.isEmpty()) {
                vo.setVerificationStatus(codes.get(0).getVerificationStatus());
                vo.setVerificationCode(codes.get(0).getVerificationCode());
            } else {
                vo.setVerificationStatus(0); // 默认未核销
            }

            // 设置订单状态 - 根据退款状态设置实际显示状态
            if (OrderStatusConstant.PayStatus.REFUND.equals(payOrder.getPayStatus())) {
                vo.setOrderStatus(4); // 已作废 - 已退款完成
            } else if (OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())
                    && "PROCESSING".equals(payOrder.getRefundStatus())) {
                vo.setOrderStatus(4); // 已作废 - 退款处理中
            } else {
                vo.setOrderStatus(4); // 其他情况也显示为已作废
            }

            // 尝试获取用户信息
            try {
                UserInfoVO userInfo = userInfoService.getByUserId(payOrder.getUserId());
                if (userInfo != null) {
                    vo.setUserName(userInfo.getNickname());
                    vo.setUserPhone(userInfo.getPhone());
                }
            } catch (Exception e) {
                log.warn("获取用户信息失败，用户ID: {}", payOrder.getUserId(), e);
            }

            // 设置标题和图片
            if (OrderStatusConstant.OrderType.BOOKING == venueOrder.getOrderType()) {
                // 场地预订订单
                List<VenueBookingOrder> bookings = bookingMap.get(venueOrder.getOrderId());
                if (bookings != null && !bookings.isEmpty()) {
                    VenueBookingOrder booking = bookings.get(0);
                    vo.setImage(booking.getSiteImage());
                    vo.setTitle(booking.getTitle());

                    // 设置场地类型
                    String siteTypeName = "";
                    Long siteId = booking.getSiteId();
                    VenueSite venueSite = venueSiteMapper.selectVenueSiteBySiteId(siteId);
                    if (venueSite != null && venueSite.getSiteType() != null) {
                        Integer siteType = venueSite.getSiteType();
                        // 通过字典服务获取场地类型名称
                        siteTypeName = sysDictDataService.selectDictLabel("venue_site_type", String.valueOf(siteType));
                    }
                    vo.setSiteType(siteTypeName);

                    // 设置预订时间段列表
                    List<BookingTimeSlotVO> timeSlots = new ArrayList<>();
                    for (VenueBookingOrder bookingItem : bookings) {
                        BookingTimeSlotVO timeSlot = BookingTimeSlotVO.builder()
                                .bookingDate(bookingItem.getBookingDate())
                                .startTime(bookingItem.getStartTime())
                                .endTime(bookingItem.getEndTime())
                                .price(bookingItem.getPrice())
                                .title(bookingItem.getTitle()) // 直接使用存储的标题，不需要重新计算
                                .build();
                        timeSlots.add(timeSlot);
                    }
                    // 按照时间排序
                    timeSlots.sort(Comparator.comparing(BookingTimeSlotVO::getBookingDate)
                            .thenComparing(BookingTimeSlotVO::getStartTime));
                    vo.setTimeSlots(timeSlots);
                }
            } else if (OrderStatusConstant.OrderType.PRODUCT == venueOrder.getOrderType()) {
                // 商品订单
                List<VenueProductOrder> products = productMap.get(venueOrder.getOrderId());
                if (products != null && !products.isEmpty()) {
                    VenueProductOrder product = products.get(0);
                    if (products.size() > 1) {
                        vo.setTitle(product.getProductName() + "等" + products.size() + "件商品");
                    } else {
                        vo.setTitle(product.getProductName());
                    }
                    vo.setImage(product.getProductImage());
                }
            }

            voList.add(vo);
        }

        // 构建并返回分页结果
        return PageResultVO.build(
                request.getPageNum(),
                request.getPageSize(),
                payOrderPage.getTotalElements(),
                voList
        );
    }

    /**
     * 订单支付
     * 支持微信支付和储值卡支付两种方式
     *
     * @param request 支付请求，包含订单ID、支付方式、储值卡ID等
     * @return 支付处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VenueOrderPayResponse payOrder(VenueOrderPayRequest request) {
        String orderId = request.getOrderId();
        Integer payType = request.getPayType();
        Long userCardId = request.getUserCardId();

        log.info("开始处理订单支付，订单ID: {}, 支付方式: {}, 储值卡ID: {}", orderId, payType, userCardId);

        // 查询订单信息
        List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) ->
                cb.equal(root.get("orderId"), orderId));

        if (venueOrderList.isEmpty()) {
            throw new RuntimeException("订单不存在");
        }

        VenueOrder venueOrder = venueOrderList.get(0);

        // 检查订单状态
        PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
        if (payOrder != null && OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
            throw new RuntimeException("订单已支付，请勿重复支付");
        }

        // 根据支付方式处理
        if (payType == 1) {
            // 微信支付
            return processWechatPay(venueOrder);
        } else if (payType == 2) {
            // 储值卡支付
            if (userCardId == null) {
                throw new RuntimeException("储值卡支付时储值卡ID不能为空");
            }
            return processCardPay(venueOrder, userCardId);
        } else {
            throw new RuntimeException("不支持的支付方式");
        }
    }

    /**
     * 处理微信支付
     *
     * @param venueOrder 订单信息
     * @return 支付响应
     */
    private VenueOrderPayResponse processWechatPay(VenueOrder venueOrder) {
        try {
            String orderId = venueOrder.getOrderId();

            // 获取用户信息
            UserInfoVO userInfoVO = userInfoService.getByUserId(venueOrder.getUserId());
            if (userInfoVO == null) {
                throw new RuntimeException("用户信息不存在");
            }

            // 计算支付金额（单位：分）
            int amount = venueOrder.getActualAmount().multiply(new BigDecimal("100")).intValue();

            // 计算过期时间
            String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 30);

            // 创建微信支付订单
            String prepayId = "";
            if (amount > 0) {
                prepayId = createWxPayOrder("场地预订", orderId, amount, timeExpire, userInfoVO.getOpenId());
            }

            // 创建或更新支付订单
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                payOrder = PayOrder.builder()
                        .orderId(orderId)
                        .userId(venueOrder.getUserId())
                        .openId(userInfoVO.getOpenId())
                        .amount(amount)
                        .body("场地预订")
                        .timeExpire(timeExpire)
                        .payStatus("NOTPAY")
                        .payType("WECHAT")
                        .createTime(LocalDateTime.now())
                        .prepayId(prepayId)
                        .build();
            } else {
                payOrder.setAmount(amount);
                payOrder.setTimeExpire(timeExpire);
                payOrder.setPayStatus("NOTPAY");
                payOrder.setPayType("WECHAT");
                payOrder.setPrepayId(prepayId);
                payOrder.setUpdateTime(LocalDateTime.now());
            }

            payOrderMapper.save(payOrder);

            return VenueOrderPayResponse.builder()
                    .orderId(orderId)
                    .payType(1)
                    .payStatus("NOTPAY")
                    .payAmount(venueOrder.getActualAmount())
                    .prepayId(prepayId)
                    .build();

        } catch (Exception e) {
            log.error("微信支付处理失败", e);
            throw new RuntimeException("微信支付处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理储值卡支付
     *
     * @param venueOrder 订单信息
     * @param userCardId 用户储值卡ID
     * @return 支付响应
     */
    private VenueOrderPayResponse processCardPay(VenueOrder venueOrder, Long userCardId) {
        try {
            String orderId = venueOrder.getOrderId();

            // 查询用户储值卡信息
            Optional<VenueMemberCardUser> cardOpt = venueMemberCardUserMapper.findById(userCardId);
            if (!cardOpt.isPresent()) {
                throw new RuntimeException("储值卡不存在");
            }

            VenueMemberCardUser userCard = cardOpt.get();

            // 验证储值卡是否属于该用户
            if (!userCard.getUserId().equals(venueOrder.getUserId())) {
                throw new RuntimeException("储值卡不属于当前用户");
            }

            // 验证储值卡是否在对应场馆
            if (!userCard.getVenueId().equals(venueOrder.getVenueId())) {
                throw new RuntimeException("储值卡只能在对应场馆使用");
            }

            // 验证储值卡类型（必须是储值卡）
            if (userCard.getCardType() != 1) {
                throw new RuntimeException("只支持储值卡支付");
            }

            // 计算储值卡支付金额
            BigDecimal cardPayAmount = calculateCardPayAmount(venueOrder, userCard.getCardId());

            // 验证余额是否充足
            if (userCard.getBalance().compareTo(cardPayAmount) < 0) {
                throw new RuntimeException("储值卡余额不足");
            }

            // 扣减余额
            BigDecimal newBalance = userCard.getBalance().subtract(cardPayAmount);
            userCard.setBalance(newBalance);
            userCard.setUpdateTime(LocalDateTime.now());
            venueMemberCardUserMapper.save(userCard);

            // 创建使用记录
            VenueMemberCardDetail cardDetail = VenueMemberCardDetail.builder()
                    .userCardId(userCardId)
                    .cardId(userCard.getCardId())
                    .cardName(userCard.getCardName())
                    .userId(userCard.getUserId())
                    .venueId(userCard.getVenueId())
                    .venueName(userCard.getVenueName())
                    .operationType(1) // 1-消费
                    .amount(cardPayAmount)
                    .operationTime(LocalDateTime.now())
                    .operationDescription("场地预订消费-订单号:" + orderId)
                    .operator("系统")
                    .operationSource(2) // 2-用户端
                    .build();
            venueMemberCardDetailMapper.save(cardDetail);

            // 创建或更新支付订单
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                payOrder = PayOrder.builder()
                        .orderId(orderId)
                        .userId(venueOrder.getUserId())
                        .amount(cardPayAmount.doubleValue())
                        .body("储值卡支付-场地预订")
                        .payStatus("SUCCESS")
                        .payType("CARD")
                        .createTime(LocalDateTime.now())
                        .payNotifyTime(LocalDateTime.now())
                        .userCardId(userCardId)
                        .build();
            } else {
                payOrder.setAmount(cardPayAmount.doubleValue());
                payOrder.setPayStatus("SUCCESS");
                payOrder.setPayType("CARD");
                payOrder.setPayNotifyTime(LocalDateTime.now());
                payOrder.setUserCardId(userCardId);
                payOrder.setUpdateTime(LocalDateTime.now());
            }

            payOrderMapper.save(payOrder);

            return VenueOrderPayResponse.builder()
                    .orderId(orderId)
                    .payType(2)
                    .payStatus("SUCCESS")
                    .payAmount(cardPayAmount)
                    .payTime(LocalDateTime.now())
                    .cardBalance(newBalance)
                    .build();

        } catch (Exception e) {
            log.error("储值卡支付处理失败", e);
            throw new RuntimeException("储值卡支付处理失败: " + e.getMessage());
        }
    }

    /**
     * 计算订单实际金额
     * 使用用户储值卡的cardId查询venue_member_card_price表，根据场地和场地类型查询对应时段的金额
     * cardId必须是这个场馆关联的储值卡ID
     *
     * @param venueOrder 订单信息
     * @return 计算后的实际金额
     */
    private BigDecimal calculateActualAmount(VenueOrder venueOrder) {
        // 如果不是场地预订订单，直接返回原实付金额
        if (venueOrder.getOrderType() != OrderStatusConstant.OrderType.BOOKING) {
            return venueOrder.getActualAmount();
        }

        try {
            // 查询场地预订详情
            List<VenueBookingOrder> bookingOrders = venueBookingOrderMapper.findByOrderId(venueOrder.getOrderId());
            if (bookingOrders.isEmpty()) {
                return venueOrder.getActualAmount();
            }

            // 查询用户在该场馆的储值卡，获取场馆关联的储值卡ID
            Long venueStorageCardId = null;
            if (venueOrder.getVenueId() != null && venueOrder.getUserId() != null) {
                List<VenueMemberCardUser> userCards = venueMemberCardUserMapper.findByVenueIdAndUserIdAndDelFlag(
                        venueOrder.getVenueId(), venueOrder.getUserId(), "0");
                Optional<VenueMemberCardUser> storageCardOpt = userCards.stream()
                        .filter(card -> card.getCardType() == 1) // 只查找储值卡
                        .findFirst();

                if (storageCardOpt.isPresent()) {
                    // 获取场馆关联的储值卡ID（这是venue_member_card表的cardId）
                    venueStorageCardId = storageCardOpt.get().getCardId();
                    log.debug("找到用户储值卡，场馆ID: {}, 储值卡ID: {}", venueOrder.getVenueId(), venueStorageCardId);
                }
            }

            // 如果找到了场馆关联的储值卡，使用cardId查询venue_member_card_price表计算金额
            if (venueStorageCardId != null) {
                BigDecimal totalCardAmount = BigDecimal.ZERO;
                boolean hasCardPriceConfig = false; // 是否有储值卡价格配置（包括-1的情况）

                // 遍历每个预订项，根据场地ID、场地类型和时段查询venue_member_card_price表
                for (VenueBookingOrder bookingOrder : bookingOrders) {
                    BigDecimal cardPrice = getCardPriceForBooking(venueStorageCardId, bookingOrder);
                    if (cardPrice != null) {
                        // 如果venue_member_card_price表中有数据
                        hasCardPriceConfig = true;
                        if (cardPrice.compareTo(new BigDecimal("-1")) == 0) {
                            // 如果价格是-1，使用原价格
                            totalCardAmount = totalCardAmount.add(bookingOrder.getPrice());
                            log.debug("储值卡价格为-1，使用原价格，场地ID: {}, 时段: {}-{}, 原价格: {}",
                                    bookingOrder.getSiteId(), bookingOrder.getStartTime(), bookingOrder.getEndTime(), bookingOrder.getPrice());
                        } else {
                            // 如果价格不是-1，使用储值卡价格
                            totalCardAmount = totalCardAmount.add(cardPrice);
                            log.debug("使用储值卡价格，场地ID: {}, 时段: {}-{}, 储值卡价格: {}",
                                    bookingOrder.getSiteId(), bookingOrder.getStartTime(), bookingOrder.getEndTime(), cardPrice);
                        }
                    } else {
                        // 如果没有储值卡价格配置，使用原价格
                        totalCardAmount = totalCardAmount.add(bookingOrder.getPrice());
                        log.debug("无储值卡价格配置，使用原价格，场地ID: {}, 时段: {}-{}, 原价格: {}",
                                bookingOrder.getSiteId(), bookingOrder.getStartTime(), bookingOrder.getEndTime(), bookingOrder.getPrice());
                    }
                }

                // 如果有任何一个时段有储值卡价格配置（包括-1），则返回计算后的总金额
                if (hasCardPriceConfig) {
                    log.info("订单 {} 使用储值卡价格计算，原金额: {}, 新金额: {}",
                            venueOrder.getOrderId(), venueOrder.getActualAmount(), totalCardAmount);
                    return totalCardAmount;
                }
            }

            // 如果没有储值卡或没有储值卡价格，返回原实付金额
            log.debug("订单 {} 使用原实付金额: {}", venueOrder.getOrderId(), venueOrder.getActualAmount());
            return venueOrder.getActualAmount();
        } catch (Exception e) {
            log.warn("计算实际金额失败，订单ID: {}, 使用原实付金额", venueOrder.getOrderId(), e);
            return venueOrder.getActualAmount();
        }
    }
}