package com.titan.event.service.impl;

import com.titan.event.entity.VenueSpecialDate;
import com.titan.event.mapper.VenueSpecialDateMapper;
import com.titan.event.service.IVenueSpecialDateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 特殊日期表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueSpecialDateServiceImpl implements IVenueSpecialDateService {

    @Resource
    private VenueSpecialDateMapper specialDateMapper;

} 