package com.titan.event.service.impl;

import com.titan.event.entity.VenueTagRel;
import com.titan.event.mapper.VenueTagRelMapper;
import com.titan.event.service.IVenueTagRelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 场馆标签关联服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueTagRelServiceImpl implements IVenueTagRelService {

    @Resource
    private VenueTagRelMapper venueTagRelMapper;

    @Override
    public VenueTagRel findRelById(Long relId) {
        Optional<VenueTagRel> optionalRel = venueTagRelMapper.findById(relId);
        return optionalRel.orElse(null);
    }

    @Override
    public VenueTagRel addRel(VenueTagRel rel) {
        // 检查是否已存在相同关联
        VenueTagRel existingRel = venueTagRelMapper.findByVenueIdAndTagId(rel.getVenueId(), rel.getTagId());
        if (existingRel != null) {
            if ("2".equals(existingRel.getDelFlag())) {
                // 如果被标记为删除，则重新激活
                existingRel.setDelFlag("0");
                existingRel.setUpdateTime(LocalDateTime.now());
                return venueTagRelMapper.save(existingRel);
            }
            // 已存在且未删除，直接返回
            return existingRel;
        }
        
        rel.setCreateTime(LocalDateTime.now());
        rel.setDelFlag("0");
        return venueTagRelMapper.save(rel);
    }

}