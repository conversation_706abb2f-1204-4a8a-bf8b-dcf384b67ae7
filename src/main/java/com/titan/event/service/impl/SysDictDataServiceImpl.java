package com.titan.event.service.impl;

import com.titan.event.entity.SysDictData;
import com.titan.event.mapper.SysDictDataMapper;
import com.titan.event.service.ISysDictDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典数据表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl implements ISysDictDataService {

    @Resource
    private SysDictDataMapper dictDataMapper;
    
    @Override
    public List<SysDictData> selectDictDataByType(String dictType) {
        return dictDataMapper.findByDictTypeOrderByDictSortAsc(dictType);
    }

    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return dictDataMapper.findDictLabelByTypeAndValue(dictType, dictValue);
    }
} 