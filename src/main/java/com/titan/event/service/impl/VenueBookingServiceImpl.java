package com.titan.event.service.impl;

import com.titan.event.entity.VenueBooking;
import com.titan.event.mapper.VenueBookingMapper;
import com.titan.event.service.IVenueBookingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预订记录表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueBookingServiceImpl implements IVenueBookingService {

    @Resource
    private VenueBookingMapper venueBookingMapper;

} 