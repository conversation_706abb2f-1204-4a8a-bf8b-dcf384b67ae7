package com.titan.event.service.impl;

import com.alibaba.fastjson.JSON;
import com.titan.event.entity.AreaCode;
import com.titan.event.entity.SysDictData;
import com.titan.event.entity.Venue;
import com.titan.event.entity.VenueProduct;
import com.titan.event.entity.VenueSite;
import com.titan.event.entity.VenueTag;
import com.titan.event.mapper.AreaCodeMapper;
import com.titan.event.mapper.SysDictDataMapper;
import com.titan.event.mapper.VenueMapper;
import com.titan.event.mapper.VenueSiteMapper;
import com.titan.event.request.venue.VenueDetailRequest;
import com.titan.event.request.venue.VenueListRequest;
import com.titan.event.response.venue.VenueDetailResponse;
import com.titan.event.response.venue.VenueListResponse;
import com.titan.event.response.venue.VenueSettingResponse;
import com.titan.event.service.ISysDictDataService;
import com.titan.event.service.IVenueMemberCardService;
import com.titan.event.service.IVenueProductService;
import com.titan.event.service.IVenueService;
import com.titan.event.service.IVenueTagService;
import com.titan.event.util.BusinessStatusUtil;
import com.titan.event.util.CommonUtil;
import com.titan.event.vo.venue.SiteTypeVO;
import com.titan.event.vo.venue.VenueCardGroupVO;
import com.titan.event.vo.venue.VenueCardVO;
import com.titan.event.vo.venue.VenueDetailVO;
import com.titan.event.vo.venue.VenueListVO;
import com.titan.event.vo.venue.VenueSettingVO;
import com.titan.event.vo.venue.WeekTimeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 场馆服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueServiceImpl implements IVenueService {

    @Resource
    private VenueMapper venueMapper;
    
    @Resource
    private IVenueTagService venueTagService;
    
    @Resource
    private IVenueProductService venueProductService;
    
    @Resource
    private AreaCodeMapper areaCodeMapper;
    
    @Resource
    private VenueSiteMapper venueSiteMapper;
    
    @Resource
    private ISysDictDataService sysDictDataService;

    @Resource
    private IVenueMemberCardService venueMemberCardService;

    @Resource
    private CommonUtil commonUtil;

    @Override
    public Venue findVenueById(Long venueId) {
        Optional<Venue> optionalVenue = venueMapper.findById(venueId);
        return optionalVenue.orElse(null);
    }

    @Override
    public Page<Venue> findVenues(Specification<Venue> spec, Pageable pageable) {
        return venueMapper.findAll(spec, pageable);
    }
    
    @Override
    public VenueSettingResponse getVenueSetting() {
        VenueSettingVO setting = new VenueSettingVO();
        
        // 获取所有地区
        List<AreaCode> areaCodes = areaCodeMapper.findAll();
        List<VenueSettingVO.AreaVO> provinces = areaCodes.stream()
                .map(area -> {
                    VenueSettingVO.AreaVO areaVO = new VenueSettingVO.AreaVO();
                    areaVO.setId(area.getId());
                    areaVO.setCode(area.getCode());
                    areaVO.setName(area.getName());
                    return areaVO;
                })
                .collect(Collectors.toList());
        setting.setProvinces(provinces);
        
        // 构建响应对象
        return VenueSettingResponse.builder()
                .provinces(provinces)
                .build();
    }
    
    @Override
    public VenueListResponse getVenueList(VenueListRequest request) {
        // 创建查询条件
        Specification<Venue> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 省份条件
            if (StringUtils.hasText(request.getProvince())) {
                predicates.add(criteriaBuilder.equal(root.get("province"), request.getProvince()));
            }
            
            // 未删除
            predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        // 创建分页和排序
        request.setSortKey("sort");
        Pageable pageable = request.getPageInfo();
        // 查询场馆列表
        Page<Venue> venuePage = findVenues(spec, pageable);
        
        // 转换为VO
        List<VenueListVO> venueList = venuePage.getContent().stream().map(venue -> {
            VenueListVO vo = new VenueListVO();
            BeanUtils.copyProperties(venue, vo);
            
            // 处理营业状态：根据businessStatusManual字段判断
            vo.setBusinessStatus(BusinessStatusUtil.getBusinessStatus(venue));
            
            // 获取标签
            List<VenueTag> tags = venueTagService.findTagsByVenueId(venue.getVenueId());
            if (tags != null && !tags.isEmpty()) {
                vo.setTags(tags.stream().map(tag -> {
                    VenueListVO.TagVO tagVO = new VenueListVO.TagVO();
                    tagVO.setTagId(tag.getTagId());
                    tagVO.setTagName(tag.getTagName());
                    return tagVO;
                }).collect(Collectors.toList()));
            }
            
            return vo;
        }).collect(Collectors.toList());
        
        // 构建响应对象
        return VenueListResponse.builder()
                .total(venuePage.getTotalElements())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .pages(venuePage.getTotalPages())
                .list(venueList)
                .build();
    }
    
    @Override
    public VenueDetailResponse getVenueDetail(VenueDetailRequest request) {
        // 查询场馆信息
        Venue venue = findVenueById(request.getVenueId());
        if (venue == null) {
            return null;
        }
        
        // 转换为VO
        VenueDetailVO vo = new VenueDetailVO();
        BeanUtils.copyProperties(venue, vo);
        
        // 处理营业状态：根据businessStatusManual字段判断
        vo.setBusinessStatus(BusinessStatusUtil.getBusinessStatus(venue));
        
        // 查询标签
        List<VenueTag> tags = venueTagService.findTagsByVenueId(venue.getVenueId());
        if (tags != null && !tags.isEmpty()) {
            vo.setTags(tags.stream().map(tag -> {
                VenueDetailVO.TagVO tagVO = new VenueDetailVO.TagVO();
                tagVO.setTagId(tag.getTagId());
                tagVO.setTagName(tag.getTagName());
                tagVO.setTagType(tag.getTagType());
                tagVO.setTagIcon(tag.getTagIcon());
                return tagVO;
            }).collect(Collectors.toList()));
        }
        
        // 查询商品
        List<VenueProduct> products = venueProductService.findOnSaleProductsByVenueId(venue.getVenueId());
        if (products != null && !products.isEmpty()) {
            vo.setProducts(products.stream().map(product -> {
                VenueDetailVO.ProductVO productVO = new VenueDetailVO.ProductVO();
                productVO.setProductId(product.getProductId());
                productVO.setProductName(product.getProductName());
                productVO.setProductCode(product.getProductCode());
                
                // 将productImages字符串转换为List<String>
                List<String> imagesList = new ArrayList<>();
                if (StringUtils.hasText(product.getProductImages())) {
                    try {
                        imagesList = JSON.parseArray(product.getProductImages(), String.class);
                    } catch (Exception e) {
                        // 解析失败，添加原始字符串作为单个图片
                        imagesList.add(product.getProductImages());
                    }
                }
                productVO.setProductImages(imagesList);
                
                // 设置封面图片(取第一张图片)
                if (!imagesList.isEmpty()) {
                    productVO.setCoverImage(imagesList.get(0));
                }
                
                productVO.setPrice(product.getPrice().toString());
                productVO.setOriginalPrice(product.getOriginalPrice() != null ? product.getOriginalPrice().toString() : null);
                // 总销量 = 真实销量 + 注水销量
                productVO.setSales(product.getRealSales() + product.getFakeSales());
                return productVO;
            }).collect(Collectors.toList()));
        }
        
        // 根据venue_id查询venue_site表中的site_type，去重
        List<Integer> siteTypes = venueSiteMapper.findByVenueIdAndDelFlag(venue.getVenueId(), "0")
                .stream()
                .map(VenueSite::getSiteType)
                .distinct()
                .collect(Collectors.toList());
        
        // 查询sys_dict_data表中dict_type为venue_site_type的数据
        if (!siteTypes.isEmpty()) {
            List<SysDictData> dictDataList = sysDictDataService.selectDictDataByType("venue_site_type");
            
            // 过滤出场馆拥有的场地类型，并按dictSort排序
            List<SiteTypeVO> siteTypeVOList = dictDataList.stream()
                    .filter(dictData -> siteTypes.contains(Integer.parseInt(dictData.getDictValue())))
                    .map(dictData -> {
                        SiteTypeVO siteTypeVO = new SiteTypeVO();
                        siteTypeVO.setDictLabel(dictData.getDictLabel());
                        siteTypeVO.setDictValue(dictData.getDictValue());
                        return siteTypeVO;
                    })
                    .collect(Collectors.toList());
            vo.setSiteTypes(siteTypeVOList.stream().sorted(Comparator.comparing(SiteTypeVO::getDictValue)).collect(Collectors.toList()));
        }
        
        // 添加最近一周的列表数据
        List<WeekTimeVO> weekTimes = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 今日
        WeekTimeVO today = new WeekTimeVO();
        today.setDayName("今日");
        today.setDate(dateFormat.format(calendar.getTime()));
        weekTimes.add(today);
        
        // 未来6天
        String[] weekDayNames = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        
        for (int i = 1; i <= 6; i++) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            WeekTimeVO weekDay = new WeekTimeVO();
            
            // 获取星期几
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            // 调整为从周一开始的索引 (1-7)
            int adjustedDayOfWeek = dayOfWeek == Calendar.SUNDAY ? 7 : dayOfWeek - 1;
            
            weekDay.setDayName(weekDayNames[adjustedDayOfWeek - 1]);
            weekDay.setDate(dateFormat.format(calendar.getTime()));
            weekTimes.add(weekDay);
        }
        
        vo.setWeekTimes(weekTimes);

        // 查询场馆卡信息（按卡类型分组）
        try {
            // 获取当前登录用户ID
            String userIdStr = commonUtil.getOperatorId();
            Long userId = null;
            if (StringUtils.hasText(userIdStr) && !"null".equals(userIdStr)) {
                try {
                    userId = Long.parseLong(userIdStr);
                } catch (NumberFormatException e) {
                    // 用户ID解析失败，设置为null
                    userId = null;
                }
            }

            // 查询场馆卡信息（按卡类型分组）
            List<VenueCardGroupVO> cardGroups = venueMemberCardService.getVenueCardsByVenueId(venue.getVenueId(), userId);
            vo.setCardGroups(cardGroups);

            // 查询储值卡列表
            List<VenueCardVO> storageCards = venueMemberCardService.getStorageCardsByVenueId(venue.getVenueId());
            vo.setStorageCards(storageCards);

            // 查询计次卡列表
            List<VenueCardVO> timesCards = venueMemberCardService.getTimesCardsByVenueId(venue.getVenueId());
            vo.setTimesCards(timesCards);

            // 查询用户储值卡信息
            VenueDetailVO.UserStorageCardVO userStorageCard = venueMemberCardService.getUserStorageCard(venue.getVenueId(), userId);
            vo.setUserStorageCard(userStorageCard);

        } catch (Exception e) {
            // 查询场馆卡失败，设置为空列表
            vo.setCardGroups(new ArrayList<>());
            vo.setStorageCards(new ArrayList<>());
            vo.setTimesCards(new ArrayList<>());
            vo.setUserStorageCard(null);
        }

        // 构建响应对象
        return VenueDetailResponse.builder()
                .venue(vo)
                .build();
    }
} 