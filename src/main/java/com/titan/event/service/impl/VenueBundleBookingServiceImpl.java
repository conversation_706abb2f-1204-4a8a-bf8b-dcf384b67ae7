package com.titan.event.service.impl;

import com.titan.event.entity.VenueBundleBooking;
import com.titan.event.mapper.VenueBundleBookingRepository;
import com.titan.event.service.IVenueBundleBookingService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 捆绑订场服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueBundleBookingServiceImpl implements IVenueBundleBookingService {
    
    @Resource
    private VenueBundleBookingRepository venueBundleBookingRepository;
    
    /**
     * 新增捆绑订场记录
     * 
     * @param venueBundleBooking 捆绑订场信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VenueBundleBooking insertVenueBundleBooking(VenueBundleBooking venueBundleBooking) {
        return venueBundleBookingRepository.save(venueBundleBooking);
    }
    
    /**
     * 修改捆绑订场记录
     * 
     * @param venueBundleBooking 捆绑订场信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VenueBundleBooking updateVenueBundleBooking(VenueBundleBooking venueBundleBooking) {
        return venueBundleBookingRepository.save(venueBundleBooking);
    }
    
    /**
     * 删除捆绑订场记录
     * 
     * @param id 捆绑订场ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVenueBundleBooking(Long id) {
        Optional<VenueBundleBooking> optional = venueBundleBookingRepository.findById(id);
        if (optional.isPresent()) {
            VenueBundleBooking venueBundleBooking = optional.get();
            venueBundleBooking.setDelFlag("2"); // 逻辑删除
            venueBundleBookingRepository.save(venueBundleBooking);
            return true;
        }
        return false;
    }
    
    /**
     * 根据ID查询捆绑订场记录
     * 
     * @param id 捆绑订场ID
     * @return 捆绑订场信息
     */
    @Override
    public VenueBundleBooking selectVenueBundleBookingById(Long id) {
        return venueBundleBookingRepository.findById(id).orElse(null);
    }
    
    /**
     * 根据条件查询捆绑订场列表
     * 
     * @param spec 查询条件
     * @param pageable 分页参数
     * @return 捆绑订场列表分页数据
     */
    @Override
    public Page<VenueBundleBooking> findVenueBundleBookings(Specification<VenueBundleBooking> spec, Pageable pageable) {
        return venueBundleBookingRepository.findAll(spec, pageable);
    }
    
    /**
     * 根据场馆ID查询捆绑订场记录
     * 
     * @param venueId 场馆ID
     * @return 捆绑订场记录列表
     */
    @Override
    public List<VenueBundleBooking> selectVenueBundleBookingsByVenueId(Long venueId) {
        return venueBundleBookingRepository.findByVenueIdAndDelFlag(venueId, "0");
    }
    
    /**
     * 查询指定日期范围内的捆绑订场记录
     * 
     * @param venueId 场馆ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 捆绑订场记录列表
     */
    @Override
    public List<VenueBundleBooking> selectVenueBundleBookingsByDateRange(Long venueId, Date startDate, Date endDate) {
        return venueBundleBookingRepository.findByVenueIdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndDelFlag(
                venueId, endDate, startDate, "0");
    }
    
    /**
     * 根据场地ID查询相关的捆绑订场记录
     * 
     * @param siteId 场地ID
     * @return 捆绑订场记录列表
     */
    @Override
    public List<VenueBundleBooking> selectVenueBundleBookingsBySiteId(Long siteId) {
        String siteIdPattern = "," + siteId + ",";
        return venueBundleBookingRepository.findBySiteIdListContainingAndDelFlag(siteIdPattern, "0");
    }
} 