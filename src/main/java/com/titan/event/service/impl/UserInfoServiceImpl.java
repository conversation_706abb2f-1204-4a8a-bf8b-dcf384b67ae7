package com.titan.event.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.titan.event.entity.*;
import com.titan.event.enums.*;
import com.titan.event.mapper.*;
import com.titan.event.redis.RedisService;
import com.titan.event.request.userinfo.*;
import com.titan.event.response.LoginResponse;
import com.titan.event.service.IUserInfoService;
import com.titan.event.util.IOSToeknUtils;
import com.titan.event.util.MD5Util;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.compression.CompressionCodecs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.titan.event.constant.SettingConstant.*;
import static com.titan.event.constant.SystemConfigConstant.*;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
@Slf4j
public class UserInfoServiceImpl implements IUserInfoService {

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Value("${oss.staticDomain}")
    private String domain;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private WxMaService wxMaService;

    @Value("${jwt.secret-key}")
    private String jwtSecretKey;

    @Override
    public Result<LoginResponse> touristsLogin(TouristsLoginRequest touristsLoginRequest) {
        UserInfoVO userInfoVO = new UserInfoVO();
        UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
        userInfoBaseRequest.setUuid(touristsLoginRequest.getUuid());
        Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
        UserInfo userInfo;
        if(!userInfoOptional.isPresent()){
            userInfo = UserInfo.builder()
                    .uuid(touristsLoginRequest.getUuid())
                    .openId("")
                    .unionId("")
                    .phone("")
                    .nickname(generateUserName())
                    .avatar(domain+DEFAULT_AVATAR)
                    .createDate(LocalDateTime.now())
                    .modifyDate(LocalDateTime.now())
                    .touristState(DefaultFlag.YES)
                    .adminState(DefaultFlag.NO)
                    .build();
            userInfoMapper.save(userInfo);
        }else{
            userInfo = userInfoOptional.get();
        }
        BeanUtils.copyProperties(userInfo ,userInfoVO);
        Date date = new Date();
        String token = Jwts.builder().setSubject(userInfoVO.getOpenId())
                .compressWith(CompressionCodecs.DEFLATE)
                .signWith(SignatureAlgorithm.HS256, jwtSecretKey)
                .setIssuedAt(date)
                .claim("uuid", userInfoVO.getUuid())
                .claim("userId", userInfoVO.getUserId())
                .claim("terminalToken", MD5Util.md5Hex(userInfoVO.getUserId()+date.getTime()+ RandomStringUtils.randomNumeric(4)))
                .setExpiration(DateUtils.addYears(date, 50))
                .compact();
        log.info("uuid[" + userInfoVO.getUuid() + "]登录，token：" + token);
        LoginResponse loginResponse = LoginResponse.builder()
                .token(token)
                .openId(userInfoVO.getOpenId())
                .uuid(userInfoVO.getUuid())
                .userInfo(userInfoVO)
                .build();
        return Result.OK(loginResponse);
    }

    @Override
    public Result<LoginResponse> wxLogin(WxLoginRequest wxLoginRequest) {
        UserInfoVO userInfoVO = new UserInfoVO();
        String sessionKey = "";
        try {
            String code = wxLoginRequest.getCode();
            wxMaService.switchover("wx4be4a1eb1149410f");
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            // 保存sessionKey，用于后续解密微信运动数据
            sessionKey = session.getSessionKey();
            UserInfoBaseRequest userInfoBaseRequest = new UserInfoBaseRequest();
            userInfoBaseRequest.setOpenId(session.getOpenid());
            Optional<UserInfo> userInfoOptional = userInfoMapper.findOne(this.build(userInfoBaseRequest));
            UserInfo userInfo;
            if(!userInfoOptional.isPresent()){
                userInfo = UserInfo.builder()
                        .uuid("")
                        .openId(session.getOpenid())
                        .unionId(session.getUnionid())
                        .nickname(generateUserName())
                        .phone("")
                        .avatar(domain+DEFAULT_AVATAR)
                        .createDate(LocalDateTime.now())
                        .modifyDate(LocalDateTime.now())
                        .touristState(DefaultFlag.NO)
                        .adminState(DefaultFlag.NO)
                        .build();
                userInfoMapper.save(userInfo);
            }else{
                userInfo = userInfoOptional.get();
            }
            BeanUtils.copyProperties(userInfo ,userInfoVO);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return Result.error("error");
        }
        Date date = new Date();
        String token = Jwts.builder().setSubject(userInfoVO.getOpenId())
                .compressWith(CompressionCodecs.DEFLATE)
                .signWith(SignatureAlgorithm.HS256, jwtSecretKey)
                .setIssuedAt(date)
                .claim("openId", userInfoVO.getOpenId())
                .claim("userId", userInfoVO.getUserId())
                .claim("terminalToken", MD5Util.md5Hex(userInfoVO.getUserId()+date.getTime()+ RandomStringUtils.randomNumeric(4)))
                .setExpiration(DateUtils.addYears(date, 50))
                .compact();
        log.info("openId[" + userInfoVO.getOpenId() + "]登录，token：" + token);
        LoginResponse loginResponse = LoginResponse.builder()
                .token(token)
                .openId(userInfoVO.getOpenId())
                .userInfo(userInfoVO)
                .sessionKey(sessionKey)
                .build();
        return Result.OK(loginResponse);
    }

    @Override
    public Result<LoginResponse> wxPhone(WxPhoneRequest wxPhoneRequest) {
        UserInfoVO userInfoVO = new UserInfoVO();
        try {
            wxMaService.switchover("wx4be4a1eb1149410f");
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(wxPhoneRequest.getCode());

            UserInfo userInfoByPhone = userInfoMapper.findByPhone(phoneNoInfo.getPurePhoneNumber());
            UserInfo userInfoByOpenId = userInfoMapper.findByOpenId(wxPhoneRequest.getOpenId());
            if(Objects.nonNull(userInfoByPhone) && !userInfoByPhone.getOpenId().equals(wxPhoneRequest.getOpenId())){
                return Result.error("该手机号已绑定其他账号");
            } else if(Objects.isNull(userInfoByOpenId)){
                return Result.error("绑定失败");
            }
            userInfoByOpenId.setPhone(phoneNoInfo.getPurePhoneNumber());
            userInfoByOpenId.setPhoneCode(phoneNoInfo.getCountryCode());
            userInfoMapper.save(userInfoByOpenId);
            BeanUtils.copyProperties(userInfoByOpenId ,userInfoVO);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        LoginResponse loginResponse = LoginResponse.builder()
                .userInfo(userInfoVO)
                .build();
        return Result.OK(loginResponse);
    }

    @Override
    public UserInfoVO getByUserId(Long userId) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if(userInfoOptional.isPresent()){
            UserInfo userInfo = userInfoOptional.get();
            UserInfoVO userInfoVO = new UserInfoVO();
            BeanUtils.copyProperties(userInfo ,userInfoVO);
            // 计算注册天数
            Duration duration = Duration.between(userInfo.getCreateDate(), LocalDateTime.now());
            long regDay = duration.toDays() + (duration.isNegative() ? 0 : 1);
            userInfoVO.setRegDay(regDay);

            return userInfoVO;
        }
        return null;
    }

    @Override
    public int updateUserInfo(Long userId, UserUpdateRequest userUpdateRequest) {
        Optional<UserInfo> userInfoOptional = userInfoMapper.findById(userId);
        if(userInfoOptional.isPresent()){
            UserInfo userInfo = userInfoOptional.get();
            if(Objects.nonNull(userUpdateRequest.getAvatar())){
                userInfo.setAvatar(userUpdateRequest.getAvatar());
            }
            if(Objects.nonNull(userUpdateRequest.getNickName())){
                userInfo.setNickname(userUpdateRequest.getNickName());
            }
            userInfoMapper.saveAndFlush(userInfo);
            return 1;
        }
        return 0;
    }

    public static String generateUserName() {
        Random random = new Random();
        String randomNumber = String.format("%06d", random.nextInt(1000000)); // 生成6位随机数字
        return "体坛用户" + randomNumber; // 拼接用户名和随机数字
    }

    private Specification<UserInfo> build(UserInfoBaseRequest userInfoBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // openId
            if(userInfoBaseRequest.getOpenId()!=null) {
                predicates.add(cbuild.equal(root.get("openId"), userInfoBaseRequest.getOpenId()));
            }
            // uuid
            if(userInfoBaseRequest.getUuid()!=null) {
                predicates.add(cbuild.equal(root.get("uuid"), userInfoBaseRequest.getUuid()));
            }
            // 手机号搜索
            if(userInfoBaseRequest.getPhone()!=null) {
                predicates.add(cbuild.equal(root.get("phone"), userInfoBaseRequest.getPhone()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

}
