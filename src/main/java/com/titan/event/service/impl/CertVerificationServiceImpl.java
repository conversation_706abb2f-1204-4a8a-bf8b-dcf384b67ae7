package com.titan.event.service.impl;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.Id2MetaVerifyRequest;
import com.aliyun.cloudauth20190307.models.Id2MetaVerifyResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.titan.event.entity.CertVerificationRecord;
import com.titan.event.mapper.CertVerificationRecordRepository;
import com.titan.event.service.ICertVerificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * 身份证二要素核验服务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class CertVerificationServiceImpl implements ICertVerificationService {

    // 替换为实际的AccessKey
    private static final String ACCESS_KEY_ID = "LTAI5tHopSd9bxRPpBsAErFa";
    private static final String ACCESS_KEY_SECRET = "******************************";
    
    // 主备服务节点
    private static final List<String> ENDPOINTS = Arrays.asList(
//        "cloudauth.cn-shanghai.aliyuncs.com",
        "cloudauth.cn-beijing.aliyuncs.com"
    );
    
    // Redis缓存前缀
    private static final String CACHE_PREFIX = "cert_verify:";
    
    // 缓存有效时间（秒）- 设置为60分钟
    private static final long CACHE_EXPIRATION_SECONDS = Duration.ofMinutes(60).getSeconds();
    
    @Resource
    private CertVerificationRecordRepository certVerificationRecordRepository;

    @Override
    public boolean certNoTwoElementVerification(String certName, String certNo) {
        return certNoTwoElementVerification(certName, certNo, null);
    }

    @Override
    public boolean certNoTwoElementVerification(String certName, String certNo, Long profileId) {
        // 先查询数据库中是否已有验证记录
        Optional<CertVerificationRecord> recordOptional = certVerificationRecordRepository.findByCertNameAndCertNo(certName, certNo);
        
        // 如果有记录，直接返回验证结果
        if (recordOptional.isPresent()) {
            CertVerificationRecord record = recordOptional.get();
            boolean result = record.getVerificationResult() == 1;
            log.info("使用数据库已有验证记录: 姓名={}, 身份证号={}*, 验证结果={}", 
                    certName, maskCertNo(certNo), result);
            
            // 如果提供了profileId，更新验证状态
            if (profileId != null) {
                updateVerifyStatus(profileId, result);
            }
            
            return result;
        }
        
        // 数据库中没有记录，调用API验证
        log.info("数据库无记录，调用API验证: 姓名={}, 身份证号={}*", 
                certName, maskCertNo(certNo));
        boolean result = callVerificationApi(certName, certNo);
        
        // 将验证结果保存到数据库
        CertVerificationRecord record = new CertVerificationRecord();
        record.setCertName(certName);
        record.setCertNo(certNo);
        record.setVerificationResult(result ? 1 : 0);
        certVerificationRecordRepository.save(record);
        log.info("验证结果已保存到数据库: 姓名={}, 身份证号={}*, 验证结果={}", 
                certName, maskCertNo(certNo), result);
        
        // 如果提供了profileId，更新验证状态
        if (profileId != null) {
            updateVerifyStatus(profileId, result);
        }
        
        return result;
    }
    
    /**
     * 掩码处理身份证号，只显示前6位和后4位
     */
    private String maskCertNo(String certNo) {
        if (certNo == null || certNo.length() < 10) {
            return "***";
        }
        int length = certNo.length();
        return certNo.substring(0, 6) + "******" + certNo.substring(length - 4);
    }
    
    /**
     * 更新档案的验证状态
     */
    private void updateVerifyStatus(Long profileId, boolean verificationResult) {
        // 根据验证结果设置状态：1-验证通过，2-验证不通过
        int status = verificationResult ? 1 : 2;
    }
    
    /**
     * 调用阿里云身份证二要素核验API
     */
    private boolean callVerificationApi(String certName, String certNo) {
        // 创建请求对象
        Id2MetaVerifyRequest request = new Id2MetaVerifyRequest();
        request.setParamType("normal");
        request.setUserName(certName);
        request.setIdentifyNum(certNo);
        
        // 调用服务
        Id2MetaVerifyResponse response = null;
        try {
            response = id2MetaVerifyAutoRoute(request);
        } catch (Exception e) {
            log.error("身份证二要素核验异常", e);
            return false;
        }
        
        // 处理响应结果
        if (response != null && response.getStatusCode() == 200 
                && response.getBody() != null && "200".equals(response.getBody().getCode())) {
            try {
                // 将整个响应转为JSON字符串
                String responseJson = JSON.toJSONString(response.getBody());
                log.info("身份验证API响应: {}", responseJson);
                
                // 解析JSON以获取bizCode
                JSONObject jsonObject = JSON.parseObject(responseJson);
                
                // 尝试获取BizCode
                String bizCode = null;
                
                // 从resultObject中获取bizCode (注意大小写）
                if (jsonObject.containsKey("resultObject")) {
                    Object resultObj = jsonObject.get("resultObject");
                    
                    // 结果可能是JSONObject或String
                    if (resultObj instanceof JSONObject) {
                        JSONObject resultObject = (JSONObject) resultObj;
                        if (resultObject.containsKey("bizCode")) {
                            bizCode = resultObject.getString("bizCode");
                        } else if (resultObject.containsKey("BizCode")) {
                            bizCode = resultObject.getString("BizCode");
                        }
                    } else if (resultObj instanceof String) {
                        // 直接是结果字符串
                        bizCode = resultObj.toString();
                    }
                }
                
                // 如果上面没找到，尝试其他可能的位置
                if (bizCode == null) {
                    // 直接从顶层找
                    if (jsonObject.containsKey("bizCode")) {
                        bizCode = jsonObject.getString("bizCode");
                    } else if (jsonObject.containsKey("BizCode")) {
                        bizCode = jsonObject.getString("BizCode");
                    } else if (jsonObject.containsKey("data")) {
                        // 有些API将结果放在data中
                        bizCode = jsonObject.getString("data");
                    }
                }
                
                // 返回结果: 1-校验一致, 2-校验不一致, 3-查无记录
                log.info("解析出的BizCode: {}", bizCode);
                if ("1".equals(bizCode)) {
                    return true;
                } else {
                    log.info("身份验证未通过，BizCode: {}", bizCode);
                    return false;
                }
            } catch (Exception e) {
                log.error("解析验证结果异常", e);
                return false;
            }
        } else {
            // 记录错误情况
            String errorMsg = response != null && response.getBody() != null 
                    ? response.getBody().getMessage() : "无响应";
            log.error("身份证二要素核验失败: {}", errorMsg);
            return false;
        }
    }
    
    /**
     * 主备服务点循环调用，获取到成功结果返回
     */
    private Id2MetaVerifyResponse id2MetaVerifyAutoRoute(Id2MetaVerifyRequest request) throws Exception {
        Id2MetaVerifyResponse lastResponse = null;
        for (String endpoint : ENDPOINTS) {
            try {
                // 调用服务
                Id2MetaVerifyResponse response = id2MetaVerify(endpoint, request);
                
                // 记录节点调用结果
                log.debug("节点 {} 调用结果状态码: {}", endpoint, 
                        response != null ? response.getStatusCode() : "无响应");
                
                // 有一个服务调用成功即返回
                if (response != null && response.getStatusCode() == 200) {
                    if (response.getBody() != null && "200".equals(response.getBody().getCode())) {
                        lastResponse = response;
                        break;
                    }
                }
            } catch (TeaException error) {
                log.error("节点 {} 调用异常: {}", endpoint, error.getMessage());
            } catch (Exception e) {
                log.error("节点 {} 调用异常: {}", endpoint, e.getMessage());
            }
        }
        return lastResponse;
    }
    
    /**
     * 获取服务Client实例，调用验证方法
     */
    private Id2MetaVerifyResponse id2MetaVerify(String endpoint, Id2MetaVerifyRequest request) throws Exception {
        // 获取SDK Client实例
        Client client = createClient(endpoint);
        // 构建RuntimeObject
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.setReadTimeout(5000);
        runtime.setConnectTimeout(5000);
        // 连接并调用API
        return client.id2MetaVerifyWithOptions(request, runtime);
    }
    
    /**
     * 安全创建服务Client实例
     */
    private Client createClient(String endpoint) throws Exception {
        // 创建SDK Client实例
        com.aliyun.teaopenapi.models.Config apiConfig = new com.aliyun.teaopenapi.models.Config();
        apiConfig.setAccessKeyId(ACCESS_KEY_ID);
        apiConfig.setAccessKeySecret(ACCESS_KEY_SECRET);
        apiConfig.setEndpoint(endpoint);
        return new Client(apiConfig);
    }
    
    /**
     * 生成缓存键
     * 对姓名和身份证号做哈希处理，避免敏感信息明文存储
     */
    private String generateCacheKey(String certName, String certNo) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            String combined = certName + ":" + certNo;
            byte[] digest = md.digest(combined.getBytes());
            return Base64.getEncoder().encodeToString(digest);
        } catch (NoSuchAlgorithmException e) {
            // 降级为简单组合（实际生产环境应该处理此异常）
            log.error("生成缓存键异常", e);
            return certName.hashCode() + ":" + certNo.hashCode();
        }
    }
} 