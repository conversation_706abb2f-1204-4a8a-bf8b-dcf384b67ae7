package com.titan.event.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.titan.event.entity.Venue;
import com.titan.event.entity.VenueProduct;
import com.titan.event.mapper.VenueMapper;
import com.titan.event.mapper.VenueProductMapper;
import com.titan.event.service.IVenueProductService;
import com.titan.event.service.IVenueService;
import com.titan.event.vo.VenueProductVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 场馆商品服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class VenueProductServiceImpl implements IVenueProductService {

    @Resource
    private VenueProductMapper venueProductMapper;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private IVenueService venueService;

    @Override
    public List<VenueProduct> findOnSaleProductsByVenueId(Long venueId) {
        return venueProductMapper.findOnSaleProductsByVenueId(venueId);
    }

    @Override
    public VenueProduct getProductDetail(Long productId) {
        // 通过JpaRepository提供的findById方法获取商品详情
        Optional<VenueProduct> productOpt = venueProductMapper.findById(productId);
        // 如果商品存在且未删除，则返回商品详情，否则返回null
        return productOpt.filter(product -> "0".equals(product.getDelFlag())).orElse(null);
    }
    
    @Override
    public VenueProductVO getProductDetailVO(Long productId) {
        // 获取原始商品数据
        VenueProduct product = getProductDetail(productId);
        if (product == null) {
            return null;
        }
        
        // 转换为VO对象
        VenueProductVO vo = new VenueProductVO();
        BeanUtils.copyProperties(product, vo);
        
        // 计算总销量(注水销量+实际销量)
        int fakeSales = product.getFakeSales() != null ? product.getFakeSales() : 0;
        int realSales = product.getRealSales() != null ? product.getRealSales() : 0;
        vo.setTotalSales(fakeSales + realSales);
        
        // 处理商品图片
        processProductImages(vo);
        
        // 获取商品对应场馆信息
        if (product.getVenueId() != null) {
            Venue venue = venueService.findVenueById(product.getVenueId());
            if (venue != null) {
                vo.setContactPhone(venue.getContactPhone());
                vo.setWxCustomerServiceId(venue.getWxCustomerServiceId());
            }
        }
        
        return vo;
    }
    
    /**
     * 处理商品图片字符串，转换为图片列表，并提取第一张图片作为封面
     * 
     * @param vo 商品VO对象
     */
    private void processProductImages(VenueProductVO vo) {
        String imagesStr = vo.getProductImages();
        List<String> imageList = new ArrayList<>();
        
        if (StringUtils.hasText(imagesStr)) {
            try {
                // 解析JSON字符串为列表
                imageList = objectMapper.readValue(imagesStr, new TypeReference<List<String>>() {});
                vo.setImageList(imageList);
                
                // 提取第一张图片作为封面
                if (!imageList.isEmpty()) {
                    vo.setCoverImage(imageList.get(0));
                }
            } catch (JsonProcessingException e) {
                log.error("处理商品图片字符串失败: {}", imagesStr, e);
                // 解析失败时，设置空列表
                vo.setImageList(imageList);
            }
        } else {
            vo.setImageList(imageList);
        }
    }
}