package com.titan.event.service.impl;

import com.titan.event.entity.UserInfo;
import com.titan.event.entity.Venue;
import com.titan.event.entity.VenueMemberCard;
import com.titan.event.entity.VenueMemberCardDetail;
import com.titan.event.entity.VenueMemberCardRelation;
import com.titan.event.entity.VenueMemberCardUser;
import com.titan.event.entity.VenueOrder;
import com.titan.event.entity.pay.PayOrder;
import com.titan.event.entity.pay.WxPayV3Bean;
import com.titan.event.enums.CardOperationType;
import com.titan.event.constant.OrderStatusConstant;
import com.titan.event.mapper.PayOrderMapper;
import com.titan.event.mapper.UserInfoMapper;
import com.titan.event.mapper.VenueMapper;
import com.titan.event.mapper.VenueMemberCardDetailMapper;
import com.titan.event.mapper.VenueMemberCardMapper;
import com.titan.event.mapper.VenueMemberCardRelationMapper;
import com.titan.event.mapper.VenueMemberCardUserMapper;
import com.titan.event.mapper.VenueOrderMapper;
import com.titan.event.request.usercard.CardPurchaseRequest;
import com.titan.event.request.usercard.DeductCountCardRequest;
import com.titan.event.request.usercard.SearchCountCardByPhoneRequest;
import com.titan.event.request.usercard.StorageCardRechargeRequest;
import com.titan.event.request.usercard.UpdateSubCardsRequest;
import com.titan.event.request.usercard.UserCardDetailRequest;
import com.titan.event.request.usercard.UserCardListRequest;
import com.titan.event.request.usercard.UserCardUsageDetailRequest;
import com.titan.event.service.IUserCardService;
import com.titan.event.vo.PageResultVO;
import com.titan.event.vo.PaymentInfoVO;
import com.titan.event.vo.usercard.CardDetailVO;
import com.titan.event.vo.usercard.CardPurchaseVO;
import com.titan.event.vo.usercard.CountCardVO;
import com.titan.event.vo.usercard.StorageCardRechargeVO;
import com.titan.event.vo.usercard.SubCardInfoVO;
import com.titan.event.vo.usercard.UserCardDetailVO;
import com.titan.event.vo.usercard.UserCardUsageDetailVO;
import com.titan.event.vo.usercard.UserCardVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

// 支付相关import
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.core.enums.AuthTypeEnum;
import com.ijpay.core.enums.RequestMethodEnum;
import com.ijpay.core.kit.PayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.enums.WxDomainEnum;
import com.ijpay.wxpay.enums.v3.BasePayApiEnum;
import com.ijpay.wxpay.model.v3.Amount;
import com.ijpay.wxpay.model.v3.Payer;
import com.ijpay.wxpay.model.v3.UnifiedOrderModel;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户卡服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCardServiceImpl implements IUserCardService {

    private String serialNo;
    
    @Resource
    private VenueMemberCardUserMapper venueMemberCardUserMapper;

    @Resource
    private VenueMemberCardMapper venueMemberCardMapper;

    @Resource
    private VenueMemberCardDetailMapper venueMemberCardDetailMapper;

    @Resource
    private VenueMemberCardRelationMapper venueMemberCardRelationMapper;

    @Resource
    private VenueMapper venueMapper;

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private PayOrderMapper payOrderMapper;

    @Resource
    private VenueOrderMapper venueOrderMapper;

    @Resource
    private WxPayV3Bean wxPayV3Bean;
    
    @Override
    public PageResultVO<UserCardVO> getUserCardList(UserCardListRequest request, Long userId) {
        // 创建查询条件
        Specification<VenueMemberCardUser> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 用户ID条件
            predicates.add(criteriaBuilder.equal(root.get("userId"), userId));
            
            // 未删除
            predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
            
            // 卡类型条件
            if (request.getCardType() != null) {
                if (request.getCardType() == 0) {
                    // 权益卡：计次卡并且是无限次使用
                    predicates.add(criteriaBuilder.equal(root.get("cardType"), 2));
                    predicates.add(criteriaBuilder.equal(root.get("unlimitedUsage"), 1));
                } else if (request.getCardType() == 1) {
                    // 储值卡：卡类型是储值卡的
                    predicates.add(criteriaBuilder.equal(root.get("cardType"), 1));
                } else if (request.getCardType() == 2) {
                    // 计次卡：非无限次的计次卡
                    predicates.add(criteriaBuilder.equal(root.get("cardType"), 2));
                    predicates.add(criteriaBuilder.equal(root.get("unlimitedUsage"), 0));
                } else if (request.getCardType() == 3) {
                    // 会员卡：写死0（这里可能需要根据实际业务调整）
                    predicates.add(criteriaBuilder.equal(root.get("cardType"), 0));
                }
            }
            
            // 可用状态条件
            if (request.getAvailable() != null) {
                LocalDateTime now = LocalDateTime.now();
                if (request.getAvailable() == 1) {
                    // 可用：状态正常且在有效期内
                    predicates.add(criteriaBuilder.equal(root.get("status"), "0"));
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("validityStartTime"), now));
                    predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("validityEndTime"), now));
                } else {
                    // 不可用：状态不正常或不在有效期内
                    Predicate statusPredicate = criteriaBuilder.notEqual(root.get("status"), "0");
                    Predicate startTimePredicate = criteriaBuilder.greaterThan(root.get("validityStartTime"), now);
                    Predicate endTimePredicate = criteriaBuilder.lessThan(root.get("validityEndTime"), now);
                    predicates.add(criteriaBuilder.or(statusPredicate, startTimePredicate, endTimePredicate));
                }
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        // 设置排序字段为创建时间，降序排列
        request.setSortKey("createTime");
        request.setSortMethod("desc");
        
        // 使用BaseSearchRequest的getPageInfo方法获取分页和排序信息
        Pageable pageable = request.getPageInfo();
        
        // 执行查询
        Page<VenueMemberCardUser> cardPage = venueMemberCardUserMapper.findAll(spec, pageable);
        
        // 如果没有数据，返回空结果
        if (cardPage.isEmpty()) {
            return PageResultVO.build(
                    request.getPageNum(),
                    request.getPageSize(),
                    0L,
                    new ArrayList<>()
            );
        }
        
        // 批量查询卡信息，获取封面图片
        List<Long> cardIds = cardPage.getContent().stream()
                .map(VenueMemberCardUser::getCardId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, VenueMemberCard> memberCardMap = new HashMap<>();
        if (!cardIds.isEmpty()) {
            try {
                List<VenueMemberCard> memberCards = venueMemberCardMapper.findAllById(cardIds);
                memberCardMap = memberCards.stream()
                        .collect(Collectors.toMap(VenueMemberCard::getCardId, card -> card, (existing, replacement) -> existing));
            } catch (Exception e) {
                log.warn("批量查询卡信息失败", e);
            }
        }

        // 转换为VO
        final Map<Long, VenueMemberCard> finalMemberCardMap = memberCardMap;
        List<UserCardVO> cardVOList = cardPage.getContent().stream()
                .map(card -> convertToUserCardVO(card, finalMemberCardMap.get(card.getCardId())))
                .collect(Collectors.toList());
        
        return PageResultVO.build(
                request.getPageNum(),
                request.getPageSize(),
                cardPage.getTotalElements(),
                cardVOList
        );
    }
    
    @Override
    public Map<String, Integer> getUserCardCountStatistics(Long userId) {
        // 获取用户信息，特别是手机号
        UserInfo userInfo = userInfoMapper.findById(userId).orElse(null);
        String userPhone = userInfo != null ? userInfo.getPhone() : null;

        // 查询用户所有未删除的卡（使用userId OR 手机号查询）
        List<VenueMemberCardUser> allCards = getUserCardsByUserIdOrPhone(userId, userPhone);
        
        Map<String, Integer> statistics = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 初始化计数器
        int benefitCardCount = 0;
        int storageCardCount = 0;
        int countCardCount = 0;
        int memberCardCount = 0;
        int availableBenefitCardCount = 0;
        int availableStorageCardCount = 0;
        int availableCountCardCount = 0;
        int availableMemberCardCount = 0;
        
        for (VenueMemberCardUser card : allCards) {
            // 判断是否可用
            boolean isAvailable = "0".equals(card.getStatus()) 
                    && card.getValidityStartTime() != null 
                    && card.getValidityEndTime() != null
                    && !now.isBefore(card.getValidityStartTime()) 
                    && !now.isAfter(card.getValidityEndTime());
            
            // 根据卡类型分类统计
            if (card.getCardType() == 2 && card.getUnlimitedUsage() == 1) {
                // 权益卡：计次卡并且是无限次使用
                benefitCardCount++;
                if (isAvailable) {
                    availableBenefitCardCount++;
                }
            } else if (card.getCardType() == 1) {
                // 储值卡
                storageCardCount++;
                if (isAvailable) {
                    availableStorageCardCount++;
                }
            } else if (card.getCardType() == 2 && card.getUnlimitedUsage() == 0) {
                // 计次卡：非无限次的计次卡
                countCardCount++;
                if (isAvailable) {
                    availableCountCardCount++;
                }
            } else if (card.getCardType() == 0) {
                // 会员卡：写死0
                memberCardCount++;
                if (isAvailable) {
                    availableMemberCardCount++;
                }
            }
        }
        
        statistics.put("benefitCardCount", benefitCardCount);
        statistics.put("storageCardCount", storageCardCount);
        statistics.put("countCardCount", countCardCount);
        statistics.put("memberCardCount", memberCardCount);
        statistics.put("availableBenefitCardCount", availableBenefitCardCount);
        statistics.put("availableStorageCardCount", availableStorageCardCount);
        statistics.put("availableCountCardCount", availableCountCardCount);
        statistics.put("availableMemberCardCount", availableMemberCardCount);
        
        return statistics;
    }
    
    /**
     * 转换为UserCardVO
     */
    private UserCardVO convertToUserCardVO(VenueMemberCardUser card) {
        return convertToUserCardVO(card, null);
    }

    /**
     * 转换为UserCardVO（带卡信息）
     */
    private UserCardVO convertToUserCardVO(VenueMemberCardUser card, VenueMemberCard memberCard) {
        UserCardVO vo = new UserCardVO();
        BeanUtils.copyProperties(card, vo);

        // 设置封面图片
        if (memberCard != null) {
            vo.setCoverImage(memberCard.getCoverImage());
        } else {
            // 如果没有传入卡信息，则查询
            try {
                VenueMemberCard queryCard = venueMemberCardMapper.findById(card.getCardId()).orElse(null);
                if (queryCard != null) {
                    vo.setCoverImage(queryCard.getCoverImage());
                }
            } catch (Exception e) {
                log.warn("获取卡信息失败，cardId: {}", card.getCardId(), e);
            }
        }

        // 设置卡类型名称和实际卡类型
        if (card.getCardType() == 2 && card.getUnlimitedUsage() == 1) {
            // 权益卡
            vo.setCardType(0);
            vo.setCardTypeName("权益卡");
        } else if (card.getCardType() == 1) {
            // 储值卡
            vo.setCardType(1);
            vo.setCardTypeName("储值卡");
        } else if (card.getCardType() == 2 && card.getUnlimitedUsage() == 0) {
            // 计次卡
            vo.setCardType(2);
            vo.setCardTypeName("计次卡");
        } else if (card.getCardType() == 0) {
            // 会员卡
            vo.setCardType(3);
            vo.setCardTypeName("会员卡");
        } else {
            vo.setCardTypeName("未知");
        }

        // 设置状态名称
        vo.setStatusName(getStatusName(card.getStatus()));

        // 判断是否可用
        LocalDateTime now = LocalDateTime.now();
        boolean isAvailable = "0".equals(card.getStatus())
                && card.getValidityStartTime() != null
                && card.getValidityEndTime() != null
                && !now.isBefore(card.getValidityStartTime())
                && !now.isAfter(card.getValidityEndTime());
        vo.setAvailable(isAvailable);

        return vo;
    }
    
    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case "0":
                return "正常";
            case "1":
                return "停用";
            case "2":
                return "过期";
            default:
                return "未知";
        }
    }

    @Override
    public UserCardDetailVO getUserCardDetail(UserCardDetailRequest request, Long userId) {
        // 查询用户卡信息
        VenueMemberCardUser userCard = venueMemberCardUserMapper.findById(request.getUserCardId()).orElse(null);
        if (userCard == null) {
            throw new RuntimeException("用户卡不存在");
        }

        // 验证卡是否属于当前用户
        if (!userId.equals(userCard.getUserId())) {
            throw new RuntimeException("无权限访问该卡信息");
        }

        UserCardDetailVO vo = new UserCardDetailVO();
        BeanUtils.copyProperties(userCard, vo);

        // 获取卡信息，特别是封面图片和介绍
        try {
            VenueMemberCard memberCard = venueMemberCardMapper.findById(userCard.getCardId()).orElse(null);
            if (memberCard != null) {
                vo.setCoverImage(memberCard.getCoverImage());
                vo.setCardDescription(memberCard.getCardDescription());
            }
        } catch (Exception e) {
            log.warn("获取卡信息失败，cardId: {}", userCard.getCardId(), e);
        }

        // 设置卡类型名称和实际卡类型
        setCardTypeInfo(vo, userCard);

        // 设置状态名称
        vo.setStatusName(getStatusName(userCard.getStatus()));

        // 判断是否可用
        LocalDateTime now = LocalDateTime.now();
        boolean isAvailable = "0".equals(userCard.getStatus())
                && userCard.getValidityStartTime() != null
                && userCard.getValidityEndTime() != null
                && !now.isBefore(userCard.getValidityStartTime())
                && !now.isAfter(userCard.getValidityEndTime());
        vo.setAvailable(isAvailable);

        // 获取场馆联系信息
        try {
            Venue venue = venueMapper.findById(userCard.getVenueId()).orElse(null);
            if (venue != null) {
                vo.setContactPerson(venue.getContactPerson());
                vo.setContactPhone(venue.getContactPhone());
                vo.setWxCustomerServiceId(venue.getWxCustomerServiceId());
            }
        } catch (Exception e) {
            log.warn("获取场馆信息失败，venueId: {}", userCard.getVenueId(), e);
        }

        // 获取当前用户信息
        UserInfo currentUserInfo = userInfoMapper.findById(userId).orElse(null);

        // 获取主副卡关系信息（固定展示3个：1主卡+2副卡）
        try {
            List<VenueMemberCardRelation> relations = venueMemberCardRelationMapper.findByCardIdAndDelFlag(userCard.getCardId(), "0");
            List<UserCardDetailVO.CardRelationVO> cardRelations = new ArrayList<>();

            // 获取当前登录用户的手机号
            String currentUserPhone = currentUserInfo != null ? currentUserInfo.getPhone() : "";

            // 1. 添加主卡信息（固定第一个位置）
            UserCardDetailVO.CardRelationVO mainCardRelation = new UserCardDetailVO.CardRelationVO();
            mainCardRelation.setCardUserId(userCard.getUserCardId());
            mainCardRelation.setCardRelationType(1); // 主卡
            mainCardRelation.setCardRelationTypeName("主卡");
            mainCardRelation.setCardNumber(userCard.getCardNumber());
            mainCardRelation.setUserName(currentUserPhone); // 主卡显示登录用户手机号
            mainCardRelation.setStatus("0"); // 默认正常状态
            mainCardRelation.setStatusName("正常");
            cardRelations.add(mainCardRelation);

            // 2. 获取副卡信息（只处理副卡）
            List<VenueMemberCardRelation> subCardRelations = relations.stream()
                    .filter(relation -> relation.getCardRelationType() == 2)
                    .collect(Collectors.toList());

            // 3. 添加副卡信息（固定2个位置）
            for (int i = 0; i < 2; i++) {
                UserCardDetailVO.CardRelationVO subCardRelation = new UserCardDetailVO.CardRelationVO();
                subCardRelation.setCardRelationType(2); // 副卡
                subCardRelation.setCardRelationTypeName("副卡");

                if (i < subCardRelations.size()) {
                    // 有副卡数据
                    VenueMemberCardRelation relation = subCardRelations.get(i);
                    subCardRelation.setRelationId(relation.getRelationId());
                    subCardRelation.setCardUserId(relation.getCardUserId());
                    subCardRelation.setStatus(relation.getStatus());
                    subCardRelation.setStatusName(getStatusName(relation.getStatus()));

                    // 获取副卡用户的手机号
                    try {
                        VenueMemberCardUser relatedUserCard = venueMemberCardUserMapper.findById(relation.getCardUserId()).orElse(null);
                        if (relatedUserCard != null) {
                            subCardRelation.setCardNumber(relatedUserCard.getCardNumber());
                            // 获取副卡用户的手机号
                            UserInfo relatedUserInfo = userInfoMapper.findById(relatedUserCard.getUserId()).orElse(null);
                            String relatedUserPhone = relatedUserInfo != null ? relatedUserInfo.getPhone() : null;
                            subCardRelation.setUserName(relatedUserPhone);
                        }
                    } catch (Exception e) {
                        log.warn("获取关联用户卡信息失败，cardUserId: {}", relation.getCardUserId(), e);
                        subCardRelation.setUserName(null);
                    }
                } else {
                    // 没有副卡数据，设置为空
                    subCardRelation.setRelationId(null);
                    subCardRelation.setCardUserId(null);
                    subCardRelation.setCardNumber(null);
                    subCardRelation.setUserName(null); // 手机号为null
                    subCardRelation.setStatus(null);
                    subCardRelation.setStatusName(null);
                }

                cardRelations.add(subCardRelation);
            }

            vo.setCardRelations(cardRelations);
        } catch (Exception e) {
            log.warn("获取主副卡关系失败，cardId: {}", userCard.getCardId(), e);
            // 即使出错也要保证返回3个对象的结构
            List<UserCardDetailVO.CardRelationVO> cardRelations = new ArrayList<>();

            // 主卡
            UserCardDetailVO.CardRelationVO mainCard = new UserCardDetailVO.CardRelationVO();
            mainCard.setCardRelationType(1);
            mainCard.setCardRelationTypeName("主卡");
            mainCard.setUserName(currentUserInfo != null ? currentUserInfo.getPhone() : null);
            cardRelations.add(mainCard);

            // 两个空副卡
            for (int i = 0; i < 2; i++) {
                UserCardDetailVO.CardRelationVO subCard = new UserCardDetailVO.CardRelationVO();
                subCard.setCardRelationType(2);
                subCard.setCardRelationTypeName("副卡");
                subCard.setUserName(null);
                cardRelations.add(subCard);
            }

            vo.setCardRelations(cardRelations);
        }

        return vo;
    }

    @Override
    public PageResultVO<UserCardUsageDetailVO> getUserCardUsageDetail(UserCardUsageDetailRequest request, Long userId) {
        // 验证用户卡是否属于当前用户
        VenueMemberCardUser userCard = venueMemberCardUserMapper.findById(request.getUserCardId()).orElse(null);
        if (userCard == null) {
            throw new RuntimeException("用户卡不存在");
        }

        if (!userId.equals(userCard.getUserId())) {
            throw new RuntimeException("无权限访问该卡信息");
        }

        // 创建查询条件
        Specification<VenueMemberCardDetail> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 使用用户会员卡ID查询
            predicates.add(criteriaBuilder.equal(root.get("userCardId"), request.getUserCardId()));

            // 未删除
            predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // 设置排序字段为操作时间，降序排列
        request.setSortKey("operationTime");
        request.setSortMethod("desc");

        // 使用BaseSearchRequest的getPageInfo方法获取分页和排序信息
        Pageable pageable = request.getPageInfo();

        // 执行查询
        Page<VenueMemberCardDetail> detailPage = venueMemberCardDetailMapper.findAll(spec, pageable);

        // 如果没有数据，返回空结果
        if (detailPage.isEmpty()) {
            return PageResultVO.build(
                    request.getPageNum(),
                    request.getPageSize(),
                    0L,
                    new ArrayList<>()
            );
        }

        // 转换为VO
        List<UserCardUsageDetailVO> detailVOList = detailPage.getContent().stream()
                .map(this::convertToUserCardUsageDetailVO)
                .collect(Collectors.toList());

        return PageResultVO.build(
                request.getPageNum(),
                request.getPageSize(),
                detailPage.getTotalElements(),
                detailVOList
        );
    }

    /**
     * 设置卡类型信息
     */
    private void setCardTypeInfo(UserCardDetailVO vo, VenueMemberCardUser card) {
        if (card.getCardType() == 2 && card.getUnlimitedUsage() == 1) {
            // 权益卡
            vo.setCardType(0);
            vo.setCardTypeName("权益卡");
        } else if (card.getCardType() == 1) {
            // 储值卡
            vo.setCardType(1);
            vo.setCardTypeName("储值卡");
        } else if (card.getCardType() == 2 && card.getUnlimitedUsage() == 0) {
            // 计次卡
            vo.setCardType(2);
            vo.setCardTypeName("计次卡");
        } else if (card.getCardType() == 0) {
            // 会员卡
            vo.setCardType(3);
            vo.setCardTypeName("会员卡");
        } else {
            vo.setCardTypeName("未知");
        }
    }

    /**
     * 获取卡关系类型名称
     */
    private String getCardRelationTypeName(Integer cardRelationType) {
        if (cardRelationType == null) {
            return "未知";
        }
        switch (cardRelationType) {
            case 1:
                return "主卡";
            case 2:
                return "副卡";
            default:
                return "未知";
        }
    }

    /**
     * 转换为UserCardUsageDetailVO
     */
    private UserCardUsageDetailVO convertToUserCardUsageDetailVO(VenueMemberCardDetail detail) {
        UserCardUsageDetailVO vo = new UserCardUsageDetailVO();
        BeanUtils.copyProperties(detail, vo);

        // 设置操作类型名称
        vo.setOperationTypeName(getOperationTypeName(detail.getOperationType()));

        // 设置操作来源名称
        vo.setOperationSourceName(getOperationSourceName(detail.getOperationSource()));

        return vo;
    }

    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(Integer operationType) {
        return CardOperationType.getNameByCode(operationType);
    }

    /**
     * 获取操作来源名称
     */
    private String getOperationSourceName(Integer operationSource) {
        if (operationSource == null) {
            return "未知";
        }
        switch (operationSource) {
            case 1:
                return "管理端";
            case 2:
                return "用户端";
            default:
                return "未知";
        }
    }

    /**
     * 根据用户ID或手机号查询用户卡列表
     */
    private List<VenueMemberCardUser> getUserCardsByUserIdOrPhone(Long userId, String phone) {
        // 创建查询条件
        Specification<VenueMemberCardUser> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 未删除
            predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));

            // 用户ID或手机号条件（OR关系）
            List<Predicate> userPredicates = new ArrayList<>();
            userPredicates.add(criteriaBuilder.equal(root.get("userId"), userId));

            if (phone != null && !phone.trim().isEmpty()) {
                // 通过手机号查询用户ID，然后查询卡
                List<UserInfo> userInfos = userInfoMapper.findAll((root2, query2, criteriaBuilder2) ->
                    criteriaBuilder2.equal(root2.get("phone"), phone));

                if (!userInfos.isEmpty()) {
                    List<Long> userIds = userInfos.stream()
                            .map(UserInfo::getUserId)
                            .collect(Collectors.toList());
                    userPredicates.add(root.get("userId").in(userIds));
                }
            }

            if (!userPredicates.isEmpty()) {
                predicates.add(criteriaBuilder.or(userPredicates.toArray(new Predicate[0])));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return venueMemberCardUserMapper.findAll(spec);
    }

    @Override
    public List<SubCardInfoVO> updateSubCards(UpdateSubCardsRequest request, Long userId) {
        // 验证用户卡是否属于当前用户
        VenueMemberCardUser userCard = venueMemberCardUserMapper.findById(request.getUserCardId()).orElse(null);
        if (userCard == null) {
            throw new RuntimeException("用户卡不存在");
        }

        if (!userId.equals(userCard.getUserId())) {
            throw new RuntimeException("无权限修改该卡信息");
        }

        // 获取当前所有副卡关系
        List<VenueMemberCardRelation> existingRelations = venueMemberCardRelationMapper
                .findByCardIdAndDelFlag(userCard.getCardId(), "0")
                .stream()
                .filter(relation -> relation.getCardRelationType() == 2) // 只处理副卡
                .collect(Collectors.toList());

        // 先删除所有现有的副卡关系
        for (VenueMemberCardRelation existingRelation : existingRelations) {
            existingRelation.setDelFlag("1");
            venueMemberCardRelationMapper.save(existingRelation);
        }

        List<SubCardInfoVO> resultList = new ArrayList<>();

        // 校验副卡数量和数据
        if (request.getSubCards() != null) {
            // 校验传入的副卡列表长度（固定为2个）
            if (request.getSubCards().size() != 2) {
                throw new RuntimeException("副卡列表必须包含2个元素");
            }

            // 获取有效的副卡手机号列表
            List<String> validPhones = request.getSubCards().stream()
                    .map(UpdateSubCardsRequest.SubCardInfo::getUserPhone)
                    .filter(phone -> phone != null && !phone.trim().isEmpty())
                    .collect(Collectors.toList());

            // 副卡数量上限为2
            if (validPhones.size() > 2) {
                throw new RuntimeException("副卡数量不能超过2个");
            }

            // 移除手机号格式校验，只保留数量和重复性校验

            // 校验手机号不能重复
            Set<String> phoneSet = new HashSet<>(validPhones);
            if (phoneSet.size() != validPhones.size()) {
                throw new RuntimeException("副卡手机号不能重复");
            }

            // 校验副卡手机号不能与主卡手机号相同
            UserInfo currentUserInfo = userInfoMapper.findById(userId).orElse(null);
            String mainCardPhone = currentUserInfo != null ? currentUserInfo.getPhone() : null;
            if (mainCardPhone != null) {
                for (String phone : validPhones) {
                    if (mainCardPhone.equals(phone)) {
                        throw new RuntimeException("副卡手机号不能与主卡手机号相同");
                    }
                }
            }
        }

        // 处理传入的副卡信息（只处理有手机号的）
        if (request.getSubCards() != null) {
            for (UpdateSubCardsRequest.SubCardInfo subCardInfo : request.getSubCards()) {
                if (subCardInfo.getUserPhone() != null && !subCardInfo.getUserPhone().trim().isEmpty()) {
                    try {
                        SubCardInfoVO subCardVO = processSubCard(userCard, subCardInfo);
                        if (subCardVO != null) {
                            resultList.add(subCardVO);
                        }
                    } catch (Exception e) {
                        log.error("处理副卡信息失败，手机号: {}", subCardInfo.getUserPhone(), e);
                        throw new RuntimeException("处理副卡信息失败: " + e.getMessage());
                    }
                }
            }
        }

        return resultList;
    }

    /**
     * 处理单个副卡信息
     */
    private SubCardInfoVO processSubCard(VenueMemberCardUser mainCard,
                                        UpdateSubCardsRequest.SubCardInfo subCardInfo) {

        // 根据手机号查找或创建用户
        UserInfo subCardUser = findOrCreateUserByPhone(subCardInfo.getUserPhone(), subCardInfo.getUserPhone());

        // 为副卡用户创建或查找用户卡
        VenueMemberCardUser subUserCard = findOrCreateUserCard(mainCard, subCardUser);

        // 创建新的副卡关系
        VenueMemberCardRelation relation = new VenueMemberCardRelation();
        relation.setCardId(mainCard.getCardId());
        relation.setCardRelationType(2); // 副卡
        relation.setDelFlag("0");
        relation.setStatus("0"); // 默认正常状态
        relation.setCreateTime(LocalDateTime.now());
        relation.setUpdateTime(LocalDateTime.now());
        relation.setCardUserId(subUserCard.getUserCardId());

        relation = venueMemberCardRelationMapper.save(relation);

        // 转换为VO
        SubCardInfoVO vo = new SubCardInfoVO();
        vo.setRelationId(relation.getRelationId());
        vo.setCardUserId(subUserCard.getUserCardId());
        vo.setUserPhone(subCardInfo.getUserPhone());
        vo.setUserName(subCardUser.getNickname());
        vo.setCardNumber(subUserCard.getCardNumber());
        vo.setStatus(relation.getStatus());
        vo.setStatusName(getStatusName(relation.getStatus()));

        return vo;
    }

    /**
     * 根据手机号查找或创建用户
     */
    private UserInfo findOrCreateUserByPhone(String phone, String userName) {
        // 先查找是否存在该手机号的用户
        List<UserInfo> existingUsers = userInfoMapper.findAll((root, query, criteriaBuilder) ->
            criteriaBuilder.equal(root.get("phone"), phone));

        if (!existingUsers.isEmpty()) {
            return existingUsers.get(0);
        }

        // 如果不存在，创建新用户（这里可能需要根据实际业务逻辑调整）
        UserInfo newUser = new UserInfo();
        newUser.setPhone(phone);
        newUser.setNickname(userName != null ? userName : phone);
        // 设置其他必要字段...

        return userInfoMapper.save(newUser);
    }

    /**
     * 为用户创建或查找用户卡
     */
    private VenueMemberCardUser findOrCreateUserCard(VenueMemberCardUser mainCard, UserInfo user) {
        // 查找是否已存在该用户的同类型卡
        List<VenueMemberCardUser> existingCards = venueMemberCardUserMapper.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("userId"), user.getUserId()));
            predicates.add(criteriaBuilder.equal(root.get("cardId"), mainCard.getCardId()));
            predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });

        if (!existingCards.isEmpty()) {
            return existingCards.get(0);
        }

        // 创建新的用户卡
        VenueMemberCardUser newUserCard = new VenueMemberCardUser();
        BeanUtils.copyProperties(mainCard, newUserCard);
        newUserCard.setUserCardId(null); // 清空ID，让数据库自动生成
        newUserCard.setUserId(user.getUserId());
        newUserCard.setUserName(user.getNickname());
        newUserCard.setCardNumber(generateCardNumber()); // 生成新的卡号
        newUserCard.setCreateTime(LocalDateTime.now());
        newUserCard.setUpdateTime(LocalDateTime.now());

        return venueMemberCardUserMapper.save(newUserCard);
    }

    /**
     * 生成卡号（简单实现，实际可能需要更复杂的逻辑）
     */
    private String generateCardNumber() {
        return "SUB" + System.currentTimeMillis();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StorageCardRechargeVO rechargeStorageCard(StorageCardRechargeRequest request, Long userId) {
        // 1. 从用户表获取openId
        UserInfo userInfo = userInfoMapper.findById(userId).orElse(null);
        if (userInfo == null) {
            throw new RuntimeException("用户不存在");
        }

        String openId = userInfo.getOpenId();
        if (openId == null || openId.isEmpty() || "null".equals(openId)) {
            throw new RuntimeException("用户未绑定微信，请使用微信登录");
        }

        VenueMemberCardUser userCard = null;
        boolean isNewCard = false;
        BigDecimal beforeBalance = BigDecimal.ZERO;

        // 2. 获取或创建用户储值卡
        if (request.getUserCardId() != null) {
            // 如果传了卡ID，为这个卡片充值
            userCard = venueMemberCardUserMapper.findById(request.getUserCardId()).orElse(null);
            if (userCard == null) {
                throw new RuntimeException("用户卡不存在");
            }

            // 验证卡是否属于当前用户
            if (!userId.equals(userCard.getUserId())) {
                throw new RuntimeException("无权限操作该卡");
            }

            // 验证是否为储值卡
            if (userCard.getCardType() != 1) {
                throw new RuntimeException("只能为储值卡充值");
            }

            beforeBalance = userCard.getBalance() != null ? userCard.getBalance() : BigDecimal.ZERO;
        } else if (request.getVenueId() != null) {
            // 如果没有传卡ID，需要场馆ID查询这个场馆的储值卡为用户开卡
            // 先检查用户是否已有该场馆的储值卡
            List<VenueMemberCardUser> existingCards = venueMemberCardUserMapper.findByVenueIdAndUserIdAndDelFlag(request.getVenueId(), userId, "0");
            Optional<VenueMemberCardUser> existingStorageCard = existingCards.stream()
                    .filter(card -> card.getCardType() == 1) // 储值卡
                    .findFirst();

            if (existingStorageCard.isPresent()) {
                // 用户已有储值卡，直接使用
                userCard = existingStorageCard.get();
                isNewCard = false;
                beforeBalance = userCard.getBalance() != null ? userCard.getBalance() : BigDecimal.ZERO;
            } else {
                // 用户没有储值卡，需要开新卡
                userCard = createNewStorageCard(request.getVenueId(), userId, userInfo);
                isNewCard = true;
                beforeBalance = BigDecimal.ZERO;
            }
        } else {
            throw new RuntimeException("必须传入用户卡ID或场馆ID");
        }

        // 3. 创建支付订单
        String orderId = generateOrderId();
        PaymentInfoVO paymentInfo = createPayOrder(orderId, request.getAmount(), userCard, userId, openId);

        // 4. 构建响应VO
        StorageCardRechargeVO vo = new StorageCardRechargeVO();
        vo.setUserCardId(userCard.getUserCardId());
        vo.setCardId(userCard.getCardId());
        vo.setCardName(userCard.getCardName());
        vo.setCardNumber(userCard.getCardNumber());
        vo.setVenueId(userCard.getVenueId());
        vo.setVenueName(userCard.getVenueName());
        vo.setBeforeBalance(beforeBalance);
        vo.setRechargeAmount(request.getAmount());
        vo.setAfterBalance(beforeBalance.add(request.getAmount())); // 注意：实际余额在支付成功后才会更新
        vo.setIsNewCard(isNewCard);
        vo.setPaymentInfo(paymentInfo);
        vo.setRechargeTime(LocalDateTime.now());

        return vo;
    }

    /**
     * 创建新的储值卡
     */
    private VenueMemberCardUser createNewStorageCard(Long venueId, Long userId, UserInfo userInfo) {
        // 查找该场馆的储值卡模板
        List<VenueMemberCard> storageCardTemplates = venueMemberCardMapper.findByVenueIdAndDelFlag(venueId, "0")
                .stream()
                .filter(card -> card.getCardType() == 1) // 储值卡
                .collect(Collectors.toList());

        if (storageCardTemplates.isEmpty()) {
            throw new RuntimeException("该场馆没有可用的储值卡");
        }

        // 使用第一个储值卡模板为用户开卡
        VenueMemberCard cardTemplate = storageCardTemplates.get(0);

        VenueMemberCardUser newUserCard = new VenueMemberCardUser();
        newUserCard.setUserId(userId);
        newUserCard.setUserName(userInfo != null ? userInfo.getNickname() : "用户" + userId);
        newUserCard.setCardId(cardTemplate.getCardId());
        newUserCard.setCardName(cardTemplate.getCardName());
        newUserCard.setVenueId(cardTemplate.getVenueId());
        newUserCard.setVenueName(cardTemplate.getVenueName());
        newUserCard.setCardNumber(generateStorageCardNumber(cardTemplate.getCardNumberPrefix()));
        newUserCard.setCardType(1); // 储值卡
        newUserCard.setUnlimitedUsage(0);
        newUserCard.setBalance(BigDecimal.ZERO);
        newUserCard.setValidityStartTime(LocalDateTime.now());
        newUserCard.setValidityEndTime(LocalDateTime.now().plusYears(10)); // 默认10年有效期
        newUserCard.setStatus("0"); // 正常状态
        newUserCard.setDelFlag("0");
        newUserCard.setCreateTime(LocalDateTime.now());
        newUserCard.setUpdateTime(LocalDateTime.now());

        return venueMemberCardUserMapper.save(newUserCard);
    }

    /**
     * 生成储值卡卡号
     */
    private String generateStorageCardNumber(String prefix) {
        String cardPrefix = prefix != null ? prefix : "SC";
        return cardPrefix + System.currentTimeMillis();
    }

    /**
     * 生成订单号
     */
    private String generateOrderId() {
        return "RECHARGE_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    /**
     * 创建支付订单
     */
    private PaymentInfoVO createPayOrder(String orderId, BigDecimal amount, VenueMemberCardUser userCard, Long userId, String openId) {
        try {
            // 1. 保存支付订单到数据库
            PayOrder payOrder = PayOrder.builder()
                    .userId(userId)
                    .openId(openId)
                    .orderId(orderId)
                    .amount(amount.doubleValue())
                    .body("储值卡充值-" + userCard.getCardName())
                    .payType("WECHAT")
                    .payStatus("NOTPAY")
                    .userCardId(userCard.getUserCardId()) // 关联用户卡ID
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();

            payOrderMapper.save(payOrder);

            // 2. 创建微信支付订单
            String prepayId = createWxPayOrder(
                    "储值卡充值-" + userCard.getCardName(),
                    orderId,
                    amount.multiply(new BigDecimal("100")).intValue(), // 转换为分
                    LocalDateTime.now().plusMinutes(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'")),
                    openId
            );

            // 3. 更新prepayId
            payOrder.setPrepayId(prepayId);
            payOrderMapper.save(payOrder);

            // 4. 构建支付信息VO
            PaymentInfoVO paymentInfo = PaymentInfoVO.builder()
                    .payStatus("NOTPAY")
                    .prepayId(prepayId)
                    .payType("WECHAT")
                    .amount(amount.doubleValue())
                    .body("储值卡充值-" + userCard.getCardName())
                    .timeExpire(LocalDateTime.now().plusMinutes(30))
                    .build();

            return paymentInfo;
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建微信支付订单并获取prepayId
     */
    private String createWxPayOrder(String description, String outTradeNo, int amount, String timeExpire, String openId) {
        try {
            // 构建统一下单模型
            UnifiedOrderModel unifiedOrderModel = new UnifiedOrderModel()
                    .setAppid(wxPayV3Bean.getAppId())
                    .setMchid(wxPayV3Bean.getMchId())
                    .setDescription(description)
                    .setOut_trade_no(outTradeNo)
                    .setTime_expire(timeExpire)
                    .setAttach(description)
                    .setPayer(Payer.builder()
                            .openid(openId)
                            .build())
                    .setNotify_url(wxPayV3Bean.getDomain().concat("/wxPay/payNotify"))
                    .setAmount(new Amount().setTotal(amount));

            log.info("统一下单参数 {}", JSONUtil.toJsonStr(unifiedOrderModel));

            // 获取证书序列号
            if (StringUtils.isBlank(serialNo)) {
                X509Certificate certificate = PayKit.getCertificate(wxPayV3Bean.getCertPath());
                if (certificate != null) {
                    serialNo = certificate.getSerialNumber().toString(16).toUpperCase();
                }
            }

            // 发起统一下单请求
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.POST,
                    WxDomainEnum.CHINA.toString(),
                    BasePayApiEnum.JS_API_PAY.toString(),
                    wxPayV3Bean.getMchId(),
                    serialNo,
                    null,
                    wxPayV3Bean.getKeyPath(),
                    JSONUtil.toJsonStr(unifiedOrderModel),
                    AuthTypeEnum.RSA.getCode()
            );

            log.info("统一下单响应 {}", response);

            // 解析响应获取prepay_id
            JSON json = JSONUtil.parse(response.getBody());
            return (String) json.getByPath("prepay_id");
        } catch (Exception e) {
            log.error("创建微信支付订单失败", e);
            throw new RuntimeException("创建微信支付订单失败: " + e.getMessage());
        }
    }

    @Override
    public String testRefund(String orderId, Long userId, String refundReason) {
        try {
            // 1. 查找支付订单
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);
            if (payOrder == null) {
                throw new RuntimeException("订单不存在");
            }

            // 2. 验证订单是否属于当前用户
            if (!userId.equals(payOrder.getUserId())) {
                throw new RuntimeException("无权限操作该订单");
            }

            // 3. 验证订单状态
            if (!"SUCCESS".equals(payOrder.getPayStatus())) {
                throw new RuntimeException("订单未支付成功，无法退款");
            }

            // 4. 生成退款订单号
            String refundId = "REFUND_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);

            // 5. 调用微信退款API
            createWxRefund(
                    orderId,
                    refundId,
                    (int) (payOrder.getAmount() * 100), // 退款金额（分）
                    (int) (payOrder.getAmount() * 100), // 原订单金额（分）
                    refundReason != null ? refundReason : "测试退款"
            );

            // 6. 更新订单状态
            payOrder.setPayStatus("REFUND");
            payOrder.setUpdateTime(LocalDateTime.now());
            payOrderMapper.save(payOrder);

            // 7. 如果是储值卡充值订单，需要扣减余额
            if (orderId.startsWith("RECHARGE_")) {
                handleStorageCardRefund(payOrder);
            }

            return "退款成功，退款单号：" + refundId;
        } catch (Exception e) {
            log.error("退款失败", e);
            throw new RuntimeException("退款失败: " + e.getMessage());
        }
    }

    /**
     * 创建微信退款订单
     */
    private String createWxRefund(String outTradeNo, String outRefundNo, int refundAmount, int totalAmount, String reason) {
        try {
            // 构建退款请求参数
            Map<String, Object> refundRequest = new HashMap<>();
            refundRequest.put("out_trade_no", outTradeNo);
            refundRequest.put("out_refund_no", outRefundNo);
            refundRequest.put("reason", reason);

            Map<String, Object> amount = new HashMap<>();
            amount.put("refund", refundAmount);
            amount.put("total", totalAmount);
            amount.put("currency", "CNY");
            refundRequest.put("amount", amount);

            log.info("退款请求参数 {}", JSONUtil.toJsonStr(refundRequest));

            // 获取证书序列号
            if (StringUtils.isBlank(serialNo)) {
                X509Certificate certificate = PayKit.getCertificate(wxPayV3Bean.getCertPath());
                if (certificate != null) {
                    serialNo = certificate.getSerialNumber().toString(16).toUpperCase();
                }
            }

            // 发起退款请求
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.POST,
                    WxDomainEnum.CHINA.toString(),
                    "/v3/refund/domestic/refunds",
                    wxPayV3Bean.getMchId(),
                    serialNo,
                    null,
                    wxPayV3Bean.getKeyPath(),
                    JSONUtil.toJsonStr(refundRequest),
                    AuthTypeEnum.RSA.getCode()
            );

            log.info("退款响应 {}", response);

            if (response.getStatus() == 200) {
                JSON json = JSONUtil.parse(response.getBody());
                return (String) json.getByPath("refund_id");
            } else {
                throw new RuntimeException("微信退款失败: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("创建微信退款订单失败", e);
            throw new RuntimeException("创建微信退款订单失败: " + e.getMessage());
        }
    }

    /**
     * 处理储值卡退款（扣减余额）
     */
    private void handleStorageCardRefund(PayOrder payOrder) {
        try {
            // 根据订单信息查找对应的用户卡（这里需要根据实际业务逻辑调整）
            // 可以在订单中添加userCardId字段，或者通过其他方式关联

            // 示例：查找用户的储值卡并扣减余额
            List<VenueMemberCardUser> userCards = venueMemberCardUserMapper.findByUserIdAndDelFlag(payOrder.getUserId(), "0");
            Optional<VenueMemberCardUser> storageCardOpt = userCards.stream()
                    .filter(card -> card.getCardType() == 1) // 储值卡
                    .findFirst();

            if (storageCardOpt.isPresent()) {
                VenueMemberCardUser storageCard = storageCardOpt.get();
                BigDecimal currentBalance = storageCard.getBalance() != null ? storageCard.getBalance() : BigDecimal.ZERO;
                BigDecimal refundAmount = BigDecimal.valueOf(payOrder.getAmount());

                // 扣减余额
                BigDecimal newBalance = currentBalance.subtract(refundAmount);
                if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
                    newBalance = BigDecimal.ZERO;
                }

                storageCard.setBalance(newBalance);
                storageCard.setUpdateTime(LocalDateTime.now());
                venueMemberCardUserMapper.save(storageCard);

                // 记录退款明细
                VenueMemberCardDetail detail = new VenueMemberCardDetail();
                detail.setUserId(payOrder.getUserId());
                detail.setCardId(storageCard.getCardId());
                detail.setCardName(storageCard.getCardName());
                detail.setUserCardId(storageCard.getUserCardId());
                detail.setVenueId(storageCard.getVenueId());
                detail.setVenueName(storageCard.getVenueName());
                detail.setOperationType(CardOperationType.REFUND.getCode()); // 退款
                detail.setAmount(refundAmount.negate()); // 负数表示扣减
                detail.setOperationTime(LocalDateTime.now());
                detail.setOperationDescription("测试退款");
                detail.setOperator("系统");
                detail.setOperationSource(1); // 管理端
                detail.setBalance(newBalance);
                detail.setDelFlag("0");
                detail.setCreateTime(LocalDateTime.now());
                detail.setUpdateTime(LocalDateTime.now());

                venueMemberCardDetailMapper.save(detail);

                log.info("储值卡退款处理完成，用户ID: {}, 退款金额: {}, 余额: {} -> {}",
                        payOrder.getUserId(), refundAmount, currentBalance, newBalance);
            }
        } catch (Exception e) {
            log.error("处理储值卡退款失败", e);
            // 这里不抛异常，避免影响退款流程
        }
    }

    @Override
    public CardDetailVO getCardDetail(Long cardId) {
        // 1. 查询卡片信息
        VenueMemberCard card = venueMemberCardMapper.findById(cardId).orElse(null);
        if (card == null) {
            throw new RuntimeException("卡片不存在");
        }

        // 2. 查询场馆信息
        Venue venue = venueMapper.findById(card.getVenueId()).orElse(null);
        if (venue == null) {
            throw new RuntimeException("场馆不存在");
        }

        // 3. 构建CardDetailVO
        CardDetailVO vo = new CardDetailVO();
        BeanUtils.copyProperties(card, vo);

        // 设置卡类型名称
        vo.setCardTypeName(getCardTypeName(card.getCardType()));

        // 设置状态名称
        vo.setStatusName(getStatusName(card.getStatus()));

        // 计算总销量
        int fakeSales = card.getInflatedSalesVolume() != null ? card.getInflatedSalesVolume() : 0;
        int realSales = card.getSalesVolume() != null ? card.getSalesVolume() : 0;
        vo.setTotalSales(fakeSales + realSales);

        // 设置场馆信息
        vo.setContactPerson(venue.getContactPerson());
        vo.setContactPhone(venue.getContactPhone());
        vo.setWxCustomerServiceId(venue.getWxCustomerServiceId());
        vo.setBusinessTimeDesc(venue.getBusinessTimeDesc());
        vo.setVenueAddress(venue.getVenueAddress());

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardPurchaseVO purchaseCard(CardPurchaseRequest request, Long userId) {
        // 1. 验证卡片信息
        VenueMemberCard card = venueMemberCardMapper.findById(request.getCardId()).orElse(null);
        if (card == null) {
            throw new RuntimeException("卡片不存在");
        }

        // 2. 验证卡片状态
        if (!"0".equals(card.getStatus())) {
            throw new RuntimeException("卡片已停用，无法购买");
        }

        // 3. 验证是否为储值卡（储值卡不能通过此接口购买）
        if (card.getCardType() == 1) {
            throw new RuntimeException("储值卡请使用充值接口");
        }

        // 4. 从用户表获取openId
        UserInfo userInfo = userInfoMapper.findById(userId).orElse(null);
        if (userInfo == null) {
            throw new RuntimeException("用户不存在");
        }

        String openId = userInfo.getOpenId();
        if (openId == null || openId.isEmpty() || "null".equals(openId)) {
            throw new RuntimeException("用户未绑定微信，请使用微信登录");
        }

        // 5. 计算总金额（购买数量固定为1）
        BigDecimal totalAmount = card.getCardPrice();

        // 6. 生成订单号
        String orderId = generateCardOrderId();

        // 7. 创建venue_order记录
        VenueOrder venueOrder = VenueOrder.builder()
                .orderId(orderId)
                .userId(userId)
                .venueId(card.getVenueId())
                .venueName(card.getVenueName())
                .orderType(OrderStatusConstant.OrderType.CARD) // 卡片购买类型
                .totalAmount(totalAmount)
                .discountAmount(BigDecimal.ZERO)
                .actualAmount(totalAmount)
                .remark("购买" + card.getCardName())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        venueOrderMapper.save(venueOrder);

        // 8. 创建支付订单
        PaymentInfoVO paymentInfo = createCardPayOrder(orderId, totalAmount, card, userId, openId);

        // 9. 构建响应VO
        CardPurchaseVO vo = new CardPurchaseVO();
        vo.setOrderId(orderId);
        vo.setCardId(card.getCardId());
        vo.setCardName(card.getCardName());
        vo.setVenueId(card.getVenueId());
        vo.setVenueName(card.getVenueName());
        vo.setCardType(card.getCardType());
        vo.setCardTypeName(getCardTypeName(card.getCardType()));
        vo.setQuantity(1); // 购买数量固定为1
        vo.setUnitPrice(card.getCardPrice());
        vo.setTotalAmount(totalAmount);
        vo.setUsageCount(card.getUsageCount());
        vo.setValidityType(card.getValidityType());
        vo.setValidityValue(card.getValidityValue());
        vo.setPaymentInfo(paymentInfo);
        vo.setPurchaseTime(LocalDateTime.now());

        return vo;
    }

    /**
     * 获取卡类型名称
     */
    private String getCardTypeName(Integer cardType) {
        if (cardType == null) {
            return "未知";
        }
        switch (cardType) {
            case 1:
                return "储值卡";
            case 2:
                return "计次卡";
            default:
                return "未知";
        }
    }



    /**
     * 生成卡片购买订单号
     */
    private String generateCardOrderId() {
        return "CARD_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    /**
     * 创建卡片购买支付订单
     */
    private PaymentInfoVO createCardPayOrder(String orderId, BigDecimal amount, VenueMemberCard card, Long userId, String openId) {
        try {
            // 1. 保存支付订单到数据库
            PayOrder payOrder = PayOrder.builder()
                    .userId(userId)
                    .openId(openId)
                    .orderId(orderId)
                    .amount(amount.doubleValue())
                    .body("购买" + card.getCardName())
                    .payType("WECHAT")
                    .payStatus("NOTPAY")
                    .cardId(card.getCardId()) // 关联卡片ID
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();

            payOrderMapper.save(payOrder);

            // 2. 创建微信支付订单
            String prepayId = createWxPayOrder(
                    "购买" + card.getCardName(),
                    orderId,
                    amount.multiply(new BigDecimal("100")).intValue(), // 转换为分
                    LocalDateTime.now().plusMinutes(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'")),
                    openId
            );

            // 3. 更新prepayId
            payOrder.setPrepayId(prepayId);
            payOrderMapper.save(payOrder);

            // 4. 构建支付信息VO
            PaymentInfoVO paymentInfo = PaymentInfoVO.builder()
                    .payStatus("NOTPAY")
                    .prepayId(prepayId)
                    .payType("WECHAT")
                    .amount(amount.doubleValue())
                    .body("购买" + card.getCardName())
                    .timeExpire(LocalDateTime.now().plusMinutes(30))
                    .build();

            return paymentInfo;
        } catch (Exception e) {
            log.error("创建卡片购买支付订单失败", e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    @Override
    public PageResultVO<CountCardVO> searchCountCardByPhone(SearchCountCardByPhoneRequest request) {
        try {
            // 1. 先根据手机号查询用户ID列表
            final List<Long> userIds;
            if (request.getPhone() != null && !request.getPhone().trim().isEmpty()) {
                // 使用Specification进行手机号模糊查询
                Specification<UserInfo> userSpec = (root, query, criteriaBuilder) ->
                    criteriaBuilder.like(root.get("phone"), "%" + request.getPhone().trim() + "%");
                List<UserInfo> users = userInfoMapper.findAll(userSpec);
                userIds = users.stream().map(UserInfo::getUserId).collect(Collectors.toList());

                // 如果没有找到匹配的用户，直接返回空结果
                if (userIds.isEmpty()) {
                    return PageResultVO.<CountCardVO>builder()
                            .list(new ArrayList<>())
                            .total(0L)
                            .pageSize(request.getPageSize())
                            .pageNum(request.getPageNum())
                            .pages(0)
                            .hasNextPage(false)
                            .build();
                }
            } else {
                userIds = new ArrayList<>();
            }

            // 2. 构建查询条件
            Specification<VenueMemberCardUser> spec = (root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();

                // 只查询计次卡（cardType = 2）
                predicates.add(criteriaBuilder.equal(root.get("cardType"), 2));

                // 删除标记为0（未删除）
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));

                // 卡状态为正常（0正常）
                predicates.add(criteriaBuilder.equal(root.get("status"), "0"));

                // 剩余次数大于0
                predicates.add(criteriaBuilder.greaterThan(root.get("remainingCount"), 0));

                // 有效期未过期（validityEndTime为null或大于当前时间）
                LocalDateTime now = LocalDateTime.now();
                Predicate validityPredicate = criteriaBuilder.or(
                    criteriaBuilder.isNull(root.get("validityEndTime")),
                    criteriaBuilder.greaterThan(root.get("validityEndTime"), now)
                );
                predicates.add(validityPredicate);

                // 根据用户ID列表筛选
                if (!userIds.isEmpty()) {
                    predicates.add(root.get("userId").in(userIds));
                }

                // 场馆ID筛选（可选）
                if (request.getVenueId() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("venueId"), request.getVenueId()));
                }

                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            };

            // 2. 分页查询
            Pageable pageable = PageRequest.of(request.getPageNum() - 1, request.getPageSize());
            Page<VenueMemberCardUser> page = venueMemberCardUserMapper.findAll(spec, pageable);

            // 3. 转换为VO
            List<CountCardVO> voList = page.getContent().stream()
                    .map(this::convertToCountCardVO)
                    .collect(Collectors.toList());

            // 4. 构建分页结果
            return PageResultVO.<CountCardVO>builder()
                    .list(voList)
                    .total(page.getTotalElements())
                    .pageSize(page.getSize())
                    .pageNum(request.getPageNum())
                    .pages((int) Math.ceil((double) page.getTotalElements() / page.getSize()))
                    .hasNextPage(page.hasNext())
                    .build();
        } catch (Exception e) {
            log.error("按手机号查询计次卡失败", e);
            throw new RuntimeException("查询计次卡失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deductCountCard(DeductCountCardRequest request, String operatorId) {
        try {
            // 1. 查询用户卡信息
            Optional<VenueMemberCardUser> cardOpt = venueMemberCardUserMapper.findById(request.getUserCardId());
            if (!cardOpt.isPresent()) {
                throw new RuntimeException("用户卡不存在");
            }

            VenueMemberCardUser userCard = cardOpt.get();

            // 2. 验证卡类型
            if (userCard.getCardType() != 2) {
                throw new RuntimeException("只能扣减计次卡的次数");
            }

            // 3. 验证卡状态
            if (!"0".equals(userCard.getStatus())) {
                throw new RuntimeException("卡片状态异常，无法扣减次数");
            }

            // 4. 验证有效期
            LocalDateTime now = LocalDateTime.now();
            if (userCard.getValidityEndTime() != null && now.isAfter(userCard.getValidityEndTime())) {
                throw new RuntimeException("卡片已过期，无法扣减次数");
            }

            // 5. 验证剩余次数
            Integer remainingCount = userCard.getRemainingCount();
            if (remainingCount == null || remainingCount < request.getDeductCount()) {
                throw new RuntimeException("剩余次数不足，当前剩余: " + (remainingCount != null ? remainingCount : 0) + " 次");
            }

            // 6. 扣减次数
            userCard.setRemainingCount(remainingCount - request.getDeductCount());
            userCard.setUpdateTime(now);
            venueMemberCardUserMapper.save(userCard);

            // 7. 记录使用明细
            VenueMemberCardDetail detail = new VenueMemberCardDetail();
            detail.setUserId(userCard.getUserId());
            detail.setUserName(userCard.getUserName());
            detail.setCardId(userCard.getCardId());
            detail.setCardName(userCard.getCardName());
            detail.setUserCardId(userCard.getUserCardId());
            detail.setVenueId(userCard.getVenueId());
            detail.setVenueName(userCard.getVenueName());
            detail.setOperationType(CardOperationType.CONSUME.getCode());
            detail.setAmount(BigDecimal.ZERO);
            detail.setUsageCount(request.getDeductCount());
            detail.setOperationTime(now);
            detail.setOperationDescription(request.getRemark() != null ? request.getRemark() : "次数扣减");
            detail.setOperator(operatorId);
            detail.setOperationSource(1); // 1表示管理端操作
            detail.setBalance(userCard.getBalance());
            detail.setRemainingCount(userCard.getRemainingCount());
            detail.setDelFlag("0");
            detail.setCreateTime(now);

            venueMemberCardDetailMapper.save(detail);

            log.info("计次卡扣减成功, 用户卡ID: {}, 扣减次数: {}, 剩余次数: {}, 操作员: {}",
                    request.getUserCardId(), request.getDeductCount(), userCard.getRemainingCount(), operatorId);

            return "扣减成功，剩余次数: " + userCard.getRemainingCount();
        } catch (Exception e) {
            log.error("计次卡扣减失败", e);
            throw new RuntimeException("扣减失败: " + e.getMessage());
        }
    }

    /**
     * 转换为CountCardVO
     */
    private CountCardVO convertToCountCardVO(VenueMemberCardUser card) {
        CountCardVO vo = new CountCardVO();
        vo.setUserCardId(card.getUserCardId());
        vo.setCardNumber(card.getCardNumber());
        vo.setCardName(card.getCardName());
        vo.setUserName(card.getUserName());

        // 通过userId查询用户手机号
        try {
            Optional<UserInfo> userInfoOpt = userInfoMapper.findById(card.getUserId());
            if (userInfoOpt.isPresent()) {
                vo.setUserPhone(userInfoOpt.get().getPhone());
            }
        } catch (Exception e) {
            log.warn("查询用户手机号失败, userId: {}", card.getUserId(), e);
        }

        vo.setVenueId(card.getVenueId());
        vo.setVenueName(card.getVenueName());
        vo.setRemainingCount(card.getRemainingCount());
        vo.setStatus(card.getStatus());
        vo.setStatusName(getStatusName(card.getStatus()));
        vo.setValidityStartTime(card.getValidityStartTime());
        vo.setValidityEndTime(card.getValidityEndTime());
        vo.setCreateTime(card.getCreateTime());

        // 计算总次数和已使用次数
        Integer remainingCount = card.getRemainingCount() != null ? card.getRemainingCount() : 0;
        // 这里需要从卡片模板获取总次数，暂时设为剩余次数（实际应该查询VenueMemberCard表）
        vo.setTotalCount(remainingCount); // 临时设置，实际需要查询卡片模板
        vo.setUsedCount(0); // 临时设置，实际需要计算

        // 判断是否过期
        LocalDateTime now = LocalDateTime.now();
        boolean isExpired = card.getValidityEndTime() != null && now.isAfter(card.getValidityEndTime());
        vo.setIsExpired(isExpired);

        // 判断是否可用（未过期且有剩余次数且状态正常）
        boolean isAvailable = !isExpired && remainingCount > 0 && "0".equals(card.getStatus());
        vo.setIsAvailable(isAvailable);

        return vo;
    }
}
