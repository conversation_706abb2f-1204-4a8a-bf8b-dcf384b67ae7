package com.titan.event.service.impl;

import com.titan.event.entity.CountryCode;
import com.titan.event.enums.PublishStatus;
import com.titan.event.mapper.CountryCodeMapper;
import com.titan.event.service.ICountryCodeService;
import com.titan.event.vo.countryCode.CountryCodeVO;
import com.titan.event.vo.eventVideo.EventVideoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
@Slf4j
public class CountryCodeServiceImpl implements ICountryCodeService {

    @Resource
    private CountryCodeMapper countryCodeMapper;

    @Override
    public List<CountryCodeVO> list(){
        List<CountryCodeVO> countryCodeVOS = new ArrayList<>();
        List<CountryCode> countryCodeList = countryCodeMapper.findAll();
        for (CountryCode countryCode : countryCodeList) {
            countryCodeVOS.add(CountryCodeVO.builder()
                    .id(countryCode.getId())
//                    .isoCountryCode2(countryCode.getIsoCountryCode2())
                    .isoCountryCode(countryCode.getIsoCountryCode())
                    .countryEng(countryCode.getCountryEng())
                    .countryChn(countryCode.getCountryChn())
                    .build());
        }
        // 自定义排序逻辑
        countryCodeVOS.sort((o1, o2) -> {
            // 定义优先级
            int priority1 = getPriority(o1.getCountryChn());
            int priority2 = getPriority(o2.getCountryChn());

            // 如果优先级相同，则按 countryEng 排序
            if (priority1 == priority2) {
                return o1.getCountryEng().compareTo(o2.getCountryEng());
            }
            // 否则按优先级排序
            return Integer.compare(priority1, priority2);
        });

        return countryCodeVOS;
    }

    // 定义优先级逻辑
    private int getPriority(String countryChn) {
        switch (countryChn) {
            case "中国":
                return 1;
            case "中国香港":
                return 2;
            case "中国澳门":
                return 3;
            case "中国台北":
                return 4;
            default:
                return 5; // 其他记录优先级最低
        }
    }

}
