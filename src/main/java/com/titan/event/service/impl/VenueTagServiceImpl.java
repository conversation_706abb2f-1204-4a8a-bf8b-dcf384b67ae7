package com.titan.event.service.impl;

import com.titan.event.entity.VenueTag;
import com.titan.event.mapper.VenueTagMapper;
import com.titan.event.service.IVenueTagService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 标签服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueTagServiceImpl implements IVenueTagService {

    @Resource
    private VenueTagMapper venueTagMapper;

    @Override
    public List<VenueTag> findTagsByVenueId(Long venueId) {
        return venueTagMapper.findTagsByVenueId(venueId);
    }
}