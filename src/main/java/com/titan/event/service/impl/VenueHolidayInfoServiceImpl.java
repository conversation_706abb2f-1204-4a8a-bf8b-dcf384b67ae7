package com.titan.event.service.impl;

import com.titan.event.entity.VenueHolidayInfo;
import com.titan.event.mapper.VenueHolidayInfoMapper;
import com.titan.event.service.IVenueHolidayInfoService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 节假日信息表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueHolidayInfoServiceImpl implements IVenueHolidayInfoService {

    @Resource
    private VenueHolidayInfoMapper holidayInfoMapper;


} 