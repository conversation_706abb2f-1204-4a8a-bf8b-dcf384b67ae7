package com.titan.event.service.impl;

import com.titan.event.entity.VenueHolidayDate;
import com.titan.event.mapper.VenueHolidayDateMapper;
import com.titan.event.service.IVenueHolidayDateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 节假日日期表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueHolidayDateServiceImpl implements IVenueHolidayDateService {

    @Resource
    private VenueHolidayDateMapper holidayDateMapper;


} 