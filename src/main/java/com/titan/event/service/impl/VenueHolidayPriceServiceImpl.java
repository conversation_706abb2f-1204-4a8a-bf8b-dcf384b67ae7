package com.titan.event.service.impl;

import com.titan.event.entity.VenueHolidayPrice;
import com.titan.event.mapper.VenueHolidayPriceMapper;
import com.titan.event.service.IVenueHolidayPriceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 节假日价格表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueHolidayPriceServiceImpl implements IVenueHolidayPriceService {

    @Resource
    private VenueHolidayPriceMapper holidayPriceMapper;
    
}