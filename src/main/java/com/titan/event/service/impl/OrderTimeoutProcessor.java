package com.titan.event.service.impl;

import com.titan.event.constant.RedisKeyConstant;
import com.titan.event.entity.pay.PayOrder;
import com.titan.event.entity.VenueOrder;
import com.titan.event.mapper.PayOrderMapper;
import com.titan.event.mapper.VenueOrderMapper;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 订单超时处理服务
 * 在应用启动时启动一个永久线程监听并处理超时订单
 */
@Service
@Slf4j
public class OrderTimeoutProcessor implements InitializingBean, DisposableBean {

    private static final String ORDER_TIMEOUT_QUEUE_KEY = "venue:order:timeout:queue";
    private ExecutorService executor;
    private volatile boolean running = true;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private VenueOrderServiceImpl venueOrderService;

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Autowired
    private VenueOrderMapper venueOrderMapper;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 创建单线程执行器
        executor = Executors.newSingleThreadExecutor(r -> {
            Thread thread = new Thread(r, "order-timeout-processor");
            thread.setDaemon(true); // 设置为守护线程，应用关闭时会自动退出
            return thread;
        });

        // 启动超时订单处理线程
        executor.submit(this::processTimeoutOrders);
        log.info("超时订单处理服务已启动");
    }

    @Override
    public void destroy() throws Exception {
        running = false;
        if (executor != null) {
            executor.shutdownNow();
            log.info("超时订单处理服务已关闭");
        }
    }

    /**
     * 处理超时订单的主要方法
     * 持续不断地从延迟队列中获取到期的订单并处理
     */
    private void processTimeoutOrders() {
        RQueue<String> queue = redissonClient.getQueue(ORDER_TIMEOUT_QUEUE_KEY);
        log.info("开始监听超时订单队列");

        while (running) {
            try {
                // 从队列中取出元素，如果队列为空则返回null
                String orderId = queue.poll();
                
                if (orderId == null) {
                    // 队列为空，等待一段时间后再尝试
                    try {
                        TimeUnit.SECONDS.sleep(5);
                        continue;
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                
                log.info("从超时队列中获取到订单[{}]，准备处理", orderId);
                
                // 处理超时订单
                processTimeoutOrder(orderId);
            } catch (Exception e) {
                log.error("处理超时订单异常", e);
                // 继续循环处理下一个订单
            }
        }
    }

    /**
     * 处理单个超时订单
     *
     * @param orderId 订单ID
     */
    private void processTimeoutOrder(String orderId) {
        try {
            // 先查询订单状态，确认是否真的要超时处理
            PayOrder payOrder = payOrderMapper.findByOrderId(orderId);

            if (payOrder == null) {
                log.warn("订单[{}]不存在，无法处理超时。创建之后两分钟超时之后应该关闭订单 修改venue_booking和venue_booking_order的booking_status为2取消状态", orderId);
                // PayOrder不存在，但可能存在venue booking记录需要取消
                handleOrphanedVenueBookings(orderId);
                return;
            }

            // 只处理未支付状态的订单
            if (!"NOTPAY".equals(payOrder.getPayStatus())) {
                log.info("订单[{}]当前状态为[{}]，不需要超时处理", orderId, payOrder.getPayStatus());
                return;
            }

            // 调用超时处理方法
            boolean success = venueOrderService.handlePaymentTimeout(orderId);

            if (success) {
                log.info("订单[{}]超时处理成功", orderId);
            } else {
                log.warn("订单[{}]超时处理失败，稍后将由定时任务处理", orderId);
            }

        } catch (Exception e) {
            log.error("处理超时订单[{}]时发生异常", orderId, e);
        }
    }

    /**
     * 处理孤立的场地预订记录
     * 当PayOrder不存在但可能存在venue booking记录时，直接取消这些预订记录
     *
     * @param orderId 订单ID
     */
    private void handleOrphanedVenueBookings(String orderId) {
        try {
            // 查询VenueOrder记录
            List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) ->
                    cb.equal(root.get("orderId"), orderId));

            if (venueOrderList.isEmpty()) {
                log.info("订单[{}]没有对应的VenueOrder记录", orderId);
                return;
            }

            VenueOrder venueOrder = venueOrderList.get(0);

            // 只处理场地预订类型的订单
            if (venueOrder.getOrderType() != 1) { // 1表示场地预订
                log.info("订单[{}]不是场地预订类型，跳过处理", orderId);
                return;
            }

            log.info("开始处理孤立的场地预订记录，订单ID: {}", orderId);

            // 调用VenueOrderService的方法来取消场地预订记录
            boolean success = venueOrderService.cancelOrphanedVenueBookings(orderId);

            if (success) {
                log.info("成功取消孤立的场地预订记录，订单ID: {}", orderId);
            } else {
                log.warn("取消孤立的场地预订记录失败，订单ID: {}", orderId);
            }

        } catch (Exception e) {
            log.error("处理孤立的场地预订记录时发生异常，订单ID: {}", orderId, e);
        }
    }
} 