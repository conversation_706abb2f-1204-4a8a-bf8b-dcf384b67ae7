package com.titan.event.service.impl;

import com.titan.event.dto.price.SiteDatePriceDTO;
import com.titan.event.entity.*;
import com.titan.event.mapper.*;
import com.titan.event.request.venuesite.SiteQueryRequest;
import com.titan.event.service.IVenueBundleBookingService;
import com.titan.event.service.IVenueSiteService;
import com.titan.event.service.IVenueSiteSectionService;
import com.titan.event.vo.site.SitePriceDataVO;
import com.titan.event.vo.site.SiteQueryVO;
import com.titan.event.vo.site.SiteSimpleVO;
import com.titan.event.vo.site.SiteSectionSimpleVO;
import com.titan.event.vo.site.SitePriceVO;
import com.titan.event.vo.site.TimeslotPriceVO;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 场地信息表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class VenueSiteServiceImpl implements IVenueSiteService {

    @Resource
    private VenueSiteMapper siteMapper;

    @Resource
    private IVenueSiteSectionService venueSiteSectionService;

    @Resource
    private VenueSiteSectionMapper venueSiteSectionMapper;

    @Resource
    private VenueHolidayDateMapper venueHolidayDateMapper;

    @Resource
    private VenueSpecialDateMapper venueSpecialDateMapper;

    @Resource
    private VenueTimeslotPriceMapper venueTimeslotPriceMapper;

    @Resource
    private VenueHolidayPriceMapper venueHolidayPriceMapper;

    @Resource
    private VenueSpecialDatePriceMapper venueSpecialDatePriceMapper;

    @Resource
    private VenueBookingOrderMapper venueBookingOrderMapper;

    @Resource
    private VenueBookingMapper venueBookingMapper;

    @Resource
    private IVenueBundleBookingService venueBundleBookingService;

    // 缓存节假日和特殊日期，减少重复查询
    private List<VenueHolidayDate> holidayCache;
    private List<VenueSpecialDate> specialDateCache;
    private Date holidayCacheTime;
    private Date specialDateCacheTime;
    private static final long CACHE_VALID_DURATION = 24 * 60 * 60 * 1000; // 24小时缓存有效期

    /**
     * 根据场地类型和删除标志查询场地列表
     *
     * @param siteType 场地类型
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 场地列表
     */
    @Override
    public List<VenueSite> findBySiteTypeAndDelFlag(Integer siteType, String delFlag) {
        Specification<VenueSite> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加场地类型条件
            if (siteType != null) {
                predicates.add(criteriaBuilder.equal(root.get("siteType"), siteType));
            }

            // 添加删除标志条件
            if (delFlag != null) {
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), delFlag));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return siteMapper.findAll(spec);
    }

    /**
     * 根据场馆ID、场地类型和删除标志查询场地列表
     *
     * @param venueId 场馆ID
     * @param siteType 场地类型
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 场地列表
     */
    public List<VenueSite> findByVenueIdAndSiteTypeAndDelFlag(Long venueId, Integer siteType, String delFlag) {
        Specification<VenueSite> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加场馆ID条件
            if (venueId != null) {
                predicates.add(criteriaBuilder.equal(root.get("venueId"), venueId));
            }

            // 添加场地类型条件
            if (siteType != null) {
                predicates.add(criteriaBuilder.equal(root.get("siteType"), siteType));
            }

            // 添加删除标志条件
            if (delFlag != null) {
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), delFlag));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return siteMapper.findAll(spec);
    }

    /**
     * 根据场地类型和日期查询场地及分区信息
     *
     * @param request 查询请求参数
     * @return 场地及分区信息
     * @throws ParseException 日期解析异常
     */
    @Override
    public SiteQueryVO querySiteByTypeAndDate(SiteQueryRequest request) throws ParseException {
        // 日期格式验证
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date queryDate = sdf.parse(request.getDate());

        // 预先获取日期相关信息，避免重复计算
        boolean isHoliday = isHoliday(request.getDate());
        Long specialDateId = getSpecialDateId(request.getDate());
        boolean isSpecialDate = specialDateId != null;

        // 查询场地信息
        List<VenueSite> siteList = this.findByVenueIdAndSiteTypeAndDelFlag(request.getVenueId(), request.getSiteType(), "0");

        // 构建响应对象
        SiteQueryVO response = new SiteQueryVO();

        // 设置日期相关信息
        response.setDate(request.getDate());
        response.setIsHoliday(isHoliday);
        response.setIsSpecialDate(isSpecialDate);
        if (specialDateId != null) {
            response.setSpecialDateId(specialDateId);
        }

        // 如果没有查询到场地，则直接返回
        if (siteList == null || siteList.isEmpty()) {
            return response;
        }

        // 收集所有场地ID，用于批量查询
        List<Long> siteIds = new ArrayList<>();
        Map<Long, VenueSite> siteMap = new HashMap<>(); // 用于快速查找场地

        // 转换场地信息为简化VO并收集ID和场地状态信息
        List<SiteSimpleVO> simpleSiteList = new ArrayList<>();

        // 预先收集节假日和特殊日期启用状态，避免后续重复查询
        List<Long> holidayEnabledSiteIds = new ArrayList<>();
        List<Long> specialDateEnabledSiteIds = new ArrayList<>();

        for (VenueSite site : siteList) {
            SiteSimpleVO siteVO = new SiteSimpleVO();
            siteVO.setSiteId(site.getSiteId());
            siteVO.setVenueId(site.getVenueId());
            siteVO.setSiteName(site.getSiteName());
            siteVO.setSiteType(site.getSiteType());
            simpleSiteList.add(siteVO);

            siteIds.add(site.getSiteId());
            siteMap.put(site.getSiteId(), site);

            // 预先检查节假日和特殊日期状态，避免后续重复查询
            if (isHoliday && "1".equals(site.getHolidayEnabled())) {
                holidayEnabledSiteIds.add(site.getSiteId());
            }
            if (specialDateId != null && "1".equals(site.getSpecialDateEnabled())) {
                specialDateEnabledSiteIds.add(site.getSiteId());
            }
        }
        response.setSiteList(simpleSiteList);

        // 批量查询所有场地的分区信息
        Map<Long, List<VenueSiteSection>> siteSectionsMap = batchGetSiteSections(siteIds);

        // 转换分区信息为简化VO
        for (Long siteId : siteIds) {
            List<VenueSiteSection> sectionList = siteSectionsMap.getOrDefault(siteId, Collections.emptyList());

            List<SiteSectionSimpleVO> simpleSectionList = new ArrayList<>();
            for (VenueSiteSection section : sectionList) {
                SiteSectionSimpleVO sectionVO = new SiteSectionSimpleVO();
                sectionVO.setSectionId(section.getSectionId());
                sectionVO.setSiteId(section.getSiteId());
                sectionVO.setSectionName(section.getSectionName());
                sectionVO.setSectionCode(section.getSectionCode());
                simpleSectionList.add(sectionVO);
            }

            response.getSiteSections().put(siteId, simpleSectionList);
        }

        // 批量查询所有场地的价格信息（已优化）
        Map<Long, List<SitePriceVO>> priceDataMap = batchGetSitePricesByDate(
                siteIds,
                siteSectionsMap,
                request.getDate(),
                isHoliday,
                specialDateId,
                holidayEnabledSiteIds,
                specialDateEnabledSiteIds,
                siteMap);
        response.setPriceData(priceDataMap);

        // 查询捆绑订场信息并添加到响应中
        Map<Long, List<SitePriceVO>> bundleBookingDataMap = getBundleBookingDataByDate(
                request.getVenueId(),
                request.getSiteType(),
                siteIds,
                request.getDate(),
                siteMap);
        response.setBundleBookingData(bundleBookingDataMap);

        return response;
    }

    /**
     * 批量获取捆绑订场数据
     *
     * @param venueId 场馆ID
     * @param siteType 场地类型
     * @param siteIds 场地ID列表
     * @param date 查询日期
     * @param siteMap 场地映射，用于快速查找场地信息
     * @return 捆绑订场数据
     */
    private Map<Long, List<SitePriceVO>> getBundleBookingDataByDate(
            Long venueId, Integer siteType, List<Long> siteIds, String date, Map<Long, VenueSite> siteMap) {
        Map<Long, List<SitePriceVO>> result = new HashMap<>();

        try {
            // 解析查询日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date queryDate = sdf.parse(date);

            // 获取星期几 (1-7对应周一到周日)
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(queryDate);
            final int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY ? 7 : calendar.get(Calendar.DAY_OF_WEEK) - 1;

            // 查询日期范围内的捆绑订场记录
            List<VenueBundleBooking> bundleBookings = venueBundleBookingService.selectVenueBundleBookingsByDateRange(
                    venueId, queryDate, queryDate);

            if (bundleBookings.isEmpty()) {
                return result;
            }

            // 过滤出与当前场地类型匹配的订场记录
            bundleBookings = bundleBookings.stream()
                    .filter(booking -> booking.getSiteType().equals(siteType))
                    .filter(booking -> {
                        // 检查是否包含当天（星期几）
                        String weekDaysStr = booking.getWeekDays();
                        if (weekDaysStr == null || weekDaysStr.isEmpty()) {
                            return false;
                        }

                        String[] weekDays = weekDaysStr.split(",");
                        for (String day : weekDays) {
                            if (Integer.parseInt(day) == dayOfWeek) {
                                return true;
                            }
                        }
                        return false;
                    })
                    .collect(Collectors.toList());

            // 处理每个捆绑订场记录
            for (VenueBundleBooking booking : bundleBookings) {
                // 解析场地ID列表
                String siteIdListStr = booking.getSiteIdList();
                if (siteIdListStr == null || siteIdListStr.isEmpty()) {
                    continue;
                }

                // 过滤只包含当前场地列表的记录
                List<Long> bookingSiteIds = Arrays.stream(siteIdListStr.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                // 检查是否存在交集
                boolean hasIntersection = false;
                for (Long id : bookingSiteIds) {
                    if (siteIds.contains(id)) {
                        hasIntersection = true;
                        break;
                    }
                }

                if (!hasIntersection) {
                    continue;
                }

                // 过滤出当前查询的场地ID
                List<Long> filteredSiteIds = bookingSiteIds.stream()
                        .filter(siteIds::contains)
                        .collect(Collectors.toList());

                if (filteredSiteIds.isEmpty()) {
                    continue;
                }

                // 处理时间段
                String timeRangesStr = booking.getTimeRanges();
                if (timeRangesStr == null || timeRangesStr.isEmpty()) {
                    continue;
                }

                String[] timeRanges = timeRangesStr.split(",");

                // 为每个场地创建捆绑订场的价格数据
                for (Long siteId : filteredSiteIds) {
                    VenueSite site = siteMap.get(siteId);
                    if (site == null) {
                        continue;
                    }

                    // 创建价格数据
                    SitePriceVO sitePrice = new SitePriceVO();
                    sitePrice.setSiteId(siteId);
                    sitePrice.setSiteName(site.getSiteName());
                    sitePrice.setDate(date);
                    sitePrice.setType("bundle"); // 标记为捆绑订场
                    sitePrice.setSectionName("捆绑订场"); // 捆绑订场没有特定分区

                    List<TimeslotPriceVO> prices = new ArrayList<>();

                    // 为每个时间段创建价格数据
                    for (String timeRange : timeRanges) {
                        String[] parts = timeRange.split("-");
                        if (parts.length != 2) {
                            continue;
                        }

                        // 解析时间
                        String startTime = parts[0]; // 格式：HH:mm
                        String endTime = parts[1];   // 格式：HH:mm

                        // 获取小时部分
                        int startHour = Integer.parseInt(startTime.split(":")[0]);

                        // 创建时段价格VO
                        TimeslotPriceVO timeslotPrice = new TimeslotPriceVO();
                        timeslotPrice.setTimeSlot(startHour);
                        timeslotPrice.setTimeslotPrice(new BigDecimal("-1")); // 使用-1表示捆绑订场
                        timeslotPrice.setPriceSource(4); // 4表示捆绑订场来源
                        timeslotPrice.setTimeDisplay(startTime + " ~ " + endTime);
                        timeslotPrice.setStart(startTime);
                        timeslotPrice.setEnd(endTime);
                        timeslotPrice.setSourceDisplay("bundle"); // 捆绑订场
                        timeslotPrice.setIsExpired(isTimeSlotExpired(date, startHour));
                        timeslotPrice.setBookingStatus("booked"); // 捆绑订场已被预订

                        prices.add(timeslotPrice);
                    }

                    // 对时间段排序
                    prices.sort(Comparator.comparing(TimeslotPriceVO::getTimeSlot));
                    sitePrice.setPrices(prices);

                    // 添加到结果集
                    if (!result.containsKey(siteId)) {
                        result.put(siteId, new ArrayList<>());
                    }
                    result.get(siteId).add(sitePrice);
                }
            }

        } catch (Exception e) {
            // 记录异常但不影响主流程
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 批量获取场地分区信息
     *
     * @param siteIds 场地ID列表
     * @return 场地分区信息，key为场地ID，value为分区列表
     */
    private Map<Long, List<VenueSiteSection>> batchGetSiteSections(List<Long> siteIds) {
        if (siteIds == null || siteIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<Long, List<VenueSiteSection>> result = new HashMap<>();

        // 优化：使用适合的方式查询指定场地ID的分区
        List<VenueSiteSection> allSections;

        // 判断是否支持JpaSpecificationExecutor
        if (venueSiteSectionMapper instanceof JpaSpecificationExecutor) {
            // 使用Specification方式查询
            Specification<VenueSiteSection> spec = (root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();

                // 添加未删除条件
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));

                // 添加场地ID条件
                predicates.add(root.get("siteId").in(siteIds));

                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            };

            allSections = ((JpaSpecificationExecutor<VenueSiteSection>) venueSiteSectionMapper).findAll(spec);
        } else {
            // 如果不支持Specification，使用循环查询
            VenueSiteSection querySection = new VenueSiteSection();
            querySection.setDelFlag("0");
            // 循环查询每个场地的分区
            allSections = new ArrayList<>();
            for (Long siteId : siteIds) {
                querySection.setSiteId(siteId);
                List<VenueSiteSection> sections = venueSiteSectionMapper.selectVenueSiteSectionList(querySection);
                if (sections != null) {
                    allSections.addAll(sections);
                }
            }
        }

        // 按场地ID分组
        for (VenueSiteSection section : allSections) {
            result.computeIfAbsent(section.getSiteId(), k -> new ArrayList<>()).add(section);
        }

        return result;
    }

    /**
     * 批量获取场地及分区价格数据
     *
     * @param siteIds 场地ID列表
     * @param siteSectionsMap 场地分区映射表
     * @param date 日期
     * @param isHoliday 是否节假日
     * @param specialDateId 特殊日期ID
     * @param holidayEnabledSiteIds 启用节假日价格的场地ID列表
     * @param specialDateEnabledSiteIds 启用特殊日期价格的场地ID列表
     * @param siteMap 场地ID到场地对象的映射
     * @return 场地价格数据，key为场地ID，value为价格数据列表
     */
    private Map<Long, List<SitePriceVO>> batchGetSitePricesByDate(
            List<Long> siteIds,
            Map<Long, List<VenueSiteSection>> siteSectionsMap,
            String date,
            boolean isHoliday,
            Long specialDateId,
            List<Long> holidayEnabledSiteIds,
            List<Long> specialDateEnabledSiteIds,
            Map<Long, VenueSite> siteMap) {

        if (siteIds == null || siteIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 解析日期，获取星期几
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int dayOfWeek = localDate.getDayOfWeek().getValue(); // 1-7 对应周一到周日

        // 批量查询全场常规价格（全场和分区使用不同的sectionId标识）
        List<VenueTimeslotPrice> allFullCourtRegularPrices = batchQueryRegularPrices(siteIds, null, dayOfWeek);

        // 批量查询半场常规价格
        List<VenueTimeslotPrice> allHalfCourtRegularPrices = batchQueryRegularPrices(siteIds, 1L, dayOfWeek);

        // 转换为易于查找的Map: siteId -> 价格列表
        Map<Long, List<VenueTimeslotPrice>> fullCourtRegularPriceMap = new HashMap<>();
        Map<Long, List<VenueTimeslotPrice>> halfCourtRegularPriceMap = new HashMap<>();

        for (VenueTimeslotPrice price : allFullCourtRegularPrices) {
            fullCourtRegularPriceMap.computeIfAbsent(price.getSiteId(), k -> new ArrayList<>()).add(price);
        }

        for (VenueTimeslotPrice price : allHalfCourtRegularPrices) {
            halfCourtRegularPriceMap.computeIfAbsent(price.getSiteId(), k -> new ArrayList<>()).add(price);
        }

        // 批量查询节假日价格
        Map<Long, Map<Long, List<VenueHolidayPrice>>> allHolidayPrices = Collections.emptyMap();
        if (isHoliday && !holidayEnabledSiteIds.isEmpty()) {
            allHolidayPrices = batchQueryHolidayPrices(holidayEnabledSiteIds, date);
        }

        // 批量查询特殊日期价格
        Map<Long, Map<Integer, List<VenueSpecialDatePrice>>> allSpecialPrices = Collections.emptyMap();
        if (specialDateId != null && !specialDateEnabledSiteIds.isEmpty()) {
            allSpecialPrices = batchQuerySpecialDatePrices(specialDateEnabledSiteIds, specialDateId);
        }

        // 构建结果
        Map<Long, List<SitePriceVO>> result = new HashMap<>();

        // 整合全场和分区价格数据
        for (Long siteId : siteIds) {
            List<SitePriceVO> sitePriceDataList = new ArrayList<>();

            // 获取场地信息 - 使用传入的siteMap避免重复查询
            VenueSite site = siteMap.get(siteId);
            if (site == null) {
                continue;
            }

            // 构建全场价格数据
            SiteDatePriceDTO fullCourtPriceDTO = buildPriceDTO(
                    siteId, null, site.getSiteName(), date,
                    fullCourtRegularPriceMap.getOrDefault(siteId, Collections.emptyList()),
                    allHolidayPrices.getOrDefault(siteId, Collections.emptyMap()).getOrDefault(0L, Collections.emptyList()),
                    allSpecialPrices.getOrDefault(siteId, Collections.emptyMap()).getOrDefault(0, Collections.emptyList())
            );

            if (fullCourtPriceDTO != null) {
                ensureAllTimeslotsHaveValues(fullCourtPriceDTO);
                SitePriceVO fullCourtPriceVO = createSitePriceVO(fullCourtPriceDTO, "全场", "site");
                sitePriceDataList.add(fullCourtPriceVO);
            }

            // 获取该场地的分区列表
            List<VenueSiteSection> sections = siteSectionsMap.getOrDefault(siteId, Collections.emptyList());

            // 如果有分区，添加分区价格
            if (!sections.isEmpty()) {
                // 所有分区使用相同的价格数据，只需计算一次
                SiteDatePriceDTO halfCourtPriceDTO = buildPriceDTO(
                        siteId, 1L, site.getSiteName(), date,
                        halfCourtRegularPriceMap.getOrDefault(siteId, Collections.emptyList()),
                        allHolidayPrices.getOrDefault(siteId, Collections.emptyMap()).getOrDefault(1L, Collections.emptyList()),
                        allSpecialPrices.getOrDefault(siteId, Collections.emptyMap()).getOrDefault(1, Collections.emptyList())
                );

                if (halfCourtPriceDTO != null) {
                    ensureAllTimeslotsHaveValues(halfCourtPriceDTO);

                    // 为每个分区创建价格数据
                    for (VenueSiteSection section : sections) {
                        // 创建半场价格DTO的副本，并设置正确的分区ID
                        SiteDatePriceDTO sectionPriceDTO = new SiteDatePriceDTO();
                        sectionPriceDTO.setSiteId(halfCourtPriceDTO.getSiteId());
                        sectionPriceDTO.setSiteName(halfCourtPriceDTO.getSiteName());
                        // 使用实际的分区ID而不是固定的1L
                        sectionPriceDTO.setSectionId(section.getSectionId());
                        sectionPriceDTO.setDate(halfCourtPriceDTO.getDate());
                        // 复制价格数据
                        sectionPriceDTO.getTimeslotPrices().putAll(halfCourtPriceDTO.getTimeslotPrices());
                        sectionPriceDTO.getPriceSource().putAll(halfCourtPriceDTO.getPriceSource());

                        SitePriceVO sectionPriceVO = createSitePriceVO(
                                sectionPriceDTO,
                                section.getSectionName(),
                                "section"
                        );
                        sectionPriceVO.setSectionId(section.getSectionId());
                        sectionPriceVO.setSectionCode(section.getSectionCode());
                        sitePriceDataList.add(sectionPriceVO);
                    }
                }
            }

            // 添加到结果集
            if (!sitePriceDataList.isEmpty()) {
                result.put(siteId, sitePriceDataList);
            }
        }

        return result;
    }

    /**
     * 批量查询常规价格
     *
     * @param siteIds 场地ID列表
     * @param sectionId 分区ID，null表示全场
     * @param dayOfWeek 星期几(1-7)
     * @return 价格列表
     */
    private List<VenueTimeslotPrice> batchQueryRegularPrices(List<Long> siteIds, Long sectionId, int dayOfWeek) {
        if (siteIds == null || siteIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 如果没有批量查询能力，则使用单次循环查询并合并结果
        List<VenueTimeslotPrice> result = new ArrayList<>();

        // 设置查询条件
        Map<String, Object> params = new HashMap<>();
        if (sectionId == null) {
            params.put("queryFullCourt", true);
        }

        for (Long siteId : siteIds) {
            VenueTimeslotPrice query = new VenueTimeslotPrice();
            query.setSiteId(siteId);
            query.setSectionId(sectionId);
            query.setWeekDays(dayOfWeek);
            query.setParams(params);

            List<VenueTimeslotPrice> prices = venueTimeslotPriceMapper.selectVenueTimeslotPriceList(query);
            if (prices != null) {
                result.addAll(prices);
            }
        }

        return result;
    }

    /**
     * 批量查询节假日价格
     *
     * @param siteIds 场地ID列表
     * @param date 日期
     * @return 价格映射，key为siteId，value为(sectionId -> 价格列表)的映射
     */
    private Map<Long, Map<Long, List<VenueHolidayPrice>>> batchQueryHolidayPrices(List<Long> siteIds, String date) {
        if (siteIds == null || siteIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 结果映射：siteId -> (sectionId -> 价格列表)
        Map<Long, Map<Long, List<VenueHolidayPrice>>> result = new HashMap<>();

        // 由于没有批量查询方法，使用循环方式查询但是逻辑优化
        for (Long siteId : siteIds) {
            Map<Long, List<VenueHolidayPrice>> sectionPrices = new HashMap<>();

            // 全场价格（sectionId = 0）
            List<VenueHolidayPrice> fullCourtPrices = venueHolidayPriceMapper.selectByDateAndSite(siteId, 0L, date);
            if (fullCourtPrices != null && !fullCourtPrices.isEmpty()) {
                sectionPrices.put(0L, fullCourtPrices);
            }

            // 半场价格（sectionId = 1）
            List<VenueHolidayPrice> halfCourtPrices = venueHolidayPriceMapper.selectByDateAndSite(siteId, 1L, date);
            if (halfCourtPrices != null && !halfCourtPrices.isEmpty()) {
                sectionPrices.put(1L, halfCourtPrices);
            }

            if (!sectionPrices.isEmpty()) {
                result.put(siteId, sectionPrices);
            }
        }

        return result;
    }

    /**
     * 批量查询特殊日期价格
     *
     * @param siteIds 场地ID列表
     * @param specialDateId 特殊日期ID
     * @return 价格映射，key为siteId，value为(siteSection -> 价格列表)的映射
     */
    private Map<Long, Map<Integer, List<VenueSpecialDatePrice>>> batchQuerySpecialDatePrices(List<Long> siteIds, Long specialDateId) {
        if (siteIds == null || siteIds.isEmpty() || specialDateId == null) {
            return Collections.emptyMap();
        }

        // 结果映射：siteId -> (siteSection -> 价格列表)
        Map<Long, Map<Integer, List<VenueSpecialDatePrice>>> result = new HashMap<>();

        // 查询所有特殊日期价格 - 使用基本查询
        VenueSpecialDatePrice query = new VenueSpecialDatePrice();
        query.setSpecialDateId(specialDateId);

        // 查询此特殊日期的所有价格记录
        List<VenueSpecialDatePrice> allPrices = venueSpecialDatePriceMapper.selectVenueSpecialDatePriceList(query);

        // 按siteId和siteSection分组，只包含请求的场地ID
        for (VenueSpecialDatePrice price : allPrices) {
            if (siteIds.contains(price.getSiteId())) {
                result
                        .computeIfAbsent(price.getSiteId(), k -> new HashMap<>())
                        .computeIfAbsent(price.getSiteSection(), k -> new ArrayList<>())
                        .add(price);
            }
        }

        return result;
    }

    /**
     * 构建价格数据DTO
     *
     * @param siteId 场地ID
     * @param sectionId 分区ID
     * @param siteName 场地名称
     * @param date 日期
     * @param regularPrices 常规价格列表
     * @param holidayPrices 节假日价格列表
     * @param specialPrices 特殊日期价格列表
     * @return 价格数据DTO
     */
    private SiteDatePriceDTO buildPriceDTO(
            Long siteId,
            Long sectionId,
            String siteName,
            String date,
            List<VenueTimeslotPrice> regularPrices,
            List<VenueHolidayPrice> holidayPrices,
            List<VenueSpecialDatePrice> specialPrices) {

        SiteDatePriceDTO result = new SiteDatePriceDTO();
        result.setSiteId(siteId);
        result.setSiteName(siteName);
        result.setDate(date);
        result.setSectionId(sectionId);

        // 处理常规价格
        for (VenueTimeslotPrice price : regularPrices) {
            String startTime = price.getStartTime();
            if (startTime != null && startTime.length() >= 2) {
                try {
                    int startHour = Integer.parseInt(startTime.substring(0, 2));
                    result.setTimeslotPrice(startHour, price.getPrice(), 1); // 1 表示常规价格
                } catch (Exception e) {
                    // 解析失败，忽略此价格项
                    e.printStackTrace();
                }
            }
        }

        // 处理节假日价格
        for (VenueHolidayPrice holidayPrice : holidayPrices) {
            if (holidayPrice.getTimeSlot() != null) {
                Integer timeSlotId = parseTimeSlot(holidayPrice.getTimeSlot());
                if (timeSlotId != null) {
                    result.setTimeslotPrice(timeSlotId, holidayPrice.getPrice(), 2); // 2 表示节假日价格
                }
            }
        }

        // 处理特殊日期价格
        for (VenueSpecialDatePrice specialPrice : specialPrices) {
            if (specialPrice.getTimeSlot() != null) {
                Integer timeSlotId = parseTimeSlot(specialPrice.getTimeSlot());
                if (timeSlotId != null) {
                    result.setTimeslotPrice(timeSlotId, specialPrice.getPrice(), 3); // 3 表示特殊日期价格
                }
            }
        }

        return result;
    }

    /**
     * 检查给定时段是否已过期
     *
     * @param date 日期
     * @param timeSlot 时段（0-23表示0点到23点）
     * @return 是否已过期
     */
    private boolean isTimeSlotExpired(String date, Integer timeSlot) {
        try {
            // 获取当前日期和时间
            LocalDate currentDate = LocalDate.now();
            LocalTime currentTime = LocalTime.now();

            // 解析查询日期
            LocalDate queryDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 构建时段的结束时间
            LocalTime slotEndTime = LocalTime.of((timeSlot + 1) % 24, 0);  // 例如：时段0的结束时间是01:00

            // 如果查询日期早于当前日期，则时段已过期
            if (queryDate.isBefore(currentDate)) {
                return true;
            }

            // 如果是当天，则需要比较具体时间
            if (queryDate.isEqual(currentDate)) {
                // 如果当前时间已经超过了时段的结束时间，则时段已过期
                return currentTime.isAfter(slotEndTime);
            }

            // 如果查询日期在当前日期之后，则时段未过期
            return false;
        } catch (Exception e) {
            // 解析异常，默认为未过期
            return false;
        }
    }

    /**
     * 将价格Map转换为价格VO列表
     *
     * @param timeslotPrices 时段价格Map
     * @param priceSource 价格来源Map
     * @param queryDate 查询日期，格式YYYY-MM-DD
     * @return 统一格式的价格VO列表
     */
    private List<TimeslotPriceVO> convertPriceMapToTimeslotPriceVOList(Map<Integer, BigDecimal> timeslotPrices, Map<Integer, Integer> priceSource, String queryDate) {
        List<TimeslotPriceVO> result = new ArrayList<>();

        for (Map.Entry<Integer, BigDecimal> entry : timeslotPrices.entrySet()) {
            Integer timeSlot = entry.getKey();
            BigDecimal price = entry.getValue();
            Integer source = priceSource.get(timeSlot);

            TimeslotPriceVO priceInfo = new TimeslotPriceVO();
            priceInfo.setTimeSlot(timeSlot);
            priceInfo.setTimeslotPrice(price);
            priceInfo.setPriceSource(source);

            // 添加时间段显示名称
            String timeDisplay = String.format("%02d:00 ~ %02d:00", timeSlot, (timeSlot + 1) % 24);
            priceInfo.setTimeDisplay(timeDisplay);

            // 添加开始时间和结束时间
            String startTime = String.format("%02d:00", timeSlot);
            String endTime = String.format("%02d:00", (timeSlot + 1) % 24);
            priceInfo.setStart(startTime);
            priceInfo.setEnd(endTime);

            // 添加价格来源显示名称（保留英文）
            String sourceDisplay;
            switch (source) {
                case 1:
                    sourceDisplay = "regular";
                    break;
                case 2:
                    sourceDisplay = "holiday";
                    break;
                case 3:
                    sourceDisplay = "special";
                    break;
                default:
                    sourceDisplay = "none";
                    break;
            }
            priceInfo.setSourceDisplay(sourceDisplay);

            // 使用查询日期而非当前系统日期判断时间段是否过期
            boolean isExpired = isTimeSlotExpired(queryDate, timeSlot);
            priceInfo.setIsExpired(isExpired);

            // 添加默认的预订状态（可预订，使用英文）
            priceInfo.setBookingStatus("available");

            result.add(priceInfo);
        }

        // 按时段排序
        Collections.sort(result, (o1, o2) -> {
            Integer timeSlot1 = o1.getTimeSlot();
            Integer timeSlot2 = o2.getTimeSlot();
            return timeSlot1.compareTo(timeSlot2);
        });

        return result;
    }

    /**
     * 创建场地价格VO
     *
     * @param priceDTO 价格数据DTO
     * @param sectionName 分区名称
     * @param type 价格类型（site或section）
     * @return 价格数据VO
     */
    private SitePriceVO createSitePriceVO(SiteDatePriceDTO priceDTO, String sectionName, String type) {
        SitePriceVO result = new SitePriceVO();
        result.setSiteId(priceDTO.getSiteId());
        result.setSiteName(priceDTO.getSiteName());
        result.setSectionId(priceDTO.getSectionId());
        result.setSectionName(sectionName);
        result.setDate(priceDTO.getDate());
        result.setType(type);

        // 转换价格数据格式，传递查询日期
        List<TimeslotPriceVO> prices = convertPriceMapToTimeslotPriceVOList(priceDTO.getTimeslotPrices(), priceDTO.getPriceSource(), priceDTO.getDate());

        // 查询预订信息并更新预订状态，确保遵循"分区被预定则全场不可预订"规则
        updateBookingStatus(priceDTO.getSiteId(), priceDTO.getSectionId(), priceDTO.getDate(), prices);

        result.setPrices(prices);

        return result;
    }

    /**
     * 更新价格数据中的预订状态
     *
     * @param siteId 场地ID
     * @param sectionId 分区ID，null表示全场
     * @param date 日期
     * @param prices 价格数据列表
     */
    private void updateBookingStatus(Long siteId, Long sectionId, String date, List<TimeslotPriceVO> prices) {
        try {
            // 解析日期
            LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 判断当前是查询全场还是分区
            boolean isQueryingFullCourt = (sectionId == null);

            // 查询该场地所有预订（不限制sectionId）
            List<VenueBooking> allBookings = venueBookingMapper.findAll((root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("siteId"), siteId));
                // VenueBooking中bookingDate是Date类型，需要格式化进行比较
                predicates.add(cb.equal(cb.function("DATE_FORMAT", String.class, root.get("bookingDate"), cb.literal("%Y-%m-%d")), date));

                // 排除已取消的预订（bookingStatus = 2)
                predicates.add(cb.notEqual(root.get("bookingStatus"), 2));

                return cb.and(predicates.toArray(new Predicate[0]));
            });

            // 无预订记录，所有时段均可预订
            if (allBookings == null || allBookings.isEmpty()) {
                return;
            }

            // 获取所有分区ID，用于全场预订状态判断
            List<VenueSiteSection> allSections = venueSiteSectionMapper.findAll((root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("siteId"), siteId));
                predicates.add(cb.equal(root.get("delFlag"), "0"));
                return cb.and(predicates.toArray(new Predicate[0]));
            });

            Set<Long> allSectionIds = new HashSet<>();
            if (allSections != null && !allSections.isEmpty()) {
                for (VenueSiteSection section : allSections) {
                    allSectionIds.add(section.getSectionId());
                }
            }

            // 遍历每个时段，检查预订状态
            for (TimeslotPriceVO priceInfo : prices) {
                String startTimeStr = priceInfo.getStart();
                String endTimeStr = priceInfo.getEnd();

                // 解析时间
                LocalTime startTime = LocalTime.parse(startTimeStr);
                LocalTime endTime = LocalTime.parse(endTimeStr);

                // 根据查询类型（全场或分区）应用不同的判断逻辑
                if (isQueryingFullCourt) {
                    updateFullCourtBookingStatus(priceInfo, allBookings, allSectionIds, startTime, endTime);
                } else {
                    updateSectionBookingStatus(priceInfo, allBookings, sectionId, startTime, endTime);
                }
            }
        } catch (Exception e) {
            // 解析异常，使用默认状态
            e.printStackTrace();
        }
    }

    /**
     * 更新全场预订状态
     * 全场预订状态：两种情况导致不可预订
     * 1. 已有全场预订
     * 2. 任一分区被预订
     */
    private void updateFullCourtBookingStatus(TimeslotPriceVO priceInfo, List<VenueBooking> allBookings,
                                              Set<Long> allSectionIds, LocalTime startTime, LocalTime endTime) {
        // 检查是否有全场预订
        boolean hasFullCourtBooking = false;
        for (VenueBooking booking : allBookings) {
            // 只检查全场预订（sectionId为null）
            if (booking.getSectionId() == null) {
                // VenueBooking中的时间是String类型，需要先转换为LocalTime再比较
                LocalTime bookingStartTime = LocalTime.parse(booking.getStartTime());
                LocalTime bookingEndTime = LocalTime.parse(booking.getEndTime());

                if (bookingStartTime.equals(startTime) && bookingEndTime.equals(endTime)) {
                    hasFullCourtBooking = true;
                    break;
                }
            }
        }

        // 情况1：已有全场预订
        if (hasFullCourtBooking) {
            priceInfo.setBookingStatus("booked");
            return;
        }

        // 如果没有分区，或者分区数为0，则直接判断为可预订
        if (allSectionIds.isEmpty()) {
            priceInfo.setBookingStatus("available");
            return;
        }

        // 情况2：检查是否有任一分区被预订
        Set<Long> bookedSectionIds = new HashSet<>();
        for (VenueBooking booking : allBookings) {
            if (booking.getSectionId() != null) {
                // VenueBooking中的时间是String类型，需要先转换为LocalTime再比较
                LocalTime bookingStartTime = LocalTime.parse(booking.getStartTime());
                LocalTime bookingEndTime = LocalTime.parse(booking.getEndTime());

                if (bookingStartTime.equals(startTime) && bookingEndTime.equals(endTime)) {
                    bookedSectionIds.add(booking.getSectionId());
                }
            }
        }

        // 如果有任一分区被预订，则全场不可预订
        if (!bookedSectionIds.isEmpty()) {
            priceInfo.setBookingStatus("booked");
        } else {
            priceInfo.setBookingStatus("available");
        }
    }

    /**
     * 更新分区预订状态
     * 分区预订状态：两种情况导致不可预订
     * 1. 对应分区被预订
     * 2. 已有对应时段的全场预订
     */
    private void updateSectionBookingStatus(TimeslotPriceVO priceInfo, List<VenueBooking> allBookings,
                                            Long sectionId, LocalTime startTime, LocalTime endTime) {
        boolean isBooked = false;

        for (VenueBooking booking : allBookings) {
            // VenueBooking中的时间是String类型，需要先转换为LocalTime再比较
            LocalTime bookingStartTime = LocalTime.parse(booking.getStartTime());
            LocalTime bookingEndTime = LocalTime.parse(booking.getEndTime());

            // 检查时间是否完全匹配
            if (bookingStartTime.equals(startTime) && bookingEndTime.equals(endTime)) {
                // 情况1：对应分区被预订
                if (sectionId.equals(booking.getSectionId())) {
                    isBooked = true;
                    break;
                }
                // 情况2：已有全场预订
                if (booking.getSectionId() == null) {
                    isBooked = true;
                    break;
                }
            }
        }

        priceInfo.setBookingStatus(isBooked ? "booked" : "available");
    }

    /**
     * 确保所有时段都有价格值
     * 对于未设置的时段，设置价格为-1和来源为0
     *
     * @param priceDTO 价格数据对象
     */
    private void ensureAllTimeslotsHaveValues(SiteDatePriceDTO priceDTO) {
        // 确保所有24个小时时段都有价格值
        for (int i = 0; i < 24; i++) {
            if (!priceDTO.getTimeslotPrices().containsKey(i)) {
                priceDTO.setTimeslotPrice(i, new BigDecimal(-1), 0);
            }
        }
    }

    /**
     * 获取场地及其所有分区在指定日期的价格数据
     *
     * @param siteId 场地ID
     * @param date 日期，格式YYYY-MM-DD
     * @return 场地及分区价格数据，包含site、sections和priceData
     */
    @Override
    public Map<String, Object> getSiteAndSectionsPricesByDate(Long siteId, String date) {
        // 创建结果Map
        Map<String, Object> result = new HashMap<>();

        // 获取场地信息
        VenueSite site = siteMapper.selectVenueSiteBySiteId(siteId);
        if (site == null) {
            return result;
        }

        // 设置日期信息
        result.put("date", date);

        // 创建场地基本信息Map
        Map<String, Object> siteInfo = new HashMap<>();
        siteInfo.put("siteId", site.getSiteId());
        siteInfo.put("siteName", site.getSiteName());
        siteInfo.put("venueId", site.getVenueId());
        siteInfo.put("siteType", site.getSiteType());
        siteInfo.put("siteStatus", site.getSiteStatus());
        siteInfo.put("isSplitAllowed", site.getIsSplitAllowed());
        siteInfo.put("maxCapacity", site.getMaxCapacity());
        siteInfo.put("holidayEnabled", site.getHolidayEnabled());
        siteInfo.put("specialDateEnabled", site.getSpecialDateEnabled());

        // 设置场地信息
        result.put("site", siteInfo);

        // 查询分区信息
        VenueSiteSection querySection = new VenueSiteSection();
        querySection.setSiteId(siteId);
        querySection.setDelFlag("0");
        List<VenueSiteSection> sections = venueSiteSectionMapper.selectVenueSiteSectionList(querySection);

        List<Map<String, Object>> sectionsList = new ArrayList<>();

        // 处理分区信息
        for (VenueSiteSection section : sections) {
            Map<String, Object> sectionMap = new HashMap<>();

            // 分区基本信息
            sectionMap.put("sectionId", section.getSectionId());
            sectionMap.put("sectionName", section.getSectionName());
            sectionMap.put("sectionCode", section.getSectionCode());
            sectionMap.put("status", section.getStatus());
            sectionMap.put("seatCount", section.getSeatCount());
            sectionMap.put("orderNum", section.getOrderNum());

            // 添加分区信息到列表
            sectionsList.add(sectionMap);
        }

        // 设置分区列表
        result.put("sections", sectionsList);

        // 使用优化的批量查询方法获取价格数据
        Map<Long, List<SitePriceVO>> priceDataMap = batchGetSitePricesByDate(
                Collections.singletonList(siteId),
                Collections.singletonMap(siteId, sections),
                date,
                false,
                null,
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyMap()
        );

        List<SitePriceVO> priceData = priceDataMap.getOrDefault(siteId, Collections.emptyList());

        // 设置价格数据
        result.put("priceData", priceData);

        return result;
    }

    /**
     * 获取场地分区在指定日期的所有时段价格
     *
     * @param siteId 场地ID
     * @param sectionId 分区ID，可为null
     * @param date 日期，格式YYYY-MM-DD
     * @return 分区价格信息
     */
    @Override
    public SiteDatePriceDTO getSiteSectionPricesByDate(Long siteId, Long sectionId, String date) {
        // 获取场地基本信息
        VenueSite site = siteMapper.selectVenueSiteBySiteId(siteId);
        if (site == null) {
            return null;
        }

        // 解析日期，获取星期几
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int dayOfWeek = localDate.getDayOfWeek().getValue(); // 1-7 对应周一到周日

        // 检查日期类型：是否为特殊日期或节假日
        boolean isHoliday = isHoliday(date);
        Long specialDateId = getSpecialDateId(date);

        // 设置查询条件
        boolean isQueryingFullCourt = (sectionId == null);
        Map<String, Object> params = new HashMap<>();
        if (isQueryingFullCourt) {
            params.put("queryFullCourt", true);
        }

        // 查询常规价格
        VenueTimeslotPrice queryTimeslot = new VenueTimeslotPrice();
        queryTimeslot.setSiteId(siteId);
        queryTimeslot.setSectionId(isQueryingFullCourt ? null : 1L);
        queryTimeslot.setWeekDays(dayOfWeek);
        queryTimeslot.setParams(params);

        List<VenueTimeslotPrice> regularPrices = venueTimeslotPriceMapper.selectVenueTimeslotPriceList(queryTimeslot);

        // 准备节假日价格
        List<VenueHolidayPrice> holidayPrices = Collections.emptyList();
        if (isHoliday && "1".equals(site.getHolidayEnabled())) {
            // 在holiday_price表中，0表示全场，1表示半场
            Long adjustedSectionId = isQueryingFullCourt ? 0L : 1L;
            holidayPrices = venueHolidayPriceMapper.selectByDateAndSite(siteId, adjustedSectionId, date);
        }

        // 准备特殊日期价格
        List<VenueSpecialDatePrice> specialPrices = Collections.emptyList();
        if (specialDateId != null && "1".equals(site.getSpecialDateEnabled())) {
            // 在special_date_price表中，0表示全场，1表示半场
            int adjustedSiteSection = isQueryingFullCourt ? 0 : 1;

            VenueSpecialDatePrice query = new VenueSpecialDatePrice();
            query.setSiteId(siteId);
            query.setSpecialDateId(specialDateId);
            query.setSiteSection(adjustedSiteSection);

            specialPrices = venueSpecialDatePriceMapper.selectVenueSpecialDatePriceList(query);
        }

        // 获取分区名称
        String sectionName = null;
        if (sectionId != null) {
            VenueSiteSection section = venueSiteSectionMapper.selectVenueSiteSectionBySectionId(sectionId);
            if (section != null) {
                sectionName = section.getSectionName();
            }
        }

        // 构建和返回价格DTO
        return buildPriceDTO(siteId, sectionId, site.getSiteName(), date, regularPrices, holidayPrices, specialPrices);
    }

    /**
     * 判断日期是否为法定节假日
     *
     * @param date 日期，格式YYYY-MM-DD
     * @return 是否为法定节假日
     */
    @Override
    public boolean isHoliday(String date) {
        try {
            // 将字符串日期转为 Date 对象
            Date targetDate = new SimpleDateFormat("yyyy-MM-dd").parse(date);

            // 获取缓存的节假日数据，或者重新查询
            List<VenueHolidayDate> holidays = getHolidayDates();

            // 检查目标日期是否在任一节假日的范围内
            if (holidays != null && !holidays.isEmpty()) {
                for (VenueHolidayDate holiday : holidays) {
                    // 获取节假日的开始日期和结束日期
                    Date startDate = holiday.getStartDate();
                    Date endDate = holiday.getEndDate();

                    // 检查目标日期是否在节假日范围内
                    if (startDate != null && endDate != null) {
                        // 检查目标日期是否在开始日期和结束日期范围内（含边界）
                        if (!targetDate.before(startDate) && !targetDate.after(endDate)) {
                            return true; // 日期在节假日范围内
                        }
                    } else if (startDate != null) {
                        // 如果只有开始日期，检查是否为同一天
                        if (DateUtils.isSameDay(targetDate, startDate)) {
                            return true; // 日期等于节假日开始日期
                        }
                    }
                }
            }
            return false; // 不在任何节假日范围内
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取节假日数据，优先使用缓存
     * 只返回当年的节假日数据
     */
    private List<VenueHolidayDate> getHolidayDates() {
        Date now = new Date();
        // 检查缓存是否有效
        if (holidayCache == null || holidayCacheTime == null ||
                (now.getTime() - holidayCacheTime.getTime() > CACHE_VALID_DURATION)) {

            // 获取当年的开始和结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            int year = calendar.get(Calendar.YEAR);

            Date startOfYear = null;
            Date endOfYear = null;
            try {
                // 设置当年的1月1日为开始日期
                startOfYear = new SimpleDateFormat("yyyy-MM-dd").parse(year + "-01-01");

                // 设置当年的12月31日为结束日期
                endOfYear = new SimpleDateFormat("yyyy-MM-dd").parse(year + "-12-31");
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 构建查询条件，只查询当年的节假日
            final Date finalStartOfYear = startOfYear;
            final Date finalEndOfYear = endOfYear;
            if (finalStartOfYear != null && finalEndOfYear != null) {
                // 直接使用JPA Specification通过数据库查询筛选，提高效率
                Specification<VenueHolidayDate> spec = (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    // 状态为有效
                    predicates.add(criteriaBuilder.equal(root.get("status"), "0"));

                    // 开始日期 <= 当年结束日期
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("startDate"), finalEndOfYear));

                    // 结束日期为null或结束日期 >= 当年开始日期
                    Predicate endDateIsNull = criteriaBuilder.isNull(root.get("endDate"));
                    Predicate endDateGteStartOfYear = criteriaBuilder.greaterThanOrEqualTo(root.get("endDate"), finalStartOfYear);
                    predicates.add(criteriaBuilder.or(endDateIsNull, endDateGteStartOfYear));

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };

                holidayCache = venueHolidayDateMapper.findAll(spec);
            } else {
                // 如果日期解析失败，回退到默认查询所有有效状态的节假日
                VenueHolidayDate query = new VenueHolidayDate();
                query.setStatus("0");
                holidayCache = venueHolidayDateMapper.selectVenueHolidayDateList(query);
            }

            holidayCacheTime = now;
        }
        return holidayCache;
    }

    /**
     * 判断日期是否为特殊日期，并返回特殊日期ID
     *
     * @param date 日期，格式YYYY-MM-DD
     * @return 特殊日期ID，如果不是特殊日期则返回null
     */
    @Override
    public Long getSpecialDateId(String date) {
        try {
            // 将字符串日期转为 Date 对象
            Date targetDate = new SimpleDateFormat("yyyy-MM-dd").parse(date);

            // 获取缓存的特殊日期数据，或者重新查询
            List<VenueSpecialDate> specialDates = getSpecialDates();

            if (specialDates != null && !specialDates.isEmpty()) {
                for (VenueSpecialDate specialDateObj : specialDates) {
                    // 检查特殊日期是否有效（状态为0表示有效）
                    if (!"0".equals(specialDateObj.getStatus())) {
                        continue;
                    }

                    // 检查日期是否在特殊日期范围内
                    Date startDate = specialDateObj.getSpecialDate();
                    Date endDate = specialDateObj.getEndDate();

                    if (startDate != null && endDate != null) {
                        // 检查目标日期是否在开始日期和结束日期范围内（含边界）
                        if (!targetDate.before(startDate) && !targetDate.after(endDate)) {
                            return specialDateObj.getSpecialDateId();
                        }
                    } else if (startDate != null) {
                        // 如果没有结束日期，只判断是否等于开始日期
                        if (DateUtils.isSameDay(targetDate, startDate)) {
                            return specialDateObj.getSpecialDateId();
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取特殊日期数据，优先使用缓存
     * 只返回当年的特殊日期数据
     */
    private List<VenueSpecialDate> getSpecialDates() {
        Date now = new Date();
        // 检查缓存是否有效
        if (specialDateCache == null || specialDateCacheTime == null ||
                (now.getTime() - specialDateCacheTime.getTime() > CACHE_VALID_DURATION)) {

            // 获取当年的开始和结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            int year = calendar.get(Calendar.YEAR);

            Date startOfYear = null;
            Date endOfYear = null;
            try {
                // 设置当年的1月1日为开始日期
                startOfYear = new SimpleDateFormat("yyyy-MM-dd").parse(year + "-01-01");

                // 设置当年的12月31日为结束日期
                endOfYear = new SimpleDateFormat("yyyy-MM-dd").parse(year + "-12-31");
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 构建查询条件，只查询当年的特殊日期
            final Date finalStartOfYear = startOfYear;
            final Date finalEndOfYear = endOfYear;
            if (finalStartOfYear != null && finalEndOfYear != null) {
                // 直接使用JPA Specification通过数据库查询筛选，提高效率
                Specification<VenueSpecialDate> spec = (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    // 状态为有效且未删除
                    predicates.add(criteriaBuilder.equal(root.get("status"), "0"));
                    predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));

                    // 特殊日期 <= 当年结束日期
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("specialDate"), finalEndOfYear));

                    // 结束日期为null或结束日期 >= 当年开始日期
                    Predicate endDateIsNull = criteriaBuilder.isNull(root.get("endDate"));
                    Predicate endDateGteStartOfYear = criteriaBuilder.greaterThanOrEqualTo(root.get("endDate"), finalStartOfYear);
                    predicates.add(criteriaBuilder.or(endDateIsNull, endDateGteStartOfYear));

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };

                specialDateCache = venueSpecialDateMapper.findAll(spec);
            } else {
                // 如果日期解析失败，回退到默认查询所有有效且未删除的特殊日期
                VenueSpecialDate query = new VenueSpecialDate();
                query.setStatus("0");
                query.setDelFlag("0");
                specialDateCache = venueSpecialDateMapper.selectVenueSpecialDateList(query);
            }

            specialDateCacheTime = now;
        }
        return specialDateCache;
    }

    /**
     * 解析时间段字符串，获取时段ID
     * @param timeSlot 时间段字符串，如 "00:00 ~ 01:00" 或 单独的开始时间如 "00:00"
     * @return 时段ID，通常是开始时间的小时值(0-23)
     */
    private Integer parseTimeSlot(String timeSlot) {
        if (timeSlot == null || timeSlot.isEmpty()) {
            return null;
        }

        try {
            // 首先尝试分割 "HH:MM ~ HH:MM" 格式
            if (timeSlot.contains("~")) {
                String[] parts = timeSlot.split("~");
                if (parts.length == 2) {
                    String startTime = parts[0].trim();
                    if (startTime.length() >= 2) {
                        return Integer.parseInt(startTime.substring(0, 2));
                    }
                }
            }
            // 其次尝试分割 "HH:MM-HH:MM" 格式
            else if (timeSlot.contains("-")) {
                String[] parts = timeSlot.split("-");
                if (parts.length == 2) {
                    String startTime = parts[0].trim();
                    if (startTime.length() >= 2) {
                        return Integer.parseInt(startTime.substring(0, 2));
                    }
                }
            }
            // 最后尝试直接解析单一时间格式 "HH:MM"
            else if (timeSlot.contains(":")) {
                if (timeSlot.length() >= 2) {
                    return Integer.parseInt(timeSlot.substring(0, 2));
                }
            }

            return null;
        } catch (Exception e) {
            // 如果解析失败，尝试使用静态映射表
            Map<String, Integer> timeSlotMapping = new HashMap<>();
            // 00:00到23:00的映射
            for (int i = 0; i < 24; i++) {
                String hour = String.format("%02d", i);
                String nextHour = String.format("%02d", (i + 1) % 24);

                // 支持多种格式
                timeSlotMapping.put(hour + ":00 ~ " + nextHour + ":00", i);
                timeSlotMapping.put(hour + ":00-" + nextHour + ":00", i);
                timeSlotMapping.put(hour + ":00", i);
            }

            return timeSlotMapping.getOrDefault(timeSlot, null);
        }
    }
} 