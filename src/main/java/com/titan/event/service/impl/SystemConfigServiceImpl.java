package com.titan.event.service.impl;

import com.titan.event.entity.SystemConfig;
import com.titan.event.mapper.SystemConfigMapper;
import com.titan.event.service.ISystemConfigService;
import com.titan.event.vo.systemConfig.SystemConfigVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
public class SystemConfigServiceImpl implements ISystemConfigService {

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Override
    public SystemConfigVO findByConfigKeyAndConfigType(String key, String type) {
        SystemConfigVO systemConfigVO = new SystemConfigVO();
        SystemConfig systemConfig = systemConfigMapper.findByConfigKeyAndConfigType(key, type);
        BeanUtils.copyProperties(systemConfig,systemConfigVO);
        return systemConfigVO;
    }
}
