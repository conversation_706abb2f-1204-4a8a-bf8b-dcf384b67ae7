package com.titan.event.service.impl;

import com.titan.event.entity.VenueSiteSection;
import com.titan.event.mapper.VenueSiteSectionMapper;
import com.titan.event.service.IVenueSiteSectionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;

/**
 * 场地分区表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueSiteSectionServiceImpl implements IVenueSiteSectionService {

    @Resource
    private VenueSiteSectionMapper sectionMapper;

    /**
     * 根据场地ID和删除标志查询分区列表
     * 
     * @param siteId 场地ID
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 分区列表
     */
    @Override
    public List<VenueSiteSection> findBySiteIdAndDelFlag(Long siteId, String delFlag) {
        Specification<VenueSiteSection> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 添加场地ID条件
            if (siteId != null) {
                predicates.add(criteriaBuilder.equal(root.get("siteId"), siteId));
            }
            
            // 添加删除标志条件
            if (delFlag != null) {
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), delFlag));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return sectionMapper.findAll(spec);
    }
} 