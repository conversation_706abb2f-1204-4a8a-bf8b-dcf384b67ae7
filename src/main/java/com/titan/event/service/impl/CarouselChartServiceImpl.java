package com.titan.event.service.impl;

import com.titan.event.entity.EventCarousel;
import com.titan.event.mapper.CarouselChartMapper;
import com.titan.event.request.carouselchart.CarouselChartBaseRequest;
import com.titan.event.service.ICarouselChartService;
import com.titan.event.vo.carouselchart.CarouselChartVO;
import com.titan.event.config.CacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 14:28
 */
@Service
@Slf4j
public class CarouselChartServiceImpl implements ICarouselChartService {

    @Resource
    private CarouselChartMapper carouselChartMapper;

    @Override
    public List<CarouselChartVO> getList() {
        List<CarouselChartVO> carouselChartVOS = new ArrayList<>();
        CarouselChartBaseRequest carouselChartBaseRequest = new CarouselChartBaseRequest();
        carouselChartBaseRequest.setShowStatus(0);
        Sort sort = Sort.by(Sort.Order.asc("sort"));
        List<EventCarousel> eventCarousels = carouselChartMapper.findAll(this.build(carouselChartBaseRequest),sort);
        eventCarousels = eventCarousels.stream().sorted(Comparator.comparing(EventCarousel::getSort)).collect(Collectors.toList());
        for (EventCarousel eventCarousel : eventCarousels) {
            CarouselChartVO carouselChartVO = new CarouselChartVO();
            BeanUtils.copyProperties(eventCarousel, carouselChartVO);
            carouselChartVOS.add(carouselChartVO);
        }
        return carouselChartVOS;
    }


    private Specification<EventCarousel> build(CarouselChartBaseRequest carouselChartBaseRequest) {
        return (root, cquery, cbuild) -> {
            List<Predicate> predicates = new ArrayList<>();
            // showStatus
            if(carouselChartBaseRequest.getShowStatus()!=null) {
                predicates.add(cbuild.equal(root.get("showStatus"), carouselChartBaseRequest.getShowStatus()));
            }
            Predicate[] p = predicates.toArray(new Predicate[predicates.size()]);
            return p.length == 0 ? null : p.length == 1 ? p[0] : cbuild.and(p);
        };
    }

}
