package com.titan.event.service.impl;

import com.titan.event.entity.VenueMemberCard;
import com.titan.event.entity.VenueMemberCardUser;
import com.titan.event.mapper.VenueMemberCardMapper;
import com.titan.event.mapper.VenueMemberCardUserMapper;
import com.titan.event.service.IVenueMemberCardService;
import com.titan.event.vo.venue.VenueCardGroupVO;
import com.titan.event.vo.venue.VenueCardVO;
import com.titan.event.vo.venue.VenueDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 场馆会员卡服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class VenueMemberCardServiceImpl implements IVenueMemberCardService {
    
    @Resource
    private VenueMemberCardMapper venueMemberCardMapper;
    
    @Resource
    private VenueMemberCardUserMapper venueMemberCardUserMapper;
    
    @Override
    public List<VenueCardGroupVO> getVenueCardsByVenueId(Long venueId, Long userId) {
        // 查询场馆的所有会员卡
        List<VenueMemberCard> cards = findCardsByVenueId(venueId);
        if (cards.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 如果传入了用户ID，查询用户的储值卡信息
        Map<Long, VenueMemberCardUser> userStorageCardsMap = new HashMap<>();
        if (userId != null) {
            List<VenueMemberCardUser> userStorageCards = findUserStorageCardsByVenueId(venueId, userId);
            userStorageCardsMap = userStorageCards.stream()
                    .collect(Collectors.toMap(VenueMemberCardUser::getCardId, card -> card, (existing, replacement) -> existing));
        }
        
        // 转换为VO并按卡类型分组
        Map<Integer, List<VenueCardVO>> cardGroupMap = new LinkedHashMap<>();
        
        for (VenueMemberCard card : cards) {
            VenueCardVO cardVO = new VenueCardVO();
            BeanUtils.copyProperties(card, cardVO);
            
            // 设置卡类型名称
            cardVO.setCardTypeName(getCardTypeName(card.getCardType()));
            
            // 计算总销量
            int fakeSales = card.getInflatedSalesVolume() != null ? card.getInflatedSalesVolume() : 0;
            int realSales = card.getSalesVolume() != null ? card.getSalesVolume() : 0;
            cardVO.setTotalSales(fakeSales + realSales);
            
            // 如果是储值卡且用户已登录，设置用户卡信息
            if (card.getCardType() == 1 && userId != null) { // 1表示储值卡
                VenueMemberCardUser userCard = userStorageCardsMap.get(card.getCardId());
                if (userCard != null) {
                    cardVO.setHasCard(true);
                    cardVO.setUserCardBalance(userCard.getBalance());
                    cardVO.setUserCardNumber(userCard.getCardNumber());
                } else {
                    cardVO.setHasCard(false);
                }
            }
            
            // 按卡类型分组
            cardGroupMap.computeIfAbsent(card.getCardType(), k -> new ArrayList<>()).add(cardVO);
        }
        
        // 转换为分组VO列表
        List<VenueCardGroupVO> result = new ArrayList<>();
        for (Map.Entry<Integer, List<VenueCardVO>> entry : cardGroupMap.entrySet()) {
            VenueCardGroupVO groupVO = new VenueCardGroupVO();
            groupVO.setCardType(entry.getKey());
            groupVO.setCardTypeName(getCardTypeName(entry.getKey()));
            groupVO.setCards(entry.getValue());
            result.add(groupVO);
        }
        
        return result;
    }
    
    @Override
    public List<VenueMemberCard> findCardsByVenueId(Long venueId) {
        return venueMemberCardMapper.findByVenueIdAndDelFlag(venueId, "0");
    }
    
    @Override
    public List<VenueMemberCardUser> findUserStorageCardsByVenueId(Long venueId, Long userId) {
        // 查询用户在该场馆的储值卡（cardType = 1）
        List<VenueMemberCardUser> allUserCards = venueMemberCardUserMapper.findByVenueIdAndUserIdAndDelFlag(venueId, userId, "0");
        return allUserCards.stream()
                .filter(card -> card.getCardType() == 1) // 只返回储值卡
                .collect(Collectors.toList());
    }
    
    @Override
    public String getCardTypeName(Integer cardType) {
        if (cardType == null) {
            return "未知";
        }
        switch (cardType) {
            case 1:
                return "储值卡";
            case 2:
                return "计次卡";
            default:
                return "未知";
        }
    }

    @Override
    public List<VenueCardVO> getStorageCardsByVenueId(Long venueId) {
        // 查询场馆的储值卡（cardType = 1）
        List<VenueMemberCard> cards = venueMemberCardMapper.findByVenueIdAndDelFlag(venueId, "0");
        return cards.stream()
                .filter(card -> card.getCardType() == 1) // 只返回储值卡
                .map(this::convertToVenueCardVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<VenueCardVO> getTimesCardsByVenueId(Long venueId) {
        // 查询场馆的计次卡（cardType = 2）
        List<VenueMemberCard> cards = venueMemberCardMapper.findByVenueIdAndDelFlag(venueId, "0");
        return cards.stream()
                .filter(card -> card.getCardType() == 2) // 只返回计次卡
                .map(this::convertToVenueCardVO)
                .collect(Collectors.toList());
    }

    @Override
    public VenueDetailVO.UserStorageCardVO getUserStorageCard(Long venueId, Long userId) {
        if (userId == null) {
            return null;
        }

        // 查询用户在该场馆的储值卡（每个场馆每个用户只有一个储值卡）
        List<VenueMemberCardUser> userCards = venueMemberCardUserMapper.findByVenueIdAndUserIdAndDelFlag(venueId, userId, "0");
        Optional<VenueMemberCardUser> storageCardOpt = userCards.stream()
                .filter(card -> card.getCardType() == 1) // 只查找储值卡
                .findFirst();

        if (storageCardOpt.isPresent()) {
            VenueMemberCardUser userCard = storageCardOpt.get();
            VenueDetailVO.UserStorageCardVO userStorageCardVO = new VenueDetailVO.UserStorageCardVO();
            userStorageCardVO.setUserCardId(userCard.getUserCardId());
            userStorageCardVO.setCardId(userCard.getCardId());
            userStorageCardVO.setCardName(userCard.getCardName());
            userStorageCardVO.setCardNumber(userCard.getCardNumber());
            userStorageCardVO.setBalance(userCard.getBalance());
            userStorageCardVO.setValidityStartTime(userCard.getValidityStartTime());
            userStorageCardVO.setValidityEndTime(userCard.getValidityEndTime());
            userStorageCardVO.setStatus(userCard.getStatus());
            return userStorageCardVO;
        }

        return null;
    }

    /**
     * 将VenueMemberCard转换为VenueCardVO
     */
    private VenueCardVO convertToVenueCardVO(VenueMemberCard card) {
        VenueCardVO cardVO = new VenueCardVO();
        BeanUtils.copyProperties(card, cardVO);

        // 设置卡类型名称
        cardVO.setCardTypeName(getCardTypeName(card.getCardType()));

        // 计算总销量
        int fakeSales = card.getInflatedSalesVolume() != null ? card.getInflatedSalesVolume() : 0;
        int realSales = card.getSalesVolume() != null ? card.getSalesVolume() : 0;
        cardVO.setTotalSales(fakeSales + realSales);

        return cardVO;
    }
}
