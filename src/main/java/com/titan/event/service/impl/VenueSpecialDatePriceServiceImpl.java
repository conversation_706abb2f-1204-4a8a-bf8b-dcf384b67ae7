package com.titan.event.service.impl;

import com.titan.event.entity.VenueSpecialDate;
import com.titan.event.entity.VenueSpecialDatePrice;
import com.titan.event.mapper.VenueSpecialDateMapper;
import com.titan.event.mapper.VenueSpecialDatePriceMapper;
import com.titan.event.service.IVenueSpecialDatePriceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 特殊日期价格表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueSpecialDatePriceServiceImpl implements IVenueSpecialDatePriceService {

    @Resource
    private VenueSpecialDatePriceMapper specialDatePriceMapper;
    
    @Resource
    private VenueSpecialDateMapper specialDateMapper;


} 