package com.titan.event.service.impl;

import com.titan.event.entity.VenueTimeslotPrice;
import com.titan.event.mapper.VenueTimeslotPriceMapper;
import com.titan.event.service.IVenueTimeslotPriceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 时段价格表 服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class VenueTimeslotPriceServiceImpl implements IVenueTimeslotPriceService {

    @Resource
    private VenueTimeslotPriceMapper timeslotPriceMapper;


} 