package com.titan.event.service;

import com.titan.event.request.userinfo.*;
import com.titan.event.response.LoginResponse;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;

import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/27 09:45
 */
public interface IUserInfoService {

    UserInfoVO getByUserId(Long userId);

    int updateUserInfo(Long userId,UserUpdateRequest userUpdateRequest);

    Result<LoginResponse> touristsLogin(TouristsLoginRequest touristsLoginRequest);

    Result<LoginResponse> wxLogin(WxLoginRequest wxLoginRequest);

    Result<LoginResponse> wxPhone(WxPhoneRequest wxPhoneRequest);

}
