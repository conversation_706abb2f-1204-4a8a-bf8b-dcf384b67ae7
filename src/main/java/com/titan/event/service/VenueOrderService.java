package com.titan.event.service;

import com.titan.event.request.ProductOrderRequest;
import com.titan.event.request.VenueBookingRequest;
import com.titan.event.request.VenueOrderListRequest;
import com.titan.event.request.VenueOrderRefundRequest;
import com.titan.event.request.VenueOrderVerifyRequest;
import com.titan.event.request.VenueRefundOrderListRequest;
import com.titan.event.request.VenueOrderPayRequest;
import com.titan.event.response.CreateOrderResponse;
import com.titan.event.response.VenueOrderPayResponse;
import com.titan.event.vo.PageResultVO;
import com.titan.event.vo.Result;
import com.titan.event.vo.VenueOrderDetailVO;
import com.titan.event.vo.VenueOrderListVO;
import com.titan.event.vo.VenueRefundOrderListVO;

/**
 * 场馆订单服务接口
 */
public interface VenueOrderService {

    /**
     * 创建场地预订订单
     *
     * @param request 场地预订请求
     * @return 订单创建响应
     */
    CreateOrderResponse createBookingOrder(VenueBookingRequest request);

    /**
     * 创建商品购买订单
     *
     * @param request 商品购买请求
     * @return 订单创建响应
     */
    CreateOrderResponse createProductOrder(ProductOrderRequest request);
    
    /**
     * 场馆订单退款
     *
     * @param request 退款请求
     * @return 退款处理结果
     */
    Result refundOrder(VenueOrderRefundRequest request);

    /**
     * 场馆订单退款
     * @param request
     * @return
     */
    Result refundApiOrder(VenueOrderRefundRequest request);

    /**
     * 查询订单详情
     * 支持通过订单ID或核销码查询
     *
     * @param orderId 订单ID，可为空
     * @param verificationCode 核销码，可为空
     * @param refundId 退款ID，可为空
     * @return 订单详情，包含订单状态(1-待支付, 2-待核销, 3-已完成, 4-已作废)和使用须知等信息
     */
    VenueOrderDetailVO getOrderDetail(String orderId, String verificationCode, String refundId);
    
    /**
     * 查询订单列表
     * 根据指定状态查询当前用户的订单，并按创建时间倒序排列
     *
     * @param request 查询请求，包含状态和分页参数
     * @return 订单列表分页结果
     */
    PageResultVO<VenueOrderListVO> getOrderList(VenueOrderListRequest request);
    
    /**
     * 查询退单列表
     * 
     * @param request 查询条件（包含退款状态）
     * @return 退单列表分页结果
     */
    PageResultVO<VenueRefundOrderListVO> getRefundOrderList(VenueRefundOrderListRequest request);
    
    /**
     * 核销订单
     * 通过核销码验证并完成订单核销，记录核销人员ID
     *
     * @param request 包含核销码的请求
     * @param operatorId 操作人员ID
     * @return 核销结果
     */
    Result verifyOrder(VenueOrderVerifyRequest request, String operatorId);
    
    /**
     * 通过API核销订单
     * 通过核销码和安全密钥验证并完成订单核销
     *
     * @param request 包含核销码和密钥的请求
     * @return 核销结果
     */
    Result verifyApiOrder(VenueOrderVerifyRequest request);

    /**
     * 订单支付
     * 支持微信支付和储值卡支付两种方式
     *
     * @param request 支付请求，包含订单ID、支付方式、储值卡ID等
     * @return 支付处理结果
     */
    VenueOrderPayResponse payOrder(VenueOrderPayRequest request);
}