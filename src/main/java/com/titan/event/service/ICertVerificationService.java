package com.titan.event.service;

/**
 * 身份证二要素核验服务接口
 * <AUTHOR>
 */
public interface ICertVerificationService {
    
    /**
     * 身份证二要素核验
     * @param certName 姓名
     * @param certNo 身份证号
     * @return 验证是否通过
     */
    boolean certNoTwoElementVerification(String certName, String certNo);

    /**
     * 身份证二要素核验，并更新档案验证状态
     * @param certName 姓名
     * @param certNo 身份证号
     * @param profileId 档案ID
     * @return 验证是否通过
     */
    boolean certNoTwoElementVerification(String certName, String certNo, Long profileId);
} 