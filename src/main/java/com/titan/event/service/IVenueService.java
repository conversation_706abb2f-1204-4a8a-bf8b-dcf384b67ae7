package com.titan.event.service;

import com.titan.event.entity.Venue;
import com.titan.event.request.venue.VenueDetailRequest;
import com.titan.event.request.venue.VenueListRequest;
import com.titan.event.response.venue.VenueDetailResponse;
import com.titan.event.response.venue.VenueListResponse;
import com.titan.event.response.venue.VenueSettingResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

/**
 * 场馆服务接口
 * 
 * <AUTHOR>
 */
public interface IVenueService {
    
    /**
     * 根据ID查询场馆
     * 
     * @param venueId 场馆ID
     * @return 场馆
     */
    Venue findVenueById(Long venueId);
    
    /**
     * 根据条件分页查询场馆
     * 
     * @param spec 查询条件
     * @param pageable 分页参数
     * @return 场馆分页数据
     */
    Page<Venue> findVenues(Specification<Venue> spec, Pageable pageable);
    
    /**
     * 获取场馆配置信息
     * 
     * @return 场馆配置信息
     */
    VenueSettingResponse getVenueSetting();
    
    /**
     * 分页查询场馆列表
     * 
     * @param request 查询参数
     * @return 场馆列表数据
     */
    VenueListResponse getVenueList(VenueListRequest request);
    
    /**
     * 获取场馆详情
     * 
     * @param request 场馆ID
     * @return 场馆详情
     */
    VenueDetailResponse getVenueDetail(VenueDetailRequest request);
} 