package com.titan.event.service;

import com.titan.event.dto.price.SiteDatePriceDTO;
import com.titan.event.entity.VenueSite;
import com.titan.event.request.venuesite.SiteQueryRequest;
import com.titan.event.vo.site.SiteQueryVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 场地信息表 服务层
 * 
 * <AUTHOR>
 */
public interface IVenueSiteService {
    
    /**
     * 根据场地类型和删除标志查询场地列表
     * 
     * @param siteType 场地类型
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 场地列表
     */
    List<VenueSite> findBySiteTypeAndDelFlag(Integer siteType, String delFlag);
    
    /**
     * 根据场地类型和日期查询场地及分区信息
     * 
     * @param request 查询请求参数
     * @return 场地及分区信息
     * @throws ParseException 日期解析异常
     */
    SiteQueryVO querySiteByTypeAndDate(SiteQueryRequest request) throws ParseException;
    
    /**
     * 获取场地及其所有分区在指定日期的价格数据
     * 
     * @param siteId 场地ID
     * @param date 日期，格式YYYY-MM-DD
     * @return 场地及分区价格数据，包含site、sections和priceData
     */
    Map<String, Object> getSiteAndSectionsPricesByDate(Long siteId, String date);
    
    /**
     * 判断日期是否为法定节假日
     * 
     * @param date 日期，格式YYYY-MM-DD
     * @return 是否为法定节假日
     */
    boolean isHoliday(String date);
    
    /**
     * 判断日期是否为特殊日期，并返回特殊日期ID
     * 
     * @param date 日期，格式YYYY-MM-DD
     * @return 特殊日期ID，如果不是特殊日期则返回null
     */
    Long getSpecialDateId(String date);
    
    /**
     * 获取场地分区在指定日期的所有时段价格
     * 
     * @param siteId 场地ID
     * @param sectionId 分区ID，可为null
     * @param date 日期，格式YYYY-MM-DD
     * @return 分区价格信息
     */
    SiteDatePriceDTO getSiteSectionPricesByDate(Long siteId, Long sectionId, String date);
}