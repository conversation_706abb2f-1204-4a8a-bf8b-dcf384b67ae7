package com.titan.event.service;

import com.titan.event.request.usercard.CardPurchaseRequest;
import com.titan.event.request.usercard.DeductCountCardRequest;
import com.titan.event.request.usercard.SearchCountCardByPhoneRequest;
import com.titan.event.request.usercard.StorageCardRechargeRequest;
import com.titan.event.request.usercard.UpdateSubCardsRequest;
import com.titan.event.request.usercard.UserCardDetailRequest;
import com.titan.event.request.usercard.UserCardListRequest;
import com.titan.event.request.usercard.UserCardUsageDetailRequest;
import com.titan.event.vo.PageResultVO;
import com.titan.event.vo.usercard.CardDetailVO;
import com.titan.event.vo.usercard.CardPurchaseVO;
import com.titan.event.vo.usercard.CountCardVO;
import com.titan.event.vo.usercard.StorageCardRechargeVO;
import com.titan.event.vo.usercard.SubCardInfoVO;
import com.titan.event.vo.usercard.UserCardDetailVO;
import com.titan.event.vo.usercard.UserCardUsageDetailVO;
import com.titan.event.vo.usercard.UserCardVO;

import java.util.List;
import java.util.Map;

/**
 * 用户卡服务接口
 * 
 * <AUTHOR>
 */
public interface IUserCardService {
    
    /**
     * 分页查询用户卡列表
     * 
     * @param request 查询请求参数
     * @param userId 用户ID
     * @return 分页结果
     */
    PageResultVO<UserCardVO> getUserCardList(UserCardListRequest request, Long userId);
    
    /**
     * 获取用户各种状态卡的数量统计
     *
     * @param userId 用户ID
     * @return 卡数量统计 Map<String, Integer>
     *         - benefitCardCount: 权益卡数量
     *         - storageCardCount: 储值卡数量
     *         - countCardCount: 计次卡数量
     *         - memberCardCount: 会员卡数量
     *         - availableBenefitCardCount: 可用权益卡数量
     *         - availableStorageCardCount: 可用储值卡数量
     *         - availableCountCardCount: 可用计次卡数量
     *         - availableMemberCardCount: 可用会员卡数量
     */
    Map<String, Integer> getUserCardCountStatistics(Long userId);

    /**
     * 获取用户卡详情
     *
     * @param request 查询请求参数
     * @param userId 用户ID
     * @return 用户卡详情
     */
    UserCardDetailVO getUserCardDetail(UserCardDetailRequest request, Long userId);

    /**
     * 分页查询用户卡使用明细
     *
     * @param request 查询请求参数
     * @param userId 用户ID
     * @return 分页结果
     */
    PageResultVO<UserCardUsageDetailVO> getUserCardUsageDetail(UserCardUsageDetailRequest request, Long userId);

    /**
     * 修改副卡信息
     *
     * @param request 修改请求参数
     * @param userId 用户ID
     * @return 修改后的副卡信息列表
     */
    List<SubCardInfoVO> updateSubCards(UpdateSubCardsRequest request, Long userId);

    /**
     * 储值卡充值
     *
     * @param request 充值请求参数
     * @param userId 用户ID
     * @return 充值响应信息（包含支付信息）
     */
    StorageCardRechargeVO rechargeStorageCard(StorageCardRechargeRequest request, Long userId);

    /**
     * 测试退款（仅用于测试）
     *
     * @param orderId 订单号
     * @param userId 用户ID
     * @param refundReason 退款原因
     * @return 退款结果
     */
    String testRefund(String orderId, Long userId, String refundReason);

    /**
     * 获取卡片详情（用于购买）
     *
     * @param cardId 卡片ID
     * @return 卡片详情信息（包含场馆客服电话等）
     */
    CardDetailVO getCardDetail(Long cardId);

    /**
     * 购买卡片（非储值卡）
     *
     * @param request 购买请求参数
     * @param userId 用户ID
     * @return 购买响应信息（包含支付信息）
     */
    CardPurchaseVO purchaseCard(CardPurchaseRequest request, Long userId);

    /**
     * 按手机号模糊查询有效的计次卡列表
     * 只返回有效的计次卡（剩余次数>0且未过期的卡片）
     *
     * @param request 查询请求参数
     * @return 有效的计次卡列表
     */
    PageResultVO<CountCardVO> searchCountCardByPhone(SearchCountCardByPhoneRequest request);

    /**
     * 扣减计次卡次数
     *
     * @param request 扣减请求参数
     * @param operatorId 操作员ID
     * @return 扣减结果
     */
    String deductCountCard(DeductCountCardRequest request, String operatorId);
}
