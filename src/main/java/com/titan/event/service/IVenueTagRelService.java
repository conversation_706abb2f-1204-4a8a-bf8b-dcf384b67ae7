package com.titan.event.service;

import com.titan.event.entity.VenueTagRel;

import java.util.List;

/**
 * 场馆标签关联服务接口
 * 
 * <AUTHOR>
 */
public interface IVenueTagRelService {
    
    /**
     * 根据ID查询关联关系
     * 
     * @param relId 关联ID
     * @return 关联关系
     */
    VenueTagRel findRelById(Long relId);
    
    /**
     * 新增关联关系
     * 
     * @param rel 关联关系信息
     * @return 结果
     */
    VenueTagRel addRel(VenueTagRel rel);
    
}