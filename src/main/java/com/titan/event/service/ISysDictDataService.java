package com.titan.event.service;

import com.titan.event.entity.SysDictData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * 字典数据表 服务层
 * 
 * <AUTHOR>
 */
public interface ISysDictDataService {
    
    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SysDictData> selectDictDataByType(String dictType);
    
    /**
     * 根据字典类型和字典值查询字典标签
     * 
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    String selectDictLabel(String dictType, String dictValue);
} 