package com.titan.event.service;

import com.titan.event.request.userinfo.UserUpdateRequest;
import com.titan.event.request.userinfo.WxLoginRequest;
import com.titan.event.request.userinfo.WxPhoneRequest;
import com.titan.event.response.LoginResponse;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;
import com.titan.event.vo.carouselchart.CarouselChartVO;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/10/27 09:45
 */
public interface ICarouselChartService {

    List<CarouselChartVO> getList();

}
