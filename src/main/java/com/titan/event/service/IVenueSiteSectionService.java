package com.titan.event.service;

import com.titan.event.entity.VenueSiteSection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * 场地分区表 服务层
 * 
 * <AUTHOR>
 */
public interface IVenueSiteSectionService {
    
    /**
     * 根据场地ID和删除标志查询分区列表
     * 
     * @param siteId 场地ID
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 分区列表
     */
    List<VenueSiteSection> findBySiteIdAndDelFlag(Long siteId, String delFlag);
}