package com.titan.event.service;

import com.titan.event.entity.VenueProduct;
import com.titan.event.vo.VenueProductVO;

import java.util.List;

/**
 * 场馆商品服务接口
 * 
 * <AUTHOR>
 */
public interface IVenueProductService {

    /**
     * 根据场馆ID查询上架中的商品
     * 
     * @param venueId 场馆ID
     * @return 商品列表
     */
    List<VenueProduct> findOnSaleProductsByVenueId(Long venueId);
    
    /**
     * 根据商品ID获取商品详情
     * 
     * @param productId 商品ID
     * @return 商品详情
     */
    VenueProduct getProductDetail(Long productId);
    
    /**
     * 根据商品ID获取商品详情VO对象
     * 
     * @param productId 商品ID
     * @return 商品详情VO对象
     */
    VenueProductVO getProductDetailVO(Long productId);
} 