package com.titan.event.service;

import com.titan.event.entity.VenueMemberCard;
import com.titan.event.entity.VenueMemberCardUser;
import com.titan.event.vo.venue.VenueCardGroupVO;
import com.titan.event.vo.venue.VenueCardVO;
import com.titan.event.vo.venue.VenueDetailVO;

import java.util.List;

/**
 * 场馆会员卡服务接口
 * 
 * <AUTHOR>
 */
public interface IVenueMemberCardService {
    
    /**
     * 根据场馆ID查询会员卡列表（按卡类型分组）
     * 
     * @param venueId 场馆ID
     * @param userId 用户ID（可为空，如果不为空则查询用户储值卡信息）
     * @return 按卡类型分组的会员卡列表
     */
    List<VenueCardGroupVO> getVenueCardsByVenueId(Long venueId, Long userId);
    
    /**
     * 根据场馆ID查询会员卡列表
     * 
     * @param venueId 场馆ID
     * @return 会员卡列表
     */
    List<VenueMemberCard> findCardsByVenueId(Long venueId);
    
    /**
     * 根据场馆ID和用户ID查询用户储值卡列表
     * 
     * @param venueId 场馆ID
     * @param userId 用户ID
     * @return 用户储值卡列表
     */
    List<VenueMemberCardUser> findUserStorageCardsByVenueId(Long venueId, Long userId);
    
    /**
     * 根据卡类型获取卡类型名称
     *
     * @param cardType 卡类型
     * @return 卡类型名称
     */
    String getCardTypeName(Integer cardType);

    /**
     * 根据场馆ID查询储值卡列表
     *
     * @param venueId 场馆ID
     * @return 储值卡列表
     */
    List<VenueCardVO> getStorageCardsByVenueId(Long venueId);

    /**
     * 根据场馆ID查询计次卡列表
     *
     * @param venueId 场馆ID
     * @return 计次卡列表
     */
    List<VenueCardVO> getTimesCardsByVenueId(Long venueId);

    /**
     * 根据场馆ID和用户ID查询用户储值卡信息
     *
     * @param venueId 场馆ID
     * @param userId 用户ID
     * @return 用户储值卡信息，如果不存在则返回null
     */
    VenueDetailVO.UserStorageCardVO getUserStorageCard(Long venueId, Long userId);
}
