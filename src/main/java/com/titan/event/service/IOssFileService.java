package com.titan.event.service;

import com.titan.event.response.StsResponse;
import com.titan.event.vo.UploadVO;
import com.titan.event.vo.UserInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @Description: OOS云存储service接口
 * @author: jeecg-boot
 */
public interface IOssFileService {

    UploadVO upload(MultipartFile multipartFile) throws Exception;

    StsResponse sts();

}
