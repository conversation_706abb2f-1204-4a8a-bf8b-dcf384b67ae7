package com.titan.event.mapper;

import com.titan.event.entity.VenueTagRel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 场馆标签关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueTagRelMapper extends JpaRepository<VenueTagRel, Long>, JpaSpecificationExecutor<VenueTagRel> {
    
    /**
     * 根据场馆ID和标签ID查询关联关系
     * 
     * @param venueId 场馆ID
     * @param tagId 标签ID
     * @return 关联关系
     */
    VenueTagRel findByVenueIdAndTagId(Long venueId, Long tagId);

} 