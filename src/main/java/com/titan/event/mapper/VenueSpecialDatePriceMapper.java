package com.titan.event.mapper;

import com.titan.event.entity.VenueSpecialDatePrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 特殊日期价格 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueSpecialDatePriceMapper extends JpaRepository<VenueSpecialDatePrice, Long>, JpaSpecificationExecutor<VenueSpecialDatePrice> {
    
    /**
     * 查询特殊日期价格列表
     * 
     * @param venueSpecialDatePrice 查询条件
     * @return 特殊日期价格列表
     */
    default List<VenueSpecialDatePrice> selectVenueSpecialDatePriceList(VenueSpecialDatePrice venueSpecialDatePrice) {
        // 使用JPA默认查询
        return findAll();
    }
}