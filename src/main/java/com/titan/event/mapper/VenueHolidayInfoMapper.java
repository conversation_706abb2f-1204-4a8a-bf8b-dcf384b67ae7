package com.titan.event.mapper;

import com.titan.event.entity.VenueHolidayInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 节假日信息表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueHolidayInfoMapper extends JpaRepository<VenueHolidayInfo, Long>, JpaSpecificationExecutor<VenueHolidayInfo> {
    
}