package com.titan.event.mapper;

import com.titan.event.entity.VenueSpecialDate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

/**
 * 特殊日期 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueSpecialDateMapper extends JpaRepository<VenueSpecialDate, Long>, JpaSpecificationExecutor<VenueSpecialDate> {
    
    /**
     * 查询特殊日期列表
     * 
     * @param venueSpecialDate 查询条件
     * @return 特殊日期列表
     */
    default List<VenueSpecialDate> selectVenueSpecialDateList(VenueSpecialDate venueSpecialDate) {
        // 构建动态查询条件
        Specification<VenueSpecialDate> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 根据ID查询
            if (venueSpecialDate != null && venueSpecialDate.getSpecialDateId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("specialDateId"), venueSpecialDate.getSpecialDateId()));
            }
            
            // 根据状态查询
            if (venueSpecialDate != null && venueSpecialDate.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), venueSpecialDate.getStatus()));
            }
            
            // 根据删除标志查询
            if (venueSpecialDate != null && venueSpecialDate.getDelFlag() != null) {
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), venueSpecialDate.getDelFlag()));
            } else {
                // 默认只查询未删除的记录
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
            }
            
            // 根据特殊日期类型查询
            if (venueSpecialDate != null && venueSpecialDate.getSpecialDateType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("specialDateType"), venueSpecialDate.getSpecialDateType()));
            }
            
            // 根据场地ID查询
            if (venueSpecialDate != null && venueSpecialDate.getSiteId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("siteId"), venueSpecialDate.getSiteId()));
            }
            
            // 根据特殊日期名称模糊查询
            if (venueSpecialDate != null && venueSpecialDate.getSpecialDateName() != null) {
                predicates.add(criteriaBuilder.like(root.get("specialDateName"), "%" + venueSpecialDate.getSpecialDateName() + "%"));
            }
            
            // 根据特殊日期范围查询
            if (venueSpecialDate != null && venueSpecialDate.getSpecialDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("specialDate"), venueSpecialDate.getSpecialDate()));
            }
            
            // 根据结束日期范围查询
            if (venueSpecialDate != null && venueSpecialDate.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("endDate"), venueSpecialDate.getEndDate()));
            }
            
            // 合并查询条件
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        // 使用JPA规范查询
        return findAll(spec);
    }
}