package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCardType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 会员卡类型Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardTypeMapper extends JpaRepository<VenueMemberCardType, Long>, JpaSpecificationExecutor<VenueMemberCardType> {
    
    /**
     * 根据删除标志查询会员卡类型列表
     * 
     * @param delFlag 删除标志
     * @return 会员卡类型列表
     */
    List<VenueMemberCardType> findByDelFlagOrderByCardTypeId(String delFlag);
    
    /**
     * 根据卡类型名称和删除标志查询会员卡类型
     * 
     * @param typeName 卡类型名称
     * @param delFlag 删除标志
     * @return 会员卡类型
     */
    VenueMemberCardType findByTypeNameAndDelFlag(String typeName, String delFlag);
    
    /**
     * 通过会员卡ID查询关联的会员卡类型列表
     * 
     * @param cardId 会员卡ID
     * @return 会员卡类型列表
     */
    @Query("select t from VenueMemberCardType t join VenueMemberCardTypeRel r on t.cardTypeId = r.cardTypeId where r.cardId = :cardId and t.delFlag = '0' and r.delFlag = '0'")
    List<VenueMemberCardType> findTypesByCardId(@Param("cardId") Long cardId);
} 