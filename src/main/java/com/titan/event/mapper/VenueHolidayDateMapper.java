package com.titan.event.mapper;

import com.titan.event.entity.VenueHolidayDate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

/**
 * 节假日日期 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueHolidayDateMapper extends JpaRepository<VenueHolidayDate, Long>, JpaSpecificationExecutor<VenueHolidayDate> {
    
    /**
     * 查询节假日列表
     * 
     * @param venueHolidayDate 查询条件
     * @return 节假日列表
     */
    default List<VenueHolidayDate> selectVenueHolidayDateList(VenueHolidayDate venueHolidayDate) {
        // 构建动态查询条件
        Specification<VenueHolidayDate> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 根据ID查询
            if (venueHolidayDate != null && venueHolidayDate.getHolidayId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("holidayId"), venueHolidayDate.getHolidayId()));
            }
            
            // 根据状态查询
            if (venueHolidayDate != null && venueHolidayDate.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), venueHolidayDate.getStatus()));
            }
            
            // 根据开始日期范围查询
            if (venueHolidayDate != null && venueHolidayDate.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("startDate"), venueHolidayDate.getStartDate()));
            }
            
            // 根据结束日期范围查询
            if (venueHolidayDate != null && venueHolidayDate.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("endDate"), venueHolidayDate.getEndDate()));
            }
            
            // 默认只查询有效状态的节假日
            if (venueHolidayDate == null || venueHolidayDate.getStatus() == null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), "0"));
            }
            
            // 合并查询条件
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        // 使用JPA规范查询
        return findAll(spec);
    }
}