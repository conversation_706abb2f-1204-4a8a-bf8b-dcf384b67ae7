package com.titan.event.mapper;

import com.titan.event.entity.SysDept;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 系统部门数据访问层
 * 
 * <AUTHOR>
 */
public interface SysDeptMapper extends JpaRepository<SysDept, Long>, JpaSpecificationExecutor<SysDept> {
    
    /**
     * 通过部门ID查询部门信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDept findByDeptId(Long deptId);
} 