package com.titan.event.mapper;

import com.titan.event.entity.VenueVerificationCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 场馆订单核销表数据访问接口
 */
@Repository
public interface VenueVerificationCodeMapper extends JpaRepository<VenueVerificationCode, Long>, JpaSpecificationExecutor<VenueVerificationCode> {

    /**
     * 根据订单ID查询核销码信息
     * 
     * @param orderId 订单ID
     * @return 核销码信息列表
     */
    List<VenueVerificationCode> findByOrderId(String orderId);

    /**
     * 根据核销码查询
     * 
     * @param verificationCode 核销码
     * @return 核销信息
     */
    Optional<VenueVerificationCode> findByVerificationCode(String verificationCode);
    
    /**
     * 根据订单ID列表查询核销码信息
     * 
     * @param orderIds 订单ID列表
     * @return 核销码信息列表
     */
    List<VenueVerificationCode> findByOrderIdIn(List<String> orderIds);
} 