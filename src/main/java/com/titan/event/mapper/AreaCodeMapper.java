package com.titan.event.mapper;

import com.titan.event.entity.AreaCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface AreaCodeMapper extends JpaRepository<AreaCode, Long>, JpaSpecificationExecutor<AreaCode>  {

    @Query(nativeQuery = true,value = "SELECT * " +
            "FROM area_code " +
            "WHERE name IN ( " +
            "    SELECT SUBSTRING_INDEX(event_area, ',', 1) AS province " +
            "    FROM event " +
            "    where del_flag = 0 " +
            "    and show_status = 1 " +
            ")")
    List<AreaCode> findAllByEvent();

    AreaCode findByCode(String code);

}
