package com.titan.event.mapper;

import com.titan.event.entity.VenueProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 场馆商品Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueProductMapper extends JpaRepository<VenueProduct, Long>, JpaSpecificationExecutor<VenueProduct> {
    
    /**
     * 根据场馆ID查询上架中的商品
     * 
     * @param venueId 场馆ID
     * @return 商品列表
     */
    @Query("SELECT p FROM VenueProduct p WHERE p.venueId = :venueId AND p.status = true AND p.delFlag = '0' ORDER BY p.orderNum")
    List<VenueProduct> findOnSaleProductsByVenueId(@Param("venueId") Long venueId);

} 