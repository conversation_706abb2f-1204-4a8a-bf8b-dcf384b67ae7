package com.titan.event.mapper;

import com.titan.event.entity.SysDictData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 字典数据表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface SysDictDataMapper extends JpaRepository<SysDictData, Long>, JpaSpecificationExecutor<SysDictData> {
    
    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SysDictData> findByDictTypeOrderByDictSortAsc(String dictType);
    
    /**
     * 根据字典类型和字典值查询字典标签
     * 
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    @Query("select d.dictLabel from SysDictData d where d.dictType = :dictType and d.dictValue = :dictValue and d.status = '0'")
    String findDictLabelByTypeAndValue(@Param("dictType") String dictType, @Param("dictValue") String dictValue);
} 