package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCardRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 会员卡主副卡关系Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardRelationMapper extends JpaRepository<VenueMemberCardRelation, Long>, JpaSpecificationExecutor<VenueMemberCardRelation> {
    
    /**
     * 根据会员卡ID查询主副卡关系
     * 
     * @param cardId 会员卡ID
     * @param delFlag 删除标志
     * @return 主副卡关系列表
     */
    List<VenueMemberCardRelation> findByCardIdAndDelFlag(Long cardId, String delFlag);
    
    /**
     * 根据用户ID查询主副卡关系
     * 
     * @param cardUserId 会员卡用户ID
     * @param delFlag 删除标志
     * @return 主副卡关系列表
     */
    List<VenueMemberCardRelation> findByCardUserIdAndDelFlag(Long cardUserId, String delFlag);
    
    /**
     * 根据会员卡ID和用户ID查询主副卡关系
     * 
     * @param cardId 会员卡ID
     * @param cardUserId 会员卡用户ID
     * @param delFlag 删除标志
     * @return 主副卡关系
     */
    VenueMemberCardRelation findByCardIdAndCardUserIdAndDelFlag(Long cardId, Long cardUserId, String delFlag);
    
    /**
     * 统计会员卡的副卡数量
     * 
     * @param cardId 会员卡ID
     * @param cardRelationType 卡类型（2副卡）
     * @param delFlag 删除标志
     * @return 副卡数量
     */
    @Query("select count(r) from VenueMemberCardRelation r where r.cardId = :cardId and r.cardRelationType = :cardRelationType and r.delFlag = :delFlag")
    int countSubCards(@Param("cardId") Long cardId, @Param("cardRelationType") Integer cardRelationType, @Param("delFlag") String delFlag);
} 