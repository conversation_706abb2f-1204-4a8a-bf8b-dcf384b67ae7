package com.titan.event.mapper;

import com.titan.event.entity.Venue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 场馆信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMapper extends JpaRepository<Venue, Long>, JpaSpecificationExecutor<Venue> {
    
}