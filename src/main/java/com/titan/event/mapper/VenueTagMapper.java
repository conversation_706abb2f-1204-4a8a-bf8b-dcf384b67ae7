package com.titan.event.mapper;

import com.titan.event.entity.VenueTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 标签Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueTagMapper extends JpaRepository<VenueTag, Long>, JpaSpecificationExecutor<VenueTag> {

    /**
     * 查询指定场馆下的所有标签
     * 
     * @param venueId 场馆ID
     * @return 标签列表
     */
    @Query("SELECT t FROM VenueTag t JOIN VenueTagRel r ON t.tagId = r.tagId WHERE r.venueId = :venueId AND t.delFlag = '0' AND r.delFlag = '0' ORDER BY t.orderNum")
    List<VenueTag> findTagsByVenueId(@Param("venueId") Long venueId);
} 