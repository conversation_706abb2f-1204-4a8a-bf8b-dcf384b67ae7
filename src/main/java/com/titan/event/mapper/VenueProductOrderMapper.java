package com.titan.event.mapper;

import com.titan.event.entity.VenueProductOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 场馆商品订单Mapper接口
 */
public interface VenueProductOrderMapper extends JpaRepository<VenueProductOrder, Long>, JpaSpecificationExecutor<VenueProductOrder> {
    
    /**
     * 根据订单ID查询商品订单详情
     * 
     * @param orderId 订单ID
     * @return 商品订单详情列表
     */
    List<VenueProductOrder> findByOrderId(String orderId);
    
    /**
     * 根据商品ID查询订单
     * 
     * @param productId 商品ID
     * @return 商品订单详情列表
     */
    List<VenueProductOrder> findByProductId(Long productId);
    
    /**
     * 根据场馆ID查询订单
     * 
     * @param venueId 场馆ID
     * @return 商品订单详情列表
     */
    List<VenueProductOrder> findByVenueId(Long venueId);

    /**
     * 根据订单ID列表查询商品订单
     * 
     * @param orderIds 订单ID列表
     * @return 商品订单列表
     */
    List<VenueProductOrder> findByOrderIdIn(List<String> orderIds);
} 