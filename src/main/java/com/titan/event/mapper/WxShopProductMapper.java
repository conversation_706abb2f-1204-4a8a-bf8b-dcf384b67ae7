package com.titan.event.mapper;

import com.titan.event.entity.WxShopProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface WxShopProductMapper extends JpaRepository<WxShopProduct, Long>, JpaSpecificationExecutor<WxShopProduct> {
    
    /**
     * 根据店铺ID和商品ID查询商品
     */
    WxShopProduct findByShopIdAndProductId(String shopId, Long productId);
    
    /**
     * 根据店铺ID删除所有商品
     */
    void deleteByShopId(String shopId);
} 