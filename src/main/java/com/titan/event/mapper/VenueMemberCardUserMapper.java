package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCardUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 * 用户会员卡Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardUserMapper extends JpaRepository<VenueMemberCardUser, Long>, JpaSpecificationExecutor<VenueMemberCardUser> {
    
    /**
     * 根据用户ID查询会员卡列表
     * 
     * @param userId 用户ID
     * @param delFlag 删除标志
     * @return 会员卡列表
     */
    List<VenueMemberCardUser> findByUserIdAndDelFlag(Long userId, String delFlag);
    
    /**
     * 根据会员卡号查询会员卡
     * 
     * @param cardNumber 会员卡号
     * @param delFlag 删除标志
     * @return 会员卡
     */
    Optional<VenueMemberCardUser> findByCardNumberAndDelFlag(String cardNumber, String delFlag);
    
    /**
     * 根据会员卡ID和用户ID查询会员卡
     * 
     * @param cardId 会员卡ID
     * @param userId 用户ID
     * @param delFlag 删除标志
     * @return 会员卡
     */
    Optional<VenueMemberCardUser> findByCardIdAndUserIdAndDelFlag(Long cardId, Long userId, String delFlag);
    
    /**
     * 根据场馆ID查询用户会员卡列表
     * 
     * @param venueId 场馆ID
     * @param delFlag 删除标志
     * @return 会员卡列表
     */
    List<VenueMemberCardUser> findByVenueIdAndDelFlag(Long venueId, String delFlag);
    
    /**
     * 根据场馆ID和用户ID查询会员卡列表
     * 
     * @param venueId 场馆ID
     * @param userId 用户ID
     * @param delFlag 删除标志
     * @return 会员卡列表
     */
    List<VenueMemberCardUser> findByVenueIdAndUserIdAndDelFlag(Long venueId, Long userId, String delFlag);
    
    /**
     * 统计用户某种类型的会员卡数量
     * 
     * @param venueId 场馆ID
     * @param userId 用户ID
     * @param cardType 卡类型
     * @param delFlag 删除标志
     * @return 数量
     */
    @Query("select count(u) from VenueMemberCardUser u where u.venueId = :venueId and u.userId = :userId and u.cardType = :cardType and u.delFlag = :delFlag")
    int countUserCardsByType(@Param("venueId") Long venueId, @Param("userId") Long userId, @Param("cardType") Integer cardType, @Param("delFlag") String delFlag);
} 