package com.titan.event.mapper;

import com.titan.event.entity.pay.PayOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface PayOrderMapper extends JpaRepository<PayOrder, Long>, JpaSpecificationExecutor<PayOrder>  {

    PayOrder findByOrderId(String orderId);

    @Query(nativeQuery = true,value = "select * from platform_pay_order where pay_status='NOTPAY' and time_expire<NOW()")
    List<PayOrder> findExpireOrder();

    @Query(nativeQuery = true, value = "select * from platform_pay_order where pay_status='NOTPAY' and time_expire<NOW() limit ?1, ?2")
    List<PayOrder> findExpireOrderWithPage(int offset, int limit);

    @Query(nativeQuery = true,value = "select * from platform_pay_order " +
            "where pay_status='SUCCESS'" +
            "and refund_id is null and upload_status is null and create_time < NOW() - INTERVAL 10 MINUTE")
    List<PayOrder> findUploadOrder();

    /**
     * 根据订单ID列表查询支付订单
     * 
     * @param orderIds 订单ID列表
     * @return 支付订单列表
     */
    List<PayOrder> findByOrderIdIn(List<String> orderIds);

}
