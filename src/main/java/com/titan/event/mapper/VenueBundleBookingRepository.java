package com.titan.event.mapper;

import com.titan.event.entity.VenueBundleBooking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 捆绑订场表 数据访问层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueBundleBookingRepository extends JpaRepository<VenueBundleBooking, Long>, JpaSpecificationExecutor<VenueBundleBooking> {
    
    /**
     * 根据场馆ID查询捆绑订场记录
     * 
     * @param venueId 场馆ID
     * @param delFlag 删除标志
     * @return 捆绑订场记录列表
     */
    List<VenueBundleBooking> findByVenueIdAndDelFlag(Long venueId, String delFlag);
    
    /**
     * 查询指定日期范围内的捆绑订场记录
     * 
     * @param venueId 场馆ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param delFlag 删除标志
     * @return 捆绑订场记录列表
     */
    List<VenueBundleBooking> findByVenueIdAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndDelFlag(
            Long venueId, Date endDate, Date startDate, String delFlag);
    
    /**
     * 根据场地ID查询相关的捆绑订场记录
     * 
     * @param siteIdLike 场地ID（模糊查询）
     * @param delFlag 删除标志
     * @return 捆绑订场记录列表
     */
    List<VenueBundleBooking> findBySiteIdListContainingAndDelFlag(String siteIdLike, String delFlag);
} 