package com.titan.event.mapper;

import com.titan.event.entity.VenueHolidayPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 节假日价格表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueHolidayPriceMapper extends JpaRepository<VenueHolidayPrice, Long>, JpaSpecificationExecutor<VenueHolidayPrice> {
    
    /**
     * 根据日期和场地查询节假日价格
     * 
     * @param siteId 场地ID
     * @param sectionId 分区ID，null表示全场
     * @param date 日期
     * @return 节假日价格列表
     */
    @Query("SELECT hp FROM VenueHolidayPrice hp LEFT JOIN VenueHolidayDate hd ON hp.holidayId = hd.holidayId " +
           "WHERE hp.siteId = :siteId AND hp.siteSection = :sectionId " +
           "AND :date BETWEEN hd.startDate AND hd.endDate " +
           "AND hd.status = '0'")
    List<VenueHolidayPrice> selectByDateAndSite(@Param("siteId") Long siteId, 
                                               @Param("sectionId") Long sectionId, 
                                               @Param("date") String date);
}