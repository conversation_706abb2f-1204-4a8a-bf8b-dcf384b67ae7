package com.titan.event.mapper;

import com.titan.event.entity.VenueOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

/**
 * 场馆订单总表Mapper接口
 */
public interface VenueOrderMapper extends JpaRepository<VenueOrder, Long>, JpaSpecificationExecutor<VenueOrder> {
    
    /**
     * 根据订单ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    Optional<VenueOrder> findByOrderId(String orderId);
    
    /**
     * 根据订单ID列表查询订单
     *
     * @param orderIds 订单ID列表
     * @return 订单列表
     */
    List<VenueOrder> findByOrderIdIn(List<String> orderIds);
}