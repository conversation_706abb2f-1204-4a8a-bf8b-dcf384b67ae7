package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCardDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 会员卡使用明细Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardDetailMapper extends JpaRepository<VenueMemberCardDetail, Long>, JpaSpecificationExecutor<VenueMemberCardDetail> {
    
    /**
     * 根据用户ID和卡ID查询使用明细
     * 
     * @param userId 用户ID
     * @param cardId 会员卡ID
     * @return 使用明细列表
     */
    List<VenueMemberCardDetail> findByUserIdAndCardIdAndDelFlagOrderByOperationTimeDesc(Long userId, Long cardId, String delFlag);
    
    /**
     * 根据用户ID查询使用明细
     * 
     * @param userId 用户ID
     * @return 使用明细列表
     */
    List<VenueMemberCardDetail> findByUserIdAndDelFlagOrderByOperationTimeDesc(Long userId, String delFlag);
    
    /**
     * 根据会员卡ID查询使用明细
     *
     * @param cardId 会员卡ID
     * @return 使用明细列表
     */
    List<VenueMemberCardDetail> findByCardIdAndDelFlagOrderByOperationTimeDesc(Long cardId, String delFlag);

    /**
     * 根据用户会员卡ID查询使用明细
     *
     * @param userCardId 用户会员卡ID
     * @param delFlag 删除标志
     * @return 使用明细列表
     */
    List<VenueMemberCardDetail> findByUserCardIdAndDelFlagOrderByOperationTimeDesc(Long userCardId, String delFlag);
}