package com.titan.event.mapper;

import com.titan.event.entity.VenueBooking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 预订记录表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueBookingMapper extends JpaRepository<VenueBooking, Long>, JpaSpecificationExecutor<VenueBooking> {

    /**
     * 根据订单ID查询预订记录
     * 
     * @param orderId 订单ID
     * @return 预订记录列表
     */
    List<VenueBooking> findByOrderId(String orderId);
} 