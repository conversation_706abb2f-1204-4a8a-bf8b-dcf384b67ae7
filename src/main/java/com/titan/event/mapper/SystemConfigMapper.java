package com.titan.event.mapper;

import com.titan.event.entity.SystemConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface SystemConfigMapper extends JpaRepository<SystemConfig, Long>, JpaSpecificationExecutor<SystemConfig>  {

    SystemConfig findByConfigKeyAndConfigType(String key,String type);

}
