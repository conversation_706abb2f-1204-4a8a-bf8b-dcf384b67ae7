package com.titan.event.mapper;

import com.titan.event.entity.VenueTimeslotPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Map;

/**
 * 时段价格 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueTimeslotPriceMapper extends JpaRepository<VenueTimeslotPrice, Long>, JpaSpecificationExecutor<VenueTimeslotPrice> {
    
    /**
     * 查询时段价格列表，根据传入的条件进行动态查询
     * 
     * @param venueTimeslotPrice 查询条件
     * @return 时段价格列表
     */
    default List<VenueTimeslotPrice> selectVenueTimeslotPriceList(VenueTimeslotPrice venueTimeslotPrice) {
        return findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 根据传入的条件参数动态构建查询条件
            if (venueTimeslotPrice != null) {
                // 按场地ID查询
                if (venueTimeslotPrice.getSiteId() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("siteId"), venueTimeslotPrice.getSiteId()));
                }
                
                // 按星期几查询
                if (venueTimeslotPrice.getWeekDays() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("weekDays"), venueTimeslotPrice.getWeekDays()));
                }
                
                // 按分区ID查询，处理特殊情况：是否查询全场
                if (venueTimeslotPrice.getParams() != null && 
                    venueTimeslotPrice.getParams().containsKey("queryFullCourt") && 
                    (Boolean)venueTimeslotPrice.getParams().get("queryFullCourt")) {
                    // 查询全场的价格设置（sectionId为null的记录）
                    predicates.add(criteriaBuilder.isNull(root.get("sectionId")));
                } else {
                    // 正常按sectionId查询
                    if (venueTimeslotPrice.getSectionId() != null) {
                        predicates.add(criteriaBuilder.equal(root.get("sectionId"), venueTimeslotPrice.getSectionId()));
                    }
                }
                
                // 按删除标志查询，默认只查询未删除的数据
                if (venueTimeslotPrice.getDelFlag() != null && !venueTimeslotPrice.getDelFlag().isEmpty()) {
                    predicates.add(criteriaBuilder.equal(root.get("delFlag"), venueTimeslotPrice.getDelFlag()));
                } else {
                    predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
                }
                
                // 处理额外的查询参数
                if (venueTimeslotPrice.getParams() != null) {
                    // 开始时间范围查询
                    if (venueTimeslotPrice.getParams().containsKey("startTimeBegin") && 
                        venueTimeslotPrice.getParams().containsKey("startTimeEnd")) {
                        String startTimeBegin = (String) venueTimeslotPrice.getParams().get("startTimeBegin");
                        String startTimeEnd = (String) venueTimeslotPrice.getParams().get("startTimeEnd");
                        predicates.add(criteriaBuilder.between(root.get("startTime"), startTimeBegin, startTimeEnd));
                    }
                    
                    // 时间段包含查询（例如查询包含特定时间点的时段）
                    if (venueTimeslotPrice.getParams().containsKey("timePoint")) {
                        String timePoint = (String) venueTimeslotPrice.getParams().get("timePoint");
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("startTime"), timePoint));
                        predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("endTime"), timePoint));
                    }
                }
            } else {
                // 如果查询条件为空，默认只查询未删除的数据
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }
}