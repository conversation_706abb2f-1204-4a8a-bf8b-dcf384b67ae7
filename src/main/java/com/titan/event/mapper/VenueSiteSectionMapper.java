package com.titan.event.mapper;

import com.titan.event.entity.VenueSiteSection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;

/**
 * 场地分区表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueSiteSectionMapper extends JpaRepository<VenueSiteSection, Long>, JpaSpecificationExecutor<VenueSiteSection> {
    
    /**
     * 根据场地ID和删除标志查询分区列表
     * 
     * @param siteId 场地ID
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 分区列表
     */
    List<VenueSiteSection> findBySiteIdAndDelFlag(Long siteId, String delFlag);
    
    /**
     * 根据分区ID查询分区信息
     * 
     * @param sectionId 分区ID
     * @return 分区信息
     */
    default VenueSiteSection selectVenueSiteSectionBySectionId(Long sectionId) {
        return findById(sectionId).orElse(null);
    }
    
    /**
     * 查询分区列表，根据传入的条件进行动态查询
     * 
     * @param venueSiteSection 查询条件
     * @return 分区列表
     */
    default List<VenueSiteSection> selectVenueSiteSectionList(VenueSiteSection venueSiteSection) {
        return findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 根据传入的条件参数动态构建查询条件
            if (venueSiteSection != null) {
                // 按场地ID查询
                if (venueSiteSection.getSiteId() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("siteId"), venueSiteSection.getSiteId()));
                }
                
                // 按分区ID查询
                if (venueSiteSection.getSectionId() != null) {
                    predicates.add(criteriaBuilder.equal(root.get("sectionId"), venueSiteSection.getSectionId()));
                }
                
                // 按分区名称模糊查询
                if (venueSiteSection.getSectionName() != null && !venueSiteSection.getSectionName().isEmpty()) {
                    predicates.add(criteriaBuilder.like(root.get("sectionName"), "%" + venueSiteSection.getSectionName() + "%"));
                }
                
                // 按分区编号查询
                if (venueSiteSection.getSectionCode() != null && !venueSiteSection.getSectionCode().isEmpty()) {
                    predicates.add(criteriaBuilder.equal(root.get("sectionCode"), venueSiteSection.getSectionCode()));
                }
                
                // 按状态查询
                if (venueSiteSection.getStatus() != null && !venueSiteSection.getStatus().isEmpty()) {
                    predicates.add(criteriaBuilder.equal(root.get("status"), venueSiteSection.getStatus()));
                }
                
                // 按删除标志查询，默认只查询未删除的数据
                if (venueSiteSection.getDelFlag() != null && !venueSiteSection.getDelFlag().isEmpty()) {
                    predicates.add(criteriaBuilder.equal(root.get("delFlag"), venueSiteSection.getDelFlag()));
                } else {
                    predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
                }
            } else {
                // 如果查询条件为空，默认只查询未删除的数据
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), "0"));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }
}