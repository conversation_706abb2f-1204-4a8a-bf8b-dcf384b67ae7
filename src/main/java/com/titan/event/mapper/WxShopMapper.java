package com.titan.event.mapper;

import com.titan.event.entity.WxShop;
import com.titan.event.entity.WxShopOrderNotify;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface WxShopMapper extends JpaRepository<WxShop, Long>, JpaSpecificationExecutor<WxShop>  {

    /**
     * 根据店铺ID查询店铺信息
     * @param shopId 店铺ID
     * @return 店铺信息
     */
    WxShop findByShopId(String shopId);
}
