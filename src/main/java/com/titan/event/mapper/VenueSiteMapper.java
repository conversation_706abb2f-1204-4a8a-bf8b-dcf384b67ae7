package com.titan.event.mapper;

import com.titan.event.entity.VenueSite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 场地信息表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface VenueSiteMapper extends JpaRepository<VenueSite, Long>, JpaSpecificationExecutor<VenueSite> {
    
    /**
     * 根据场馆ID和删除标志查询场地信息
     * 
     * @param venueId 场馆ID
     * @param delFlag 删除标志（0代表存在 2代表删除）
     * @return 场地信息列表
     */
    List<VenueSite> findByVenueIdAndDelFlag(Long venueId, String delFlag);
    
    /**
     * 根据场地ID查询场地信息
     * 
     * @param siteId 场地ID
     * @return 场地信息
     */
    default VenueSite selectVenueSiteBySiteId(Long siteId) {
        return findById(siteId).orElse(null);
    }
}