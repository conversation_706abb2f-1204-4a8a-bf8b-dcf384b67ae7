package com.titan.event.mapper;

import com.titan.event.entity.UserInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> lhx
 * @date 2023/9/22 14:27
 */
public interface UserInfoMapper extends JpaRepository<UserInfo, Long>, JpaSpecificationExecutor<UserInfo>  {

    UserInfo findByPhone(String phone);

    UserInfo findByOpenId(String openId);

}
