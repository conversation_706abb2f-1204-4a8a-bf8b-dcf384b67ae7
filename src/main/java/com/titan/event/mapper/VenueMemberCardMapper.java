package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 会员卡Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardMapper extends JpaRepository<VenueMemberCard, Long>, JpaSpecificationExecutor<VenueMemberCard> {
    
    /**
     * 根据场馆ID查询会员卡列表
     * 
     * @param venueId 场馆ID
     * @return 会员卡列表
     */
    List<VenueMemberCard> findByVenueIdAndDelFlag(Long venueId, String delFlag);
    
    /**
     * 根据会员卡类型查询会员卡列表
     * 
     * @param cardType 会员卡类型
     * @return 会员卡列表
     */
    List<VenueMemberCard> findByCardTypeAndDelFlag(Integer cardType, String delFlag);
} 