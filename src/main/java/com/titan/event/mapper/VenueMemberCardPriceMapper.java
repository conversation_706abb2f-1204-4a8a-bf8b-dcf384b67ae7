package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCardPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 会员卡价格Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardPriceMapper extends JpaRepository<VenueMemberCardPrice, Long>, JpaSpecificationExecutor<VenueMemberCardPrice> {
    
    /**
     * 根据会员卡ID查询价格列表
     * 
     * @param cardId 会员卡ID
     * @return 价格列表
     */
    List<VenueMemberCardPrice> findByCardId(Long cardId);
    
    /**
     * 根据会员卡ID和场地ID查询价格列表
     * 
     * @param cardId 会员卡ID
     * @param siteId 场地ID
     * @return 价格列表
     */
    List<VenueMemberCardPrice> findByCardIdAndSiteId(Long cardId, Long siteId);
    
    /**
     * 根据会员卡ID和时间段查询价格
     * 
     * @param cardId 会员卡ID
     * @param timeSlot 时间段
     * @param siteId 场地ID
     * @param siteSection 场地类型（0全场 1半场）
     * @return 价格对象
     */
    VenueMemberCardPrice findByCardIdAndTimeSlotAndSiteIdAndSiteSection(Long cardId, String timeSlot, Long siteId, Integer siteSection);
} 