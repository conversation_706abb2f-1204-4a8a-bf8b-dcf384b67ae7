package com.titan.event.mapper;

import com.titan.event.entity.VenueBookingOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 场馆预订订单Mapper接口
 */
public interface VenueBookingOrderMapper extends JpaRepository<VenueBookingOrder, Long>, JpaSpecificationExecutor<VenueBookingOrder> {
    
    /**
     * 根据订单ID查询预订订单详情
     * 
     * @param orderId 订单ID
     * @return 预订订单详情列表
     */
    List<VenueBookingOrder> findByOrderId(String orderId);
    
    /**
     * 根据场地ID查询订单
     * 
     * @param siteId 场地ID
     * @return 预订订单详情列表
     */
    List<VenueBookingOrder> findBySiteId(Long siteId);
    
    /**
     * 根据场馆ID查询订单
     * 
     * @param venueId 场馆ID
     * @return 预订订单详情列表
     */
    List<VenueBookingOrder> findByVenueId(Long venueId);
    
    /**
     * 查询指定时间段内的场地预订
     * 
     * @param siteId 场地ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预订订单详情列表
     */
    @Query("SELECT b FROM VenueBookingOrder b WHERE b.siteId = :siteId " +
           "AND ((b.startTime <= :endTime AND b.endTime >= :startTime) " +
           "OR (b.startTime >= :startTime AND b.startTime < :endTime) " +
           "OR (b.endTime > :startTime AND b.endTime <= :endTime))")
    List<VenueBookingOrder> findBySiteIdAndTimeRange(
            @Param("siteId") Long siteId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询某一天的场地预订
     * 
     * @param siteId 场地ID
     * @param bookingDate 预订日期
     * @return 预订订单详情列表
     */
    List<VenueBookingOrder> findBySiteIdAndBookingDate(Long siteId, LocalDateTime bookingDate);

    /**
     * 根据订单ID列表查询预订订单
     * 
     * @param orderIds 订单ID列表
     * @return 预订订单列表
     */
    List<VenueBookingOrder> findByOrderIdIn(List<String> orderIds);
} 