package com.titan.event.mapper;

import com.titan.event.entity.CertVerificationRecord;
import com.titan.event.entity.CountryCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 身份证二要素核验记录Repository
 * <AUTHOR>
 */
public interface CertVerificationRecordRepository extends JpaRepository<CertVerificationRecord, Long>, JpaSpecificationExecutor<CertVerificationRecord> {

    /**
     * 根据姓名和身份证号查询记录
     * @param certName 姓名
     * @param certNo 身份证号
     * @return 验证记录
     */
    Optional<CertVerificationRecord> findByCertNameAndCertNo(String certName, String certNo);
} 