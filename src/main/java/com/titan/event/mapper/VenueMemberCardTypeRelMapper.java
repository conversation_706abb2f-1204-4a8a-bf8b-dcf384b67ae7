package com.titan.event.mapper;

import com.titan.event.entity.VenueMemberCardTypeRel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 会员卡与卡类型关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface VenueMemberCardTypeRelMapper extends JpaRepository<VenueMemberCardTypeRel, Long>, JpaSpecificationExecutor<VenueMemberCardTypeRel> {
    
    /**
     * 根据会员卡ID查询关联记录
     * 
     * @param cardId 会员卡ID
     * @param delFlag 删除标志
     * @return 关联记录列表
     */
    List<VenueMemberCardTypeRel> findByCardIdAndDelFlag(Long cardId, String delFlag);
    
    /**
     * 根据卡类型ID查询关联记录
     * 
     * @param cardTypeId 卡类型ID
     * @param delFlag 删除标志
     * @return 关联记录列表
     */
    List<VenueMemberCardTypeRel> findByCardTypeIdAndDelFlag(Long cardTypeId, String delFlag);
    
    /**
     * 根据会员卡ID和卡类型ID查询关联记录
     * 
     * @param cardId 会员卡ID
     * @param cardTypeId 卡类型ID
     * @param delFlag 删除标志
     * @return 关联记录
     */
    VenueMemberCardTypeRel findByCardIdAndCardTypeIdAndDelFlag(Long cardId, Long cardTypeId, String delFlag);
    
    /**
     * 删除会员卡的所有类型关联
     * 
     * @param cardId 会员卡ID
     * @return 影响行数
     */
    @Query("update VenueMemberCardTypeRel set delFlag = '2' where cardId = :cardId")
    int deleteByCardId(@Param("cardId") Long cardId);
} 