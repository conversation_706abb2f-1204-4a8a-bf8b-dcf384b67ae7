package com.titan.event.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.titan.event.config.CacheConfig;
import com.titan.event.response.countryCode.CountryCodeResponse;
import com.titan.event.response.systemConfig.CarouselConfigResponse;
import com.titan.event.response.systemConfig.ContactUsConfigResponse;
import com.titan.event.response.systemConfig.SmsSourceConfigResponse;
import com.titan.event.response.systemConfig.SuccessConfigResponse;
import com.titan.event.service.ICountryCodeService;
import com.titan.event.service.IUserInfoService;
import com.titan.event.service.impl.SystemConfigServiceImpl;
import com.titan.event.util.CommonUtil;
import com.titan.event.vo.*;
import com.titan.event.vo.countryCode.CountryCodeVO;
import com.titan.event.vo.systemConfig.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static com.titan.event.constant.SystemConfigConstant.*;
import static com.titan.event.config.CacheConfig.*;

/**
 * <AUTHOR>
 */
@RequestMapping("/system")
@RestController
@Slf4j
@Api(description = "app接口 - 系统设置", tags = "SystemConfigController")
public class SystemConfigController {

    @Autowired
    private SystemConfigServiceImpl systemConfigService;

    @Autowired
    private ICountryCodeService countryCodeService;

    /**
     * 系统配置
     * @return
     */
    @ApiOperation(value = "系统配置")
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    @Cacheable(value = SYSTEM_SETTING_CACHE)
    public Result setting() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, SYSTEM_SETTING);
        return Result.OK(JSONObject.parseObject(systemConfig.getContext()));
    }

    @ApiOperation(value = "金刚区")
    @RequestMapping(value = "/links", method = RequestMethod.GET)
    @Cacheable(value = SYSTEM_LINKS_CACHE)
    public Result links() {
        SystemConfigVO systemConfig = systemConfigService.findByConfigKeyAndConfigType(MOBILE_CONFIG_KEY, JINGANG_DISTRICT);
        return Result.OK(JSONArray.parseArray(systemConfig.getContext()));
    }

    /**
     * 国家与地区
     * @return
     */
    @ApiOperation(value = "国家与地区")
    @RequestMapping(value = "/countryCode", method = RequestMethod.GET)
    @Cacheable(value = SYSTEM_COUNTRY_CODE_CACHE)
    public Result<CountryCodeResponse> countryCode() {
        return Result.OK(CountryCodeResponse.builder()
                .countryCode(countryCodeService.list())
                .build());
    }

    @ApiOperation(value = "刷新系统配置缓存")
    @RequestMapping(value = "/refresh/cache", method = RequestMethod.POST)
    @CacheEvict(value = {SYSTEM_SETTING_CACHE, SYSTEM_LINKS_CACHE, SYSTEM_COUNTRY_CODE_CACHE,CAROUSEL_LIST_CACHE,EVENT_LIST_CACHE}, allEntries = true)
    public Result refreshCache() {
        log.info("刷新系统配置缓存");
        return Result.OK();
    }
}
