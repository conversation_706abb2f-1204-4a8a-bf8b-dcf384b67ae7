package com.titan.event.controller;

import com.titan.event.request.venue.VenueDetailRequest;
import com.titan.event.request.venue.VenueListRequest;
import com.titan.event.response.venue.VenueDetailResponse;
import com.titan.event.response.venue.VenueListResponse;
import com.titan.event.response.venue.VenueSettingResponse;
import com.titan.event.service.IVenueService;
import com.titan.event.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 场馆管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/venue")
@Api(tags = "场馆管理接口")
public class VenueController {

    @Resource
    private IVenueService venueService;
    
    /**
     * 查询场馆配置信息
     */
    @GetMapping("/setting")
    @ApiOperation(value = "获取场馆配置信息", notes = "查询场馆相关的配置信息，主要包括地区数据")
    public Result<VenueSettingResponse> setting() {
        VenueSettingResponse setting = venueService.getVenueSetting();
        return Result.OK(setting);
    }
    
    /**
     * 场馆列表查询
     */
    @PostMapping("/list")
    @ApiOperation(value = "场馆列表查询", notes = "根据条件查询场馆列表信息")
    public Result<VenueListResponse> list(@RequestBody VenueListRequest request) {
        VenueListResponse venueList = venueService.getVenueList(request);
        return Result.OK(venueList);
    }
    
    /**
     * 场馆详情查询
     */
    @PostMapping("/detail")
    @ApiOperation(value = "场馆详情查询", notes = "查询场馆详细信息")
    public Result<VenueDetailResponse> detail(@RequestBody VenueDetailRequest request) {
        VenueDetailResponse venueDetail = venueService.getVenueDetail(request);
        if (venueDetail == null) {
            return Result.error("场馆不存在");
        }
        return Result.OK(venueDetail);
    }
} 