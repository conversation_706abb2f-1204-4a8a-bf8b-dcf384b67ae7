package com.titan.event.controller;

import com.titan.event.request.usercard.CardPurchaseRequest;
import com.titan.event.request.usercard.DeductCountCardRequest;
import com.titan.event.request.usercard.SearchCountCardByPhoneRequest;
import com.titan.event.request.usercard.StorageCardRechargeRequest;
import com.titan.event.request.usercard.UpdateSubCardsRequest;
import com.titan.event.request.usercard.UserCardDetailRequest;
import com.titan.event.request.usercard.UserCardListRequest;
import com.titan.event.request.usercard.UserCardUsageDetailRequest;
import com.titan.event.service.IUserCardService;
import com.titan.event.util.CommonUtil;
import com.titan.event.vo.PageResultVO;
import com.titan.event.vo.Result;
import com.titan.event.vo.usercard.CardDetailVO;
import com.titan.event.vo.usercard.CardPurchaseVO;
import com.titan.event.vo.usercard.CountCardVO;
import com.titan.event.vo.usercard.StorageCardRechargeVO;
import com.titan.event.vo.usercard.SubCardInfoVO;
import com.titan.event.vo.usercard.UserCardDetailVO;
import com.titan.event.vo.usercard.UserCardUsageDetailVO;
import com.titan.event.vo.usercard.UserCardVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户卡管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/userCard")
@Api(tags = "用户卡管理接口")
public class UserCardController {

    @Resource
    private IUserCardService userCardService;
    
    @Resource
    private CommonUtil commonUtil;
    
    /**
     * 分页查询用户卡列表
     * 支持按卡类型和可用状态筛选
     * 卡类型说明：
     * 0-权益卡：计次卡并且是无限次使用
     * 1-储值卡：卡类型是储值卡的
     * 2-计次卡：非无限次的计次卡
     * 3-会员卡：写死0
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询用户卡列表", notes = "根据卡类型和可用状态分页查询用户持有的卡列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<PageResultVO<UserCardVO>> getUserCardList(@RequestBody @Valid UserCardListRequest request) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }
            
            Long userId = Long.parseLong(operatorId);
            
            // 调用服务层查询用户卡列表
            PageResultVO<UserCardVO> pageResult = userCardService.getUserCardList(request, userId);
            return Result.OK(pageResult);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (Exception e) {
            log.error("查询用户卡列表失败", e);
            return Result.error("查询用户卡列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户卡详情
     * 包含卡片详情、主副卡信息、场馆客服和电话
     */
    @PostMapping("/detail")
    @ApiOperation(value = "获取用户卡详情", notes = "获取用户卡的详细信息，包含主副卡信息和场馆联系方式")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<UserCardDetailVO> getUserCardDetail(@RequestBody @Valid UserCardDetailRequest request) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }

            Long userId = Long.parseLong(operatorId);

            // 调用服务层查询用户卡详情
            UserCardDetailVO result = userCardService.getUserCardDetail(request, userId);
            return Result.OK(result);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (RuntimeException e) {
            log.error("获取用户卡详情失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取用户卡详情失败", e);
            return Result.error("获取用户卡详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询用户卡使用明细
     */
    @PostMapping("/usageDetail")
    @ApiOperation(value = "分页查询用户卡使用明细", notes = "分页查询用户卡的使用明细记录")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<PageResultVO<UserCardUsageDetailVO>> getUserCardUsageDetail(@RequestBody @Valid UserCardUsageDetailRequest request) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }

            Long userId = Long.parseLong(operatorId);

            // 调用服务层查询用户卡使用明细
            PageResultVO<UserCardUsageDetailVO> pageResult = userCardService.getUserCardUsageDetail(request, userId);
            return Result.OK(pageResult);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (RuntimeException e) {
            log.error("查询用户卡使用明细失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询用户卡使用明细失败", e);
            return Result.error("查询用户卡使用明细失败: " + e.getMessage());
        }
    }

    /**
     * 修改副卡信息
     * 固定传递2个副卡的手机号，没有数据传null
     * 校验规则：
     * 1. 列表长度必须为2
     * 2. 最多只能有2个有效手机号
     * 3. 手机号不能重复
     * 4. 副卡手机号不能与主卡手机号相同
     */
    @PostMapping("/updateSubCards")
    @ApiOperation(value = "修改副卡信息", notes = "修改副卡信息，固定传递2个副卡的手机号，支持数量和重复性校验")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<List<SubCardInfoVO>> updateSubCards(@RequestBody @Valid UpdateSubCardsRequest request) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }

            Long userId = Long.parseLong(operatorId);

            // 调用服务层修改副卡信息
            List<SubCardInfoVO> result = userCardService.updateSubCards(request, userId);
            return Result.OK(result);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (RuntimeException e) {
            log.error("修改副卡信息失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改副卡信息失败", e);
            return Result.error("修改副卡信息失败: " + e.getMessage());
        }
    }

    /**
     * 储值卡充值
     * 如果传了卡ID就为这个卡片充值，如果没有需要场馆ID查询这个场馆的储值卡为用户开卡
     * 支持微信支付
     */
    @PostMapping("/recharge")
    @ApiOperation(value = "储值卡充值", notes = "储值卡充值，支持为现有卡充值或新开卡充值，仅支持微信支付")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<StorageCardRechargeVO> rechargeStorageCard(@RequestBody @Valid StorageCardRechargeRequest request) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }

            Long userId = Long.parseLong(operatorId);

            // 校验参数
            if (request.getUserCardId() == null && request.getVenueId() == null) {
                return Result.error("必须传入用户卡ID或场馆ID");
            }

            // 调用服务层进行充值
            StorageCardRechargeVO result = userCardService.rechargeStorageCard(request, userId);
            return Result.OK(result);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (RuntimeException e) {
            log.error("储值卡充值失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("储值卡充值失败", e);
            return Result.error("储值卡充值失败: " + e.getMessage());
        }
    }

    /**
     * 测试退款接口（仅用于测试）
     * 根据订单号进行退款
     */
    @PostMapping("/testRefund")
    @ApiOperation(value = "测试退款接口", notes = "仅用于测试的退款接口，根据订单号进行退款")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<String> testRefund(@RequestParam String orderId,
                                   @RequestParam(required = false) String refundReason) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }

            Long userId = Long.parseLong(operatorId);

            // 调用服务层进行退款
            String result = userCardService.testRefund(orderId, userId, refundReason);
            return Result.OK(result);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (RuntimeException e) {
            log.error("退款失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("退款失败", e);
            return Result.error("退款失败: " + e.getMessage());
        }
    }

    /**
     * 获取卡片详情（用于购买）
     * 包含卡片信息和场馆客服电话等信息
     */
    @GetMapping("/cardDetail/{cardId}")
    @ApiOperation(value = "获取卡片详情", notes = "获取卡片详情信息，包含场馆客服电话等，用于购买前查看")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<CardDetailVO> getCardDetail(@PathVariable Long cardId) {
        try {
            // 调用服务层获取卡片详情
            CardDetailVO result = userCardService.getCardDetail(cardId);
            return Result.OK(result);
        } catch (RuntimeException e) {
            log.error("获取卡片详情失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取卡片详情失败", e);
            return Result.error("获取卡片详情失败: " + e.getMessage());
        }
    }

    /**
     * 购买卡片（非储值卡）
     * 支持购买计次卡等非储值卡类型，支付成功后自动开卡
     */
    @PostMapping("/purchase")
    @ApiOperation(value = "购买卡片", notes = "购买非储值卡，支付成功后自动开卡，仅支持微信支付")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<CardPurchaseVO> purchaseCard(@RequestBody @Valid CardPurchaseRequest request) {
        try {
            // 获取当前用户ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("用户未登录");
            }

            Long userId = Long.parseLong(operatorId);

            // 调用服务层进行购买
            CardPurchaseVO result = userCardService.purchaseCard(request, userId);
            return Result.OK(result);
        } catch (NumberFormatException e) {
            log.error("用户ID格式错误", e);
            return Result.error("用户ID格式错误");
        } catch (RuntimeException e) {
            log.error("购买卡片失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("购买卡片失败", e);
            return Result.error("购买卡片失败: " + e.getMessage());
        }
    }

    /**
     * 按手机号模糊查询有效的计次卡列表
     * 只返回有效的计次卡（剩余次数>0且未过期的卡片）
     */
    @PostMapping("/searchCountCardByPhone")
    @ApiOperation(value = "按手机号查询有效计次卡", notes = "按手机号模糊查询有效的计次卡列表，只返回剩余次数大于0且未过期的卡片，支持分页")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<PageResultVO<CountCardVO>> searchCountCardByPhone(@RequestBody @Valid SearchCountCardByPhoneRequest request) {
        try {
            // 调用服务层查询计次卡列表
            PageResultVO<CountCardVO> result = userCardService.searchCountCardByPhone(request);
            return Result.OK(result);
        } catch (RuntimeException e) {
            log.error("按手机号查询计次卡失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("按手机号查询计次卡失败", e);
            return Result.error("查询计次卡失败: " + e.getMessage());
        }
    }

    /**
     * 扣减计次卡次数
     * 对指定的计次卡进行次数扣减操作
     */
    @PostMapping("/deductCount")
    @ApiOperation(value = "扣减计次卡次数", notes = "对指定的计次卡进行次数扣减，会记录操作明细")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<String> deductCountCard(@RequestBody @Valid DeductCountCardRequest request) {
        try {
            // 获取当前操作员ID
            String operatorId = commonUtil.getOperatorId();
            if (operatorId == null) {
                return Result.error("操作员未登录");
            }

            // 调用服务层进行次数扣减
            String result = userCardService.deductCountCard(request, operatorId);
            return Result.OK(result);
        } catch (RuntimeException e) {
            log.error("扣减计次卡次数失败", e);
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("扣减计次卡次数失败", e);
            return Result.error("扣减次数失败: " + e.getMessage());
        }
    }
}
