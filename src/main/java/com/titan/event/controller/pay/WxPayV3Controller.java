package com.titan.event.controller.pay;

import cn.binarywang.wx.miniapp.api.WxMaOrderShippingService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.shop.request.shipping.OrderKeyBean;
import cn.binarywang.wx.miniapp.bean.shop.request.shipping.PayerBean;
import cn.binarywang.wx.miniapp.bean.shop.request.shipping.ShippingListBean;
import cn.binarywang.wx.miniapp.bean.shop.request.shipping.WxMaOrderShippingInfoUploadRequest;
import cn.binarywang.wx.miniapp.bean.shop.response.WxMaOrderShippingInfoBaseResponse;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ijpay.core.kit.HttpKit;
import com.ijpay.core.kit.WxPayKit;
import com.titan.event.constant.OrderStatusConstant;
import com.titan.event.constant.RedisKeyConstant;
import com.titan.event.entity.VenueBooking;
import com.titan.event.entity.VenueBookingOrder;
import com.titan.event.entity.UserInfo;
import com.titan.event.entity.VenueOrder;
import com.titan.event.entity.VenueMemberCard;
import com.titan.event.entity.VenueMemberCardUser;
import com.titan.event.entity.VenueMemberCardDetail;
import com.titan.event.entity.pay.PayOrder;
import com.titan.event.entity.pay.WxPayV3Bean;
import com.titan.event.enums.*;
import com.titan.event.enums.CardOperationType;
import com.titan.event.mapper.*;
import com.titan.event.redis.RedisService;
import com.titan.event.request.pay.PayRequest;
import com.titan.event.vo.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>IJPay 让支付触手可及，封装了微信支付、支付宝支付、银联支付常用的支付方式以及各种常用的接口。</p>
 *
 * <p>不依赖任何第三方 mvc 框架，仅仅作为工具使用简单快速完成支付模块的开发，可轻松嵌入到任何系统里。 </p>
 *
 * <p>IJPay 交流群: 723992875、864988890</p>
 *
 * <p>Node.js 版: <a href="https://gitee.com/javen205/TNWX">https://gitee.com/javen205/TNWX</a></p>
 *
 * <p>微信支付 v3 接口示例</p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wxPay")
@Slf4j
public class WxPayV3Controller {
    private final static int OK = 200;

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Autowired
    private VenueOrderMapper venueOrderMapper;

    @Autowired
    private VenueBookingMapper venueBookingMapper;

    @Autowired
    private VenueBookingOrderMapper venueBookingOrderMapper;

    @Autowired
    private VenueMemberCardUserMapper venueMemberCardUserMapper;

    @Autowired
    private VenueMemberCardDetailMapper venueMemberCardDetailMapper;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private VenueMemberCardMapper venueMemberCardMapper;

    @Resource
    private WxPayV3Bean wxPayV3Bean;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisService redisService;

    @ApiOperation("获取小程序支付参数")
    @PostMapping("/getJsApiParams")
    public Result getJsApiParams(@RequestBody PayRequest request) {
        Map<String, String> payParams;
        // 生成小程序调起支付的参数
        try {
            payParams = WxPayKit.jsApiCreateSign(
                    wxPayV3Bean.getAppId(),
                    request.getId(),
                    wxPayV3Bean.getKeyPath()
            );
        } catch (Exception e) {
            return Result.error("error");
        }
        return Result.ok(payParams);
    }

    @ApiOperation("支付通知")
    @RequestMapping(value = "/payNotify", method = {RequestMethod.POST, RequestMethod.GET})
    @Transactional(rollbackFor = Exception.class)
    public void payNotify(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> map = new HashMap<>(12);
        String outTradeNo = null;
        String transactionId = null;
        RLock lock = null;
        try {
            // 1. 验证通知签名
            String result = verifyNotifySignature(request);
            if (StrUtil.isEmpty(result)) {
                handleNotifyError(response, "签名验证失败");
                return;
            }

            // 2. 解析通知数据
            log.info("准备解析的JSON字符串: [{}]", result);
            
            // 清理潜在的BOM和不可见字符
            String cleanResult = result.trim();
            if (cleanResult.startsWith("\uFEFF")) {
                cleanResult = cleanResult.substring(1);
            }
            
            try {
                JSON json = JSONUtil.parse(cleanResult);
                
                // 获取支付订单号
                outTradeNo = (String) json.getByPath("out_trade_no");
                if (StrUtil.isEmpty(outTradeNo)) {
                    log.warn("从JSON中未找到out_trade_no字段，尝试读取原始JSON: {}", cleanResult);
                    handleNotifyError(response, "订单号为空");
                    return;
                }
    
                // 获取微信支付系统生成的订单号
                transactionId = (String) json.getByPath("transaction_id");
                if (StrUtil.isEmpty(transactionId)) {
                    log.warn("微信支付订单号为空, 订单号: {}", outTradeNo);
                }
    
                // 4. 获取分布式锁，防止重复处理
                String lockKey = RedisKeyConstant.PAY_NOTIFY_LOCK_PREFIX + outTradeNo;
                lock = redissonClient.getLock(lockKey);
                if (!lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                    log.warn("获取支付通知处理锁失败, 订单号: {}", outTradeNo);
                    handleNotifySuccess(response); // 返回成功，避免微信重复通知
                    return;
                }
    
                // 5. 获取支付状态
                String tradeState = (String) json.getByPath("trade_state");
                if (StrUtil.isEmpty(tradeState)) {
                    handleNotifyError(response, "支付状态为空");
                    return;
                }

                // 6. 查询订单
                PayOrder payOrder = payOrderMapper.findByOrderId(outTradeNo);
                if (payOrder == null) {
                    log.error("支付订单不存在, 订单号: {}", outTradeNo);
                    handleNotifyError(response, "订单不存在");
                    return;
                }

                // 订单已支付，直接返回成功
                if (OrderStatusConstant.PayStatus.SUCCESS.equals(payOrder.getPayStatus())) {
                    handleNotifySuccess(response);
                    return;
                }

                // 7. 处理支付结果
                if ("SUCCESS".equals(tradeState) || "REFUND".equals(tradeState)) {
                    // 获取支付完成时间
                    String successTime = (String) json.getByPath("success_time");
                    LocalDateTime payTime = LocalDateTime.now();
                    if (StrUtil.isNotEmpty(successTime)) {
                        try {
                            payTime = OffsetDateTime.parse(successTime).toLocalDateTime();
                            log.info("解析支付完成时间成功, 订单号: {}, 支付时间: {}", outTradeNo, payTime);
                        } catch (Exception e) {
                            log.warn("解析支付完成时间失败, 使用当前时间作为支付时间, 订单号: {}, 时间字符串: {}", outTradeNo, successTime, e);
                        }
                    }

                    // 更新支付订单状态
                    payOrder.setPayStatus(OrderStatusConstant.PayStatus.SUCCESS);
                    payOrder.setTransactionId(transactionId);
                    payOrder.setPayNotifyTime(payTime);
                    payOrder.setPayNotifyData(result);
                    payOrder.setUpdateTime(LocalDateTime.now());
                    payOrderMapper.save(payOrder);
                    log.info("支付订单状态更新成功, 订单号: {}", outTradeNo);

                    // 8. 更新主订单状态
                    final String finalOrderId = outTradeNo;
                    List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) -> 
                        cb.equal(root.get("orderId"), finalOrderId));
                    
                    if (!venueOrderList.isEmpty()) {
                        VenueOrder venueOrder = venueOrderList.get(0);
                        // 只更新订单的更新时间，状态由PayOrder.payStatus维护
                        venueOrder.setUpdateTime(LocalDateTime.now());
                        venueOrderMapper.save(venueOrder);
                        log.info("主订单状态更新成功, 订单号: {}, 订单类型: {}", outTradeNo, venueOrder.getOrderType());

                        // 9. 如果是场地预订订单，更新预订状态
                        if (OrderStatusConstant.OrderType.BOOKING == venueOrder.getOrderType()) {
                            try {
                                // 更新VenueBooking表状态
                                List<VenueBooking> bookingList = venueBookingMapper.findByOrderId(finalOrderId);
                                
                                if (!bookingList.isEmpty()) {
                                    log.info("开始更新VenueBooking表预订状态, 订单号: {}, 记录数: {}", outTradeNo, bookingList.size());
                                    for (VenueBooking booking : bookingList) {
                                        try {
                                            Integer oldStatus = booking.getBookingStatus();
                                            booking.setBookingStatus(1); // 更新为已支付状态
                                            booking.setPaymentTime(new Date()); // 设置支付时间
                                            venueBookingMapper.save(booking);
                                            log.info("VenueBooking记录状态更新成功, 预订ID: {}, 状态从 {} 更新为 1", booking.getBookingId(), oldStatus);
                                        } catch (Exception e) {
                                            log.error("更新单条VenueBooking记录失败, 预订ID: {}, 尝试重试一次", booking.getBookingId(), e);
                                            // 重试一次
                                            try {
                                                // 重新获取实体并更新
                                                VenueBooking refreshBooking = venueBookingMapper.findById(booking.getBookingId()).orElse(null);
                                                if (refreshBooking != null) {
                                                    refreshBooking.setBookingStatus(1);
                                                    refreshBooking.setPaymentTime(new Date());
                                                    venueBookingMapper.save(refreshBooking);
                                                    log.info("VenueBooking记录状态重试更新成功, 预订ID: {}", booking.getBookingId());
                                                }
                                            } catch (Exception retryEx) {
                                                log.error("VenueBooking记录状态重试更新失败, 预订ID: {}", booking.getBookingId(), retryEx);
                                            }
                                        }
                                    }
                                    log.info("VenueBooking表预订状态更新成功, 订单号: {}, 更新记录数: {}", outTradeNo, bookingList.size());
                                } else {
                                    log.warn("未找到VenueBooking预订记录, 订单号: {}", outTradeNo);
                                }
                                
                                // 同时更新VenueBookingOrder表状态
                                List<VenueBookingOrder> bookingOrderList = venueBookingOrderMapper.findByOrderId(finalOrderId);
                                if (!bookingOrderList.isEmpty()) {
                                    log.info("开始更新VenueBookingOrder表预订状态, 订单号: {}, 记录数: {}", outTradeNo, bookingOrderList.size());
                                    for (VenueBookingOrder bookingOrder : bookingOrderList) {
                                        try {
                                            Integer oldStatus = bookingOrder.getBookingStatus();
                                            bookingOrder.setBookingStatus(1); // 更新为已支付状态
                                            bookingOrder.setUpdateTime(LocalDateTime.now());
                                            venueBookingOrderMapper.save(bookingOrder);
                                            log.info("VenueBookingOrder记录状态更新成功, 记录ID: {}, 状态从 {} 更新为 1", bookingOrder.getId(), oldStatus);
                                        } catch (Exception e) {
                                            log.error("更新单条VenueBookingOrder记录失败, 记录ID: {}, 尝试重试一次", bookingOrder.getId(), e);
                                            // 重试一次
                                            try {
                                                // 重新获取实体并更新
                                                VenueBookingOrder refreshOrder = venueBookingOrderMapper.findById(bookingOrder.getId()).orElse(null);
                                                if (refreshOrder != null) {
                                                    refreshOrder.setBookingStatus(1);
                                                    refreshOrder.setUpdateTime(LocalDateTime.now());
                                                    venueBookingOrderMapper.save(refreshOrder);
                                                    log.info("VenueBookingOrder记录状态重试更新成功, 记录ID: {}", bookingOrder.getId());
                                                }
                                            } catch (Exception retryEx) {
                                                log.error("VenueBookingOrder记录状态重试更新失败, 记录ID: {}", bookingOrder.getId(), retryEx);
                                            }
                                        }
                                    }
                                    log.info("VenueBookingOrder表预订状态更新成功, 订单号: {}, 更新记录数: {}", outTradeNo, bookingOrderList.size());
                                } else {
                                    log.warn("未找到VenueBookingOrder预订记录, 订单号: {}", outTradeNo);
                                }
                            } catch (Exception e) {
                                log.error("更新预订状态异常, 订单号: {}", outTradeNo, e);
                                // 不抛出异常，避免影响支付流程
                            }
                        }
                    } else {
                        log.warn("未找到主订单记录, 订单号: {}", outTradeNo);
                    }

                    // 10. 处理储值卡充值订单
                    if (outTradeNo.startsWith("RECHARGE_")) {
                        try {
                            handleStorageCardRecharge(payOrder);
                            log.info("储值卡充值处理完成, 订单号: {}", outTradeNo);
                        } catch (Exception e) {
                            log.error("处理储值卡充值异常, 订单号: {}", outTradeNo, e);
                            // 不抛出异常，避免影响支付流程
                        }
                    }

                    // 11. 处理卡片购买订单
                    if (outTradeNo.startsWith("CARD_")) {
                        try {
                            handleCardPurchase(payOrder);
                            log.info("卡片购买处理完成, 订单号: {}", outTradeNo);
                        } catch (Exception e) {
                            log.error("处理卡片购买异常, 订单号: {}", outTradeNo, e);
                            // 不抛出异常，避免影响支付流程
                        }
                    }

                    log.info("支付成功处理完成, 订单号: {}", outTradeNo);
                } else {
                    // 其他状态，记录但不处理
                    log.info("支付状态非成功，状态值: {}, 订单号: {}", tradeState, outTradeNo);
                }

                // 9. 返回成功响应
                handleNotifySuccess(response);
            } catch (Exception e) {
                log.error("解析通知数据异常, 订单号: {}, 原始数据: {}", outTradeNo, result, e);
                handleNotifyError(response, "系统异常");
            }
        } catch (Exception e) {
            log.error("处理支付通知异常, 订单号: {}", outTradeNo, e);
            handleNotifyError(response, "系统异常");
        } finally {
            // 释放锁
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation("退款通知")
    @RequestMapping(value = "/refundNotify", method = {RequestMethod.POST, RequestMethod.GET})
    @Transactional(rollbackFor = Exception.class)
    public void refundNotify(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> map = new HashMap<>(12);
        String outTradeNo = null;
        String refundId = null;
        RLock lock = null;
        try {
            // 1. 验证通知签名
            String result = verifyNotifySignature(request);
            if (StrUtil.isEmpty(result)) {
                handleNotifyError(response, "签名验证失败");
                return;
            }

            // 2. 解析通知数据
            log.info("准备解析的JSON字符串: [{}]", result);
            
            // 清理潜在的BOM和不可见字符
            String cleanResult = result.trim();
            if (cleanResult.startsWith("\uFEFF")) {
                cleanResult = cleanResult.substring(1);
            }
            
            try {
                JSON json = JSONUtil.parse(cleanResult);
                
                // 获取支付订单号
                outTradeNo = (String) json.getByPath("out_trade_no");
                
                if (StrUtil.isEmpty(outTradeNo)) {
                    log.warn("从JSON中未找到out_trade_no字段，尝试读取原始JSON: {}", cleanResult);
                    handleNotifyError(response, "订单号为空");
                    return;
                }

                // 获取退款单号
                refundId = (String) json.getByPath("refund_id");
                if (StrUtil.isEmpty(refundId)) {
                    log.warn("退款单号为空, 订单号: {}", outTradeNo);
                }

                // 3. 获取分布式锁，防止重复处理
                String lockKey = RedisKeyConstant.REFUND_NOTIFY_LOCK_PREFIX + outTradeNo;
                lock = redissonClient.getLock(lockKey);
                if (!lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                    log.warn("获取退款通知处理锁失败, 订单号: {}", outTradeNo);
                    handleNotifySuccess(response); // 返回成功，避免微信重复通知
                    return;
                }

                // 4. 获取退款状态
                String refundStatus = (String) json.getByPath("refund_status");
                if (StrUtil.isEmpty(refundStatus)) {
                    handleNotifyError(response, "退款状态为空");
                    return;
                }

                // 5. 查询订单
                PayOrder payOrder = payOrderMapper.findByOrderId(outTradeNo);
                if (payOrder == null) {
                    log.error("支付订单不存在, 订单号: {}", outTradeNo);
                    handleNotifyError(response, "订单不存在");
                    return;
                }

                // 订单已退款，直接返回成功
                if (OrderStatusConstant.PayStatus.REFUND.equals(payOrder.getPayStatus())) {
                    handleNotifySuccess(response);
                    return;
                }

                // 6. 处理退款结果
                if ("SUCCESS".equals(refundStatus)) {
                    // 获取退款完成时间
                    String successTime = (String) json.getByPath("success_time");
                    LocalDateTime refundTime = LocalDateTime.now();
                    if (StrUtil.isNotEmpty(successTime)) {
                        try {
                            refundTime = OffsetDateTime.parse(successTime).toLocalDateTime();
                            log.info("解析退款完成时间成功, 订单号: {}, 退款时间: {}", outTradeNo, refundTime);
                        } catch (Exception e) {
                            log.warn("解析退款完成时间失败, 使用当前时间作为退款时间, 订单号: {}, 时间字符串: {}", outTradeNo, successTime, e);
                        }
                    }

                    // 更新支付订单状态
                    payOrder.setPayStatus(OrderStatusConstant.PayStatus.REFUND);
                    payOrder.setRefundId(refundId);
                    payOrder.setRefundNotifyTime(refundTime);
                    payOrder.setRefundNotifyData(result);
                    payOrder.setUpdateTime(LocalDateTime.now());
                    payOrderMapper.save(payOrder);
                    log.info("支付订单退款状态更新成功, 订单号: {}", outTradeNo);

                    // 7. 更新主订单状态
                    final String finalOrderId = outTradeNo;
                    List<VenueOrder> venueOrderList = venueOrderMapper.findAll((root, query, cb) -> 
                        cb.equal(root.get("orderId"), finalOrderId));
                    
                    if (!venueOrderList.isEmpty()) {
                        VenueOrder venueOrder = venueOrderList.get(0);
                        venueOrder.setUpdateTime(LocalDateTime.now());
                        venueOrderMapper.save(venueOrder);
                        log.info("主订单退款状态更新成功, 订单号: {}, 订单类型: {}", outTradeNo, venueOrder.getOrderType());

                        // 8. 如果是场地预订订单，更新预订状态
                        if (OrderStatusConstant.OrderType.BOOKING == venueOrder.getOrderType()) {
                            try {
                                // 更新VenueBooking表状态
                                List<VenueBooking> bookingList = venueBookingMapper.findByOrderId(finalOrderId);
                                
                                if (!bookingList.isEmpty()) {
                                    log.info("开始更新VenueBooking表预订状态, 订单号: {}, 记录数: {}", outTradeNo, bookingList.size());
                                    for (VenueBooking booking : bookingList) {
                                        try {
                                            Integer oldStatus = booking.getBookingStatus();
                                            booking.setBookingStatus(2); // 更新为已取消状态
                                            booking.setPaymentTime(new Date()); // 设置支付时间
                                            venueBookingMapper.save(booking);
                                            log.info("VenueBooking记录状态更新成功, 预订ID: {}, 状态从 {} 更新为 2", booking.getBookingId(), oldStatus);
                                        } catch (Exception e) {
                                            log.error("更新单条VenueBooking记录失败, 预订ID: {}, 尝试重试一次", booking.getBookingId(), e);
                                            // 重试一次
                                            try {
                                                // 重新获取实体并更新
                                                VenueBooking refreshBooking = venueBookingMapper.findById(booking.getBookingId()).orElse(null);
                                                if (refreshBooking != null) {
                                                    refreshBooking.setBookingStatus(2);
                                                    refreshBooking.setPaymentTime(new Date());
                                                    venueBookingMapper.save(refreshBooking);
                                                    log.info("VenueBooking记录状态重试更新成功, 预订ID: {}", booking.getBookingId());
                                                }
                                            } catch (Exception retryEx) {
                                                log.error("VenueBooking记录状态重试更新失败, 预订ID: {}", booking.getBookingId(), retryEx);
                                            }
                                        }
                                    }
                                    log.info("VenueBooking表预订状态更新成功, 订单号: {}, 更新记录数: {}", outTradeNo, bookingList.size());
                                } else {
                                    log.warn("未找到VenueBooking预订记录, 订单号: {}", outTradeNo);
                                }
                                
                                // 同时更新VenueBookingOrder表状态
                                List<VenueBookingOrder> bookingOrderList = venueBookingOrderMapper.findByOrderId(finalOrderId);
                                if (!bookingOrderList.isEmpty()) {
                                    log.info("开始更新VenueBookingOrder表预订状态, 订单号: {}, 记录数: {}", outTradeNo, bookingOrderList.size());
                                    for (VenueBookingOrder bookingOrder : bookingOrderList) {
                                        try {
                                            Integer oldStatus = bookingOrder.getBookingStatus();
                                            bookingOrder.setBookingStatus(2); // 更新为已取消状态
                                            bookingOrder.setUpdateTime(LocalDateTime.now());
                                            venueBookingOrderMapper.save(bookingOrder);
                                            log.info("VenueBookingOrder记录状态更新成功, 记录ID: {}, 状态从 {} 更新为 2", bookingOrder.getId(), oldStatus);
                                        } catch (Exception e) {
                                            log.error("更新单条VenueBookingOrder记录失败, 记录ID: {}, 尝试重试一次", bookingOrder.getId(), e);
                                            // 重试一次
                                            try {
                                                // 重新获取实体并更新
                                                VenueBookingOrder refreshOrder = venueBookingOrderMapper.findById(bookingOrder.getId()).orElse(null);
                                                if (refreshOrder != null) {
                                                    refreshOrder.setBookingStatus(2);
                                                    refreshOrder.setUpdateTime(LocalDateTime.now());
                                                    venueBookingOrderMapper.save(refreshOrder);
                                                    log.info("VenueBookingOrder记录状态重试更新成功, 记录ID: {}", bookingOrder.getId());
                                                }
                                            } catch (Exception retryEx) {
                                                log.error("VenueBookingOrder记录状态重试更新失败, 记录ID: {}", bookingOrder.getId(), retryEx);
                                            }
                                        }
                                    }
                                    log.info("VenueBookingOrder表预订状态更新成功, 订单号: {}, 更新记录数: {}", outTradeNo, bookingOrderList.size());
                                } else {
                                    log.warn("未找到VenueBookingOrder预订记录, 订单号: {}", outTradeNo);
                                }
                            } catch (Exception e) {
                                log.error("更新预订退款状态异常, 订单号: {}", outTradeNo, e);
                                // 不抛出异常，避免影响退款流程
                            }
                        }
                    } else {
                        log.warn("未找到主订单记录, 订单号: {}", outTradeNo);
                    }

                    log.info("退款成功处理完成, 订单号: {}", outTradeNo);
                } else {
                    // 其他状态，记录但不处理
                    log.info("退款状态非成功，状态值: {}, 订单号: {}", refundStatus, outTradeNo);
                }

                // 9. 返回成功响应
                handleNotifySuccess(response);
            } catch (Exception e) {
                log.error("解析通知数据异常, 订单号: {}, 原始数据: {}", outTradeNo, result, e);
                handleNotifyError(response, "系统异常");
            }
        } catch (Exception e) {
            log.error("处理退款通知异常, 订单号: {}, 退款单号: {}", outTradeNo, refundId, e);
            handleNotifyError(response, "系统异常");
        } finally {
            // 释放锁
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @ApiOperation("API退款通知")
    @RequestMapping(value = "/apiRefundNotify", method = {RequestMethod.POST, RequestMethod.GET})
    @Transactional(rollbackFor = Exception.class)
    public void apiRefundNotify(HttpServletRequest request, HttpServletResponse response) {
        // API退款通知处理逻辑与普通退款通知基本相同
        refundNotify(request, response);
    }

    private String verifyNotifySignature(HttpServletRequest request) throws Exception {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");

        log.info("通知签名信息 - timestamp:{} nonce:{} serialNo:{} signature:{}",
                timestamp, nonce, serialNo, signature);

        String result = HttpKit.readData(request);
        log.info("通知密文: {}", result);

        // 验证签名
        String plainText = WxPayKit.verifyNotify(serialNo, result, signature, nonce, timestamp,
                wxPayV3Bean.getApiKey3(), wxPayV3Bean.getPlatformCertPath());
        log.info("通知明文: {}", plainText);

        return plainText;
    }

    private void handleNotifySuccess(HttpServletResponse response) {
        try {
            Map<String, String> map = new HashMap<>(2);
            map.put("code", "SUCCESS");
            map.put("message", "SUCCESS");
            response.setStatus(200);
            writeResponse(response, map);
        } catch (Exception e) {
            log.error("处理成功响应异常", e);
        }
    }

    private void handleNotifyError(HttpServletResponse response, String message) {
        try {
            Map<String, String> map = new HashMap<>(2);
            map.put("code", "ERROR");
            map.put("message", message);
            response.setStatus(500);
            writeResponse(response, map);
        } catch (Exception e) {
            log.error("处理错误响应异常", e);
        }
    }

    private void writeResponse(HttpServletResponse response, Map<String, String> map) throws Exception {
        response.setHeader("Content-type", ContentType.JSON.toString());
        response.getOutputStream().write(JSONUtil.toJsonStr(map).getBytes(StandardCharsets.UTF_8));
        response.flushBuffer();
    }

    /**
     * 处理储值卡充值订单
     */
    private void handleStorageCardRecharge(PayOrder payOrder) {
        try {
            log.info("开始处理储值卡充值订单, 订单号: {}, 用户ID: {}, 金额: {}",
                    payOrder.getOrderId(), payOrder.getUserId(), payOrder.getAmount());

            // 1. 根据订单中的userCardId查找具体的储值卡
            if (payOrder.getUserCardId() == null) {
                log.error("订单中未关联用户卡ID, 订单号: {}", payOrder.getOrderId());
                return;
            }

            Optional<VenueMemberCardUser> storageCardOpt = venueMemberCardUserMapper.findById(payOrder.getUserCardId());
            if (!storageCardOpt.isPresent()) {
                log.error("未找到对应的用户储值卡, 用户卡ID: {}, 订单号: {}", payOrder.getUserCardId(), payOrder.getOrderId());
                return;
            }

            VenueMemberCardUser storageCard = storageCardOpt.get();

            // 验证卡片类型和用户权限
            if (storageCard.getCardType() != 1) {
                log.error("卡片类型不是储值卡, 卡片类型: {}, 订单号: {}", storageCard.getCardType(), payOrder.getOrderId());
                return;
            }

            if (!payOrder.getUserId().equals(storageCard.getUserId())) {
                log.error("订单用户与卡片用户不匹配, 订单用户: {}, 卡片用户: {}, 订单号: {}",
                        payOrder.getUserId(), storageCard.getUserId(), payOrder.getOrderId());
                return;
            }
            BigDecimal currentBalance = storageCard.getBalance() != null ? storageCard.getBalance() : BigDecimal.ZERO;
            BigDecimal rechargeAmount = BigDecimal.valueOf(payOrder.getAmount());
            BigDecimal newBalance = currentBalance.add(rechargeAmount);

            // 2. 更新储值卡余额
            storageCard.setBalance(newBalance);
            storageCard.setUpdateTime(LocalDateTime.now());
            venueMemberCardUserMapper.save(storageCard);

            // 3. 记录充值明细
            VenueMemberCardDetail detail = new VenueMemberCardDetail();
            detail.setUserId(payOrder.getUserId());
            detail.setCardId(storageCard.getCardId());
            detail.setCardName(storageCard.getCardName());
            detail.setUserCardId(storageCard.getUserCardId());
            detail.setVenueId(storageCard.getVenueId());
            detail.setVenueName(storageCard.getVenueName());
            detail.setOperationType(CardOperationType.RECHARGE.getCode()); // 充值
            detail.setAmount(rechargeAmount);
            detail.setOperationTime(LocalDateTime.now());
            detail.setOperationDescription("储值卡充值");
            detail.setOperator("系统");
            detail.setOperationSource(1); // 管理端
            detail.setBalance(newBalance);
            detail.setDelFlag("0");
            detail.setCreateTime(LocalDateTime.now());
            detail.setUpdateTime(LocalDateTime.now());

            venueMemberCardDetailMapper.save(detail);

            log.info("储值卡充值处理成功, 用户ID: {}, 订单号: {}, 充值金额: {}, 余额: {} -> {}",
                    payOrder.getUserId(), payOrder.getOrderId(), rechargeAmount, currentBalance, newBalance);
        } catch (Exception e) {
            log.error("处理储值卡充值失败, 订单号: {}", payOrder.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 处理卡片购买订单
     */
    private void handleCardPurchase(PayOrder payOrder) {
        try {
            log.info("开始处理卡片购买订单, 订单号: {}, 用户ID: {}, 金额: {}, 卡片ID: {}",
                    payOrder.getOrderId(), payOrder.getUserId(), payOrder.getAmount(), payOrder.getCardId());

            // 1. 验证订单中的卡片ID
            if (payOrder.getCardId() == null) {
                log.error("订单中未关联卡片ID, 订单号: {}", payOrder.getOrderId());
                return;
            }

            // 2. 查找卡片信息
            Optional<VenueMemberCard> cardOpt = venueMemberCardMapper.findById(payOrder.getCardId());
            if (!cardOpt.isPresent()) {
                log.error("卡片不存在, 卡片ID: {}, 订单号: {}", payOrder.getCardId(), payOrder.getOrderId());
                return;
            }

            VenueMemberCard card = cardOpt.get();

            // 3. 查找用户信息
            Optional<UserInfo> userInfoOpt = userInfoMapper.findById(payOrder.getUserId());
            if (!userInfoOpt.isPresent()) {
                log.error("用户不存在, 用户ID: {}, 订单号: {}", payOrder.getUserId(), payOrder.getOrderId());
                return;
            }

            UserInfo userInfo = userInfoOpt.get();

            // 4. 为用户创建卡片记录
            VenueMemberCardUser newUserCard = new VenueMemberCardUser();
            newUserCard.setUserId(payOrder.getUserId());
            newUserCard.setUserName(userInfo.getNickname());
            newUserCard.setCardId(card.getCardId());
            newUserCard.setCardName(card.getCardName());
            newUserCard.setVenueId(card.getVenueId());
            newUserCard.setVenueName(card.getVenueName());
            newUserCard.setCardNumber(generateCardNumber(card.getCardNumberPrefix()));
            newUserCard.setCardType(card.getCardType());
            newUserCard.setUnlimitedUsage(card.getUnlimitedUsage());

            // 5. 设置卡片属性
            if (card.getCardType() == 1) {
                // 储值卡（理论上不会走到这里，因为储值卡走充值流程）
                newUserCard.setBalance(BigDecimal.ZERO);
            } else if (card.getCardType() == 2) {
                // 计次卡
                newUserCard.setRemainingCount(card.getUsageCount());
                newUserCard.setBalance(BigDecimal.ZERO);
            }

            // 6. 设置有效期
            LocalDateTime now = LocalDateTime.now();
            newUserCard.setValidityStartTime(now);

            if (card.getValidityType() != null) {
                switch (card.getValidityType()) {
                    case 1: // 天
                        newUserCard.setValidityEndTime(now.plusDays(card.getValidityValue()));
                        break;
                    case 2: // 月
                        newUserCard.setValidityEndTime(now.plusMonths(card.getValidityValue()));
                        break;
                    case 3: // 年
                        newUserCard.setValidityEndTime(now.plusYears(card.getValidityValue()));
                        break;
                    case 4: // 固定时间范围
                        newUserCard.setValidityStartTime(card.getValidityStartTime());
                        newUserCard.setValidityEndTime(card.getValidityEndTime());
                        break;
                    default:
                        newUserCard.setValidityEndTime(now.plusYears(1)); // 默认1年
                        break;
                }
            } else {
                newUserCard.setValidityEndTime(now.plusYears(1)); // 默认1年
            }

            newUserCard.setStatus("0"); // 正常状态
            newUserCard.setDelFlag("0");
            newUserCard.setCreateTime(now);
            newUserCard.setUpdateTime(now);

            // 7. 保存用户卡记录
            venueMemberCardUserMapper.save(newUserCard);

            // 8. 更新venue_order状态为已支付
            Optional<VenueOrder> venueOrderOpt = venueOrderMapper.findByOrderId(payOrder.getOrderId());
            if (venueOrderOpt.isPresent()) {
                VenueOrder venueOrder = venueOrderOpt.get();
                venueOrder.setUpdateTime(LocalDateTime.now());
                venueOrderMapper.save(venueOrder);
                log.info("更新venue_order状态成功, 订单号: {}", payOrder.getOrderId());
            } else {
                log.warn("未找到对应的venue_order记录, 订单号: {}", payOrder.getOrderId());
            }

            log.info("卡片购买处理成功, 用户ID: {}, 订单号: {}, 卡片: {}, 卡号: {}",
                    payOrder.getUserId(), payOrder.getOrderId(), card.getCardName(), newUserCard.getCardNumber());
        } catch (Exception e) {
            log.error("处理卡片购买失败, 订单号: {}", payOrder.getOrderId(), e);
            throw e;
        }
    }

    /**
     * 生成卡号
     */
    private String generateCardNumber(String prefix) {
        String cardPrefix = prefix != null ? prefix : "CARD";
        return cardPrefix + System.currentTimeMillis();
    }
}
