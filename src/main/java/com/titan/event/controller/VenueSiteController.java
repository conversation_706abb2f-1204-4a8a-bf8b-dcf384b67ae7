package com.titan.event.controller;

import com.titan.event.entity.VenueSite;
import com.titan.event.entity.VenueSiteSection;
import com.titan.event.request.venuesite.SiteQueryRequest;
import com.titan.event.service.IVenueSiteService;
import com.titan.event.service.IVenueSiteSectionService;
import com.titan.event.vo.Result;
import com.titan.event.vo.site.SiteQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 场馆场地管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/venue-site")
@Api(tags = "场馆场地管理接口")
public class VenueSiteController {

    @Resource
    private IVenueSiteService venueSiteService;
    
    @Resource
    private IVenueSiteSectionService venueSiteSectionService;
    
    /**
     * 根据场地类型和日期查询场地信息
     */
    @PostMapping("/info")
    @ApiOperation(value = "场地信息查询", notes = "根据场馆ID、场地类型和日期查询场地信息及分区信息")
    public Result<SiteQueryVO> querySiteByTypeAndDate(@RequestBody SiteQueryRequest request) {
        log.info("根据场馆ID、场地类型和日期查询场地信息，venueId={}，siteType={}，date={}", 
                request.getVenueId(), request.getSiteType(), request.getDate());
        
        try {
            // 调用服务查询场地信息
            SiteQueryVO response = venueSiteService.querySiteByTypeAndDate(request);
            return Result.OK(response);
        } catch (ParseException e) {
            return Result.error("日期格式不正确，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("查询场地信息异常", e);
            return Result.error("查询场地信息失败：" + e.getMessage());
        }
    }
} 