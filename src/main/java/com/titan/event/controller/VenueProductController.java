package com.titan.event.controller;

import com.titan.event.entity.VenueProduct;
import com.titan.event.request.venue.ProductDetailRequest;
import com.titan.event.service.IVenueProductService;
import com.titan.event.vo.Result;
import com.titan.event.vo.VenueProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 场馆商品控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/venue/product")
@Api(tags = "场馆商品接口")
public class VenueProductController {
    
    @Resource
    private IVenueProductService venueProductService;
    
    /**
     * 获取商品详情
     * 
     * @param request 请求参数
     * @return 商品详情信息
     */
    @PostMapping("/detail")
    @ApiOperation(value = "获取商品详情", notes = "根据商品ID获取商品详情信息，包含处理后的销量等数据")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = VenueProductVO.class),
        @ApiResponse(code = 500, message = "操作失败")
    })
    public Result<VenueProductVO> getProductDetail(@Valid @RequestBody ProductDetailRequest request) {
        if (request == null || request.getProductId() == null) {
            return Result.error("商品ID不能为空");
        }
        
        VenueProductVO product = venueProductService.getProductDetailVO(request.getProductId());
        if (product == null) {
            return Result.error("商品不存在或已下架");
        }
        
        return Result.OK(product);
    }
} 