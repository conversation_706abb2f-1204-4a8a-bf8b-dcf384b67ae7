package com.titan.event.controller;

import com.titan.event.request.ProductOrderRequest;
import com.titan.event.request.VenueBookingRequest;
import com.titan.event.request.VenueOrderDetailRequest;
import com.titan.event.request.VenueOrderListRequest;
import com.titan.event.request.VenueOrderRefundRequest;
import com.titan.event.request.VenueOrderVerifyRequest;
import com.titan.event.request.VenueRefundOrderListRequest;
import com.titan.event.request.VenueOrderPayRequest;
import com.titan.event.response.CreateOrderResponse;
import com.titan.event.response.VenueOrderPayResponse;
import com.titan.event.service.VenueOrderService;
import com.titan.event.service.IUserInfoService;
import com.titan.event.util.CommonUtil;
import com.titan.event.vo.PageResultVO;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;
import com.titan.event.vo.VenueOrderDetailVO;
import com.titan.event.vo.VenueOrderListVO;
import com.titan.event.vo.VenueRefundOrderListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 场馆订单控制器
 * 处理场地预订和商品购买的下单
 */
@RestController
@RequestMapping("/venueOrder")
@Slf4j
@Api(tags = "场馆订单接口")
public class VenueOrderController {

    @Autowired
    private VenueOrderService venueOrderService;
    
    @Autowired
    private CommonUtil commonUtil;
    
    @Autowired
    private IUserInfoService userInfoService;

    /**
     * 场地预订下单接口
     * 创建订单的同时会生成12位数字的核销码
     * 
     * @param request 场地预订请求
     * @return 订单创建结果
     */
    @PostMapping("/createBookingOrder")
    @ApiOperation(value = "创建场地预订订单", notes = "专用于场地预订的下单接口，会生成核销码")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<CreateOrderResponse> createBookingOrder(@RequestBody @Valid VenueBookingRequest request) {
        try {
            // 调用服务层处理业务逻辑
            CreateOrderResponse response = venueOrderService.createBookingOrder(request);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("创建场地预订订单失败", e);
            return Result.error("创建场地预订订单失败: " + e.getMessage());
        }
    }

    /**
     * 商品购买下单接口
     * 创建订单的同时会生成12位数字的核销码
     * 
     * @param request 商品购买请求
     * @return 订单创建结果
     */
    @PostMapping("/createProductOrder")
    @ApiOperation(value = "创建商品购买订单", notes = "专用于商品购买的下单接口，会生成核销码")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<CreateOrderResponse> createProductOrder(@RequestBody @Valid ProductOrderRequest request) {
        try {
            // 调用服务层处理业务逻辑
            CreateOrderResponse response = venueOrderService.createProductOrder(request);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("创建商品购买订单失败", e);
            return Result.error("创建商品购买订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 场馆订单退款接口
     * 
     * @param request 退款请求，包含订单ID
     * @return 退款处理结果
     */
    @PostMapping("/refundOrder")
    @ApiOperation(value = "场馆订单退款", notes = "根据订单ID申请退款")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = Result.class),
            @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result refundOrder(@RequestBody @Valid VenueOrderRefundRequest request) {
        try {
            // 调用服务层处理退款业务逻辑
            return venueOrderService.refundOrder(request);
        } catch (Exception e) {
            log.error("场馆订单退款失败，订单ID: " + request.getOrderId(), e);
            return Result.error("场馆订单退款失败: " + e.getMessage());
        }
    }

    @PostMapping("/refundApiOrder")
    @ApiOperation(value = "场馆订单退款", notes = "根据订单ID申请退款")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = Result.class),
            @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result refundApiOrder(@RequestBody @Valid VenueOrderRefundRequest request) {
        try {
            String key = generateRefundKey(request.getOrderId());
            if(!key.equals(request.getKey())){
                return Result.error("场馆订单退款失败");
            }
            // 调用服务层处理退款业务逻辑
            return venueOrderService.refundApiOrder(request);
        } catch (Exception e) {
            log.error("场馆订单退款失败，订单ID: " + request.getOrderId(), e);
            return Result.error("场馆订单退款失败: " + e.getMessage());
        }
    }

    /**
     * 生成退款密钥
     */
    private String generateRefundKey(String orderId) {
        String originalString = orderId + "titan_venue_api";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(originalString.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
    
    /**
     * 查询订单详情接口
     * 包含订单基本信息、支付信息、预订/商品明细、核销码信息、
     * 订单状态(1-待支付, 2-待核销, 3-已完成, 4-已作废)及使用须知
     * 支持通过订单ID、退款单ID或核销码查询
     * 
     * @param request 包含订单ID、退款单ID或核销码的请求
     * @return 订单详情信息
     */
    @PostMapping("/getOrderDetail")
    @ApiOperation(value = "查询订单详情", notes = "根据订单ID、退款单ID或核销码查询订单详细信息，包含核销码、订单状态(1-待支付, 2-待核销, 3-已完成, 4-已作废)及使用须知")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<VenueOrderDetailVO> getOrderDetail(@RequestBody @Valid VenueOrderDetailRequest request) {
        try {
            // 校验参数
            if (!request.isValid()) {
                return Result.error("订单ID、退款单ID和核销码不能同时为空");
            }
            
            // 调用服务层查询订单详情
            VenueOrderDetailVO orderDetail = venueOrderService.getOrderDetail(
                    request.getOrderId(), request.getVerificationCode(), request.getRefundId());
            return Result.ok(orderDetail);
        } catch (Exception e) {
            log.error("查询订单详情失败，订单ID: " + request.getOrderId() + 
                    ", 核销码: " + request.getVerificationCode() + 
                    ", 退款单ID: " + request.getRefundId(), e);
            return Result.error("查询订单详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询订单列表接口
     * 根据状态查询当前用户的订单列表，支持按照全部、待支付、待核销、已完成、已作废状态筛选
     * 结果按照创建时间倒序排列
     * 
     * @param request 查询条件，包含状态和分页参数
     * @return 订单列表分页结果
     */
    @PostMapping("/getOrderList")
    @ApiOperation(value = "查询订单列表", notes = "按状态查询订单列表，按创建时间倒序排列")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<PageResultVO<VenueOrderListVO>> getOrderList(@RequestBody @Valid VenueOrderListRequest request) {
        try {
            // 调用服务层查询订单列表
            PageResultVO<VenueOrderListVO> pageResult = venueOrderService.getOrderList(request);
            return Result.ok(pageResult);
        } catch (Exception e) {
            log.error("查询订单列表失败", e);
            return Result.error("查询订单列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 订单核销接口
     * 通过核销码验证并完成订单核销，自动获取当前操作人员ID
     * 
     * @param request 包含核销码的请求
     * @return 核销处理结果
     */
    @PostMapping("/verifyOrder")
    @ApiOperation(value = "订单核销", notes = "根据核销码进行订单核销，记录核销人员ID")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result verifyOrder(@RequestBody @Valid VenueOrderVerifyRequest request) {
        try {
            // 获取当前操作人员ID
            String operatorId = commonUtil.getOperatorId();
            
            log.info("订单核销操作，核销码: {}，操作人ID: {}", request.getVerificationCode(), operatorId);
            
            // 调用服务层处理核销业务逻辑，传递操作人ID
            return venueOrderService.verifyOrder(request, operatorId);
        } catch (Exception e) {
            log.error("订单核销失败，核销码: " + request.getVerificationCode(), e);
            return Result.error("订单核销失败: " + e.getMessage());
        }
    }

    @PostMapping("/verifyApiOrder")
    @ApiOperation(value = "API订单核销", notes = "根据核销码进行API订单核销，需要提供有效密钥")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result verifyApiOrder(@RequestBody @Valid VenueOrderVerifyRequest request) {
        try {
            String key = generateRefundKey(request.getVerificationCode());
            if(!key.equals(request.getKey())){
                return Result.error("订单核销失败：密钥无效");
            }
            
            log.info("API订单核销操作，核销码: {}", request.getVerificationCode());
            
            // 调用服务层处理API核销业务逻辑
            return venueOrderService.verifyApiOrder(request);
        } catch (Exception e) {
            log.error("API订单核销失败，核销码: " + request.getVerificationCode(), e);
            return Result.error("API订单核销失败: " + e.getMessage());
        }
    }

    /**
     * 统一支付接口
     * 支持微信支付和储值卡支付两种方式
     *
     * @param request 支付请求，包含订单ID、支付方式、储值卡ID等
     * @return 支付处理结果
     */
    @PostMapping("/payOrder")
    @ApiOperation(value = "订单支付", notes = "支持微信支付和储值卡支付，储值卡支付需要验证场馆匹配和余额充足")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<VenueOrderPayResponse> payOrder(@RequestBody @Valid VenueOrderPayRequest request) {
        try {
            log.info("订单支付操作，订单ID: {}，支付方式: {}，储值卡ID: {}",
                    request.getOrderId(), request.getPayType(), request.getUserCardId());

            // 调用服务层处理支付业务逻辑
            VenueOrderPayResponse response = venueOrderService.payOrder(request);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("订单支付失败，订单ID: " + request.getOrderId(), e);
            return Result.error("订单支付失败: " + e.getMessage());
        }
    }

    /**
     * 查询退单列表接口
     * 根据退款状态查询当前用户的退款订单列表，支持按照全部、已完成、待退款、待审核、已作废状态筛选
     * 结果按照创建时间倒序排列
     * 
     * @param request 查询条件，包含退款状态和分页参数
     * @return 退单列表分页结果
     */
    @PostMapping("/getRefundOrderList")
    @ApiOperation(value = "查询退单列表", notes = "按退款状态查询退款订单列表，按创建时间倒序排列")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = Result.class),
        @ApiResponse(code = 500, message = "操作失败", response = Result.class)
    })
    public Result<PageResultVO<VenueRefundOrderListVO>> getRefundOrderList(@RequestBody @Valid VenueRefundOrderListRequest request) {
        try {
            // 调用服务层查询退单列表
            PageResultVO<VenueRefundOrderListVO> pageResult = venueOrderService.getRefundOrderList(request);
            return Result.ok(pageResult);
        } catch (Exception e) {
            log.error("查询退单列表失败", e);
            return Result.error("查询退单列表失败: " + e.getMessage());
        }
    }
} 