package com.titan.event.controller;

import com.titan.event.response.UploadResponse;
import com.titan.event.service.IOssFileService;
import com.titan.event.service.IUserInfoService;
import com.titan.event.util.CommonUtil;
import com.titan.event.vo.Result;
import com.titan.event.vo.UploadVO;
import com.titan.event.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

@RequestMapping("/oss")
@RestController
@Slf4j
@Api(description = "app接口 - oss上传", tags = "OssFileController")
public class OssFileController {

    @Autowired
    private IOssFileService ossFileService;

    @Autowired
    private CommonUtil commonUtil;

    @Autowired
    private IUserInfoService userInfoService;

    /**
     * oss文件上传
     * @param multipartFile
     * @return
     */
    @ApiOperation("上传文件至oss")
    @ResponseBody
    @PostMapping("/upload")
    public Result<UploadResponse> upload(@RequestParam("file") MultipartFile multipartFile) {
        // 获取用户id
//        String customerId = commonUtil.getOperatorId();
        // 查询用户已关注的省份列表
//        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
//        if(Objects.isNull(userInfoVO)){
//            return Result.error("error");
//        }
        Result<UploadResponse> result = new Result();
        try {
            UploadVO upload = ossFileService.upload(multipartFile);
            UploadResponse uploadResponse = UploadResponse.builder()
                    .uploadInfo(upload)
                    .build();
            return Result.OK("上传成功！",uploadResponse);
        } catch (Exception ex) {
            log.info(ex.getMessage(), ex);
            result.error500("上传失败");
        }
        return result;
    }

    /**
     * 获取sts凭证
     * @return
     */
    @ApiOperation("获取sts凭证")
    @ResponseBody
    @PostMapping("/sts")
    public Result sts() {
        return Result.OK(ossFileService.sts());
    }

}
