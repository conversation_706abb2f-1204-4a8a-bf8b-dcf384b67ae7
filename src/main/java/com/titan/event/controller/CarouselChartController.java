package com.titan.event.controller;

import com.titan.event.request.userinfo.UserUpdateRequest;
import com.titan.event.request.userinfo.WxLoginRequest;
import com.titan.event.request.userinfo.WxPhoneRequest;
import com.titan.event.response.LoginResponse;
import com.titan.event.response.carouselchart.CarouselChartResponse;
import com.titan.event.service.ICarouselChartService;
import com.titan.event.config.CacheConfig;
import com.titan.event.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import com.titan.event.vo.carouselchart.CarouselChartVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:13
 */
@RequestMapping("/carousel")
@RestController
@Slf4j
@Api(description = "app接口 - 轮播图", tags = "CarouselChartController")
public class CarouselChartController {

    @Autowired
    private ICarouselChartService carouselChartService;

    /**
     * 轮播图列表
     * @return
     */
    @ApiOperation("轮播图列表")
    @GetMapping("/list")
    @Cacheable(value = CacheConfig.CAROUSEL_LIST_CACHE, key = "'all'")
    public Result<CarouselChartResponse> getList() {
        List<CarouselChartVO> list = carouselChartService.getList();
        return Result.ok(CarouselChartResponse.builder()
                .carouselCharts(list)
                .build());
    }

}
