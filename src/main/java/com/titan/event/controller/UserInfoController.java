package com.titan.event.controller;

import com.titan.event.request.userinfo.*;
import com.titan.event.response.LoginResponse;
import com.titan.event.service.IUserCardService;
import com.titan.event.service.IUserInfoService;
import com.titan.event.util.*;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:13
 */
@RequestMapping("/user")
@RestController
@Slf4j
@Api(description = "app接口 - 用户登录", tags = "UserInfoController")
public class UserInfoController {

    @Autowired
    private IUserInfoService userInfoService;

    @Resource
    private IUserCardService userCardService;

    @Resource
    private CommonUtil commonUtil;

    /**
     * 游客登录
     * @param touristsLoginRequest
     * @return
     */
    @ApiOperation("游客登录")
    @PostMapping("/tourists/login")
    public Result<LoginResponse> touristsLogin(@RequestBody TouristsLoginRequest touristsLoginRequest) {
        return userInfoService.touristsLogin(touristsLoginRequest);
    }

    /**
     * 微信登录
     * @param wxLoginRequest
     * @return
     */
    @ApiOperation("微信登录")
    @PostMapping("/wx/login")
    public Result<LoginResponse> wxLogin(@RequestBody WxLoginRequest wxLoginRequest) {
        return userInfoService.wxLogin(wxLoginRequest);
    }

    @ApiOperation("手机号绑定")
    @PostMapping("/wx/phone")
    public Result<LoginResponse> wxPhone(@RequestBody WxPhoneRequest wxPhoneRequest) {
        return userInfoService.wxPhone(wxPhoneRequest);
    }

    @ApiOperation("获取用户信息")
    @GetMapping("/getUserInfo")
    public Result<LoginResponse> getUserInfo() {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }

        // 获取用户卡数量统计
        Long userId = Long.parseLong(customerId);
        Map<String, Integer> cardStatistics = userCardService.getUserCardCountStatistics(userId);

        // 设置卡数量统计到用户信息中
        userInfoVO.setBenefitCardCount(cardStatistics.get("benefitCardCount"));
        userInfoVO.setStorageCardCount(cardStatistics.get("storageCardCount"));
        userInfoVO.setCountCardCount(cardStatistics.get("countCardCount"));
        userInfoVO.setMemberCardCount(cardStatistics.get("memberCardCount"));
        userInfoVO.setAvailableBenefitCardCount(cardStatistics.get("availableBenefitCardCount"));
        userInfoVO.setAvailableStorageCardCount(cardStatistics.get("availableStorageCardCount"));
        userInfoVO.setAvailableCountCardCount(cardStatistics.get("availableCountCardCount"));
        userInfoVO.setAvailableMemberCardCount(cardStatistics.get("availableMemberCardCount"));

        LoginResponse loginResponse = LoginResponse.builder()
                .userInfo(userInfoVO)
                .build();
        return Result.OK(loginResponse);
    }

    /**
     * 修改用户信息
     * @param userUpdateRequest
     * @return
     */
    @ApiOperation(value = "修改用户信息")
    @RequestMapping(value = "/update", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<Void> update(@RequestBody UserUpdateRequest userUpdateRequest) {
        String customerId = commonUtil.getOperatorId();
        UserInfoVO userInfoVO = userInfoService.getByUserId(Long.parseLong(customerId));
        if(Objects.isNull(userInfoVO)){
            return Result.error("error");
        }
        userInfoService.updateUserInfo(userInfoVO.getUserId(), userUpdateRequest);
        return Result.OK();
    }


}
