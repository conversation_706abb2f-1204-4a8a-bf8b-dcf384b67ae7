package com.titan.event.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.titan.event.entity.WxShop;
import com.titan.event.entity.WxShopOrderNotify;
import com.titan.event.entity.WxShopProduct;
import com.titan.event.entity.WxShopOrderDetail;
import com.titan.event.mapper.WxShopMapper;
import com.titan.event.mapper.WxShopOrderNotifyMapper;
import com.titan.event.mapper.WxShopProductMapper;
import com.titan.event.mapper.WxShopOrderDetailMapper;
import com.titan.event.service.IUserInfoService;
import com.titan.event.util.CommonUtil;
import com.titan.event.vo.Result;
import com.titan.event.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.util.crypt.WxMaCryptUtils;
import org.apache.commons.codec.binary.Base64;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RequestMapping("/wxProduct")
@RestController
@Slf4j
@Api(description = "app接口 - 微信店铺", tags = "WeChatController")
public class WeChatController {

    private static final String TOKEN = "mauv3erp6T9V1F0fPz7b8LRZhWkJKnUc";

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private WxShopMapper wxShopMapper;

    @Autowired
    private WxShopOrderNotifyMapper wxShopOrderNotifyMapper;

    @Autowired
    private WxShopProductMapper wxShopProductMapper;

    @Autowired
    private WxShopOrderDetailMapper wxShopOrderDetailMapper;

    /**
     * 将状态码转换为对应的状态描述
     */
    private String convertBindStatus(int status) {
        String statusDesc;
        switch (status) {
            case 1:
                statusDesc = "已绑定";
                break;
            case 2:
                statusDesc = "解绑";
                break;
            case 3:
                statusDesc = "拒绝绑定";
                break;
            case 4:
                statusDesc = "已邀请";
                break;
            case 5:
                statusDesc = "绑定超时";
                break;
            case 6:
                statusDesc = "绑定失败";
                break;
            case 7:
                statusDesc = "店铺取消绑定";
                break;
            default:
                statusDesc = "未知状态";
                break;
        }
        return statusDesc;
    }

    @ApiOperation(value = "合作店铺商品列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Result list() {
        wxMaService.switchover("wx4be4a1eb1149410f");
        try {
            String token = wxMaService.getAccessToken();
            String url = "https://api.weixin.qq.com/channels/ec/cooperation/product/list?access_token="+token;
            JSONObject parseObj = new JSONObject();
            parseObj.put("shop_appid","wxd3a94bc05a254d32");
            parseObj.put("page_size",100);
            HttpResponse response = sendJsonPostRequest(url, parseObj.toString());
            String result = response.body();
            response.close();
            return Result.OK(JSONObject.parseObject(result));
        } catch (WxErrorException e) {
            return Result.error("error");
        }
    }

    @ApiOperation(value = "刷新合作小店列表")
    @GetMapping("/refreshShops")
    @Transactional(rollbackFor = Exception.class)
    public Result refreshShops() {
        wxMaService.switchover("wx4be4a1eb1149410f");
        try {
            String token = wxMaService.getAccessToken();
            String url = "https://api.weixin.qq.com/channels/ec/cooperation/shop/list?access_token=" + token;
            JSONObject parseObj = new JSONObject();
            parseObj.put("page_size", 100);
            HttpResponse response = sendJsonPostRequest(url, parseObj.toString());
            String result = response.body();
            JSONObject jsonResult = JSONObject.parseObject(result);
            
            if (jsonResult.getInteger("errcode") == 0) {
                List<JSONObject> shops = jsonResult.getJSONArray("shop_list").toJavaList(JSONObject.class);
                int updateCount = 0;
                int insertCount = 0;
                for (JSONObject shop : shops) {
                    String shopId = shop.getString("appid");
                    WxShop existingShop = wxShopMapper.findByShopId(shopId);
                    
                    if (existingShop != null) {
                        // 更新现有记录
                        existingShop.setShopName(shop.getString("nickname"));
                        existingShop.setBindStatus(convertBindStatus(shop.getIntValue("status")));
                        existingShop.setBindTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(shop.getLongValue("bind_time")), ZoneId.systemDefault()));
                        wxShopMapper.save(existingShop);
                        updateCount++;
                    } else {
                        // 插入新记录
                        WxShop wxShop = new WxShop();
                        wxShop.setShopId(shopId);
                        wxShop.setShopName(shop.getString("nickname"));
                        wxShop.setBindStatus(convertBindStatus(shop.getIntValue("status")));
                        wxShop.setBindTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(shop.getLongValue("bind_time")), ZoneId.systemDefault()));
                        wxShopMapper.save(wxShop);
                        insertCount++;
                    }
                    
                    // 获取并保存店铺商品列表
                    refreshShopProducts(token, shopId);
                }
                return Result.OK(String.format("刷新成功，更新%d条记录，新增%d条记录", updateCount, insertCount));
            }
            return Result.error("刷新失败：" + jsonResult.getString("errmsg"));
        } catch (WxErrorException e) {
            log.error("刷新合作小店列表失败", e);
            return Result.error("刷新失败：" + e.getMessage());
        }
    }

    /**
     * 刷新店铺商品列表
     */
    private void refreshShopProducts(String token, String shopId) {
        try {
            // 删除该店铺的所有商品
            wxShopProductMapper.deleteByShopId(shopId);
            
            String nextKey = null;
            do {
                // 获取商品列表
                JSONObject productList = getShopProducts(token, shopId, nextKey);
                if (productList != null && productList.getInteger("errcode") == 0) {
                    // 获取商品详情并保存
                    JSONArray products = productList.getJSONArray("products");
                    for (int i = 0; i < products.size(); i++) {
                        JSONObject product = products.getJSONObject(i);
                        Long productId = product.getLong("product_id");
                        
                        // 获取商品详情
                        JSONObject productDetail = getProductDetail(token, shopId, productId);
                        if (productDetail != null && productDetail.getInteger("errcode") == 0) {
                            JSONObject detail = productDetail.getJSONObject("product");
                            
                            // 保存商品信息
                            WxShopProduct wxShopProduct = new WxShopProduct();
                            wxShopProduct.setShopId(shopId);
                            wxShopProduct.setProductId(productId);
                            wxShopProduct.setTitle(detail.getString("title"));
                            wxShopProduct.setImgUrls(String.join(",", detail.getJSONArray("img_urls").toJavaList(String.class)));
                            wxShopProduct.setMinPrice(detail.getInteger("min_price"));
                            wxShopProductMapper.save(wxShopProduct);
                        }
                    }
                    nextKey = productList.getString("next_key");
                } else {
                    break;
                }
            } while (nextKey != null && !nextKey.isEmpty());
            
        } catch (Exception e) {
            log.error("刷新店铺商品列表失败，shopId: {}", shopId, e);
            throw new RuntimeException("刷新店铺商品列表失败", e);
        }
    }

    /**
     * 获取店铺商品列表
     */
    private JSONObject getShopProducts(String token, String shopId, String nextKey) {
        try {
            String url = "https://api.weixin.qq.com/channels/ec/cooperation/product/list?access_token=" + token;
            JSONObject parseObj = new JSONObject();
            parseObj.put("shop_appid", shopId);
            parseObj.put("page_size", 100);
            if (nextKey != null) {
                parseObj.put("next_key", nextKey);
            }
            
            HttpResponse response = sendJsonPostRequest(url, parseObj.toString());
            return JSONObject.parseObject(response.body());
        } catch (Exception e) {
            log.error("获取店铺商品列表失败，shopId: {}", shopId, e);
            return null;
        }
    }

    /**
     * 获取商品详情
     */
    private JSONObject getProductDetail(String token, String shopId, Long productId) {
        try {
            String url = "https://api.weixin.qq.com/channels/ec/cooperation/product/get?access_token=" + token;
            JSONObject parseObj = new JSONObject();
            parseObj.put("shop_appid", shopId);
            parseObj.put("product_id", productId);
            
            HttpResponse response = sendJsonPostRequest(url, parseObj.toString());
            return JSONObject.parseObject(response.body());
        } catch (Exception e) {
            log.error("获取商品详情失败，shopId: {}, productId: {}", shopId, productId, e);
            return null;
        }
    }

    private HttpResponse sendJsonPostRequest(String url, String requestData) {
        // 发送POST请求并获取响应
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .body(requestData)
                .timeout(5000)
                .execute();

        if (response.getStatus() != 200) {
            // 如果不是200 OK状态，可以在这里处理异常情况
            throw new RuntimeException("请求失败，HTTP状态码：" + response.getStatus());
        }

        return response;
    }

    @ApiOperation(value = "获取订单详情并保存")
    private void saveOrderDetail(String shopAppid, String orderId) {
        try {
            String token = wxMaService.getAccessToken();
            String url = "https://api.weixin.qq.com/channels/ec/cooperation/order/get?access_token=" + token;
            
            JSONObject parseObj = new JSONObject();
            parseObj.put("shop_appid", shopAppid);
            parseObj.put("order_id", orderId);
            
            HttpResponse response = sendJsonPostRequest(url, parseObj.toString());
            JSONObject result = JSONObject.parseObject(response.body());
            
            if (result.getInteger("errcode") == 0) {
                JSONObject order = result.getJSONObject("order");
                JSONObject orderDetail = order.getJSONObject("order_detail");
                JSONArray productInfos = orderDetail.getJSONArray("product_infos");
                JSONObject priceInfo = orderDetail.getJSONObject("price_info");
                
                // 遍历商品信息并保存
                for (int i = 0; i < productInfos.size(); i++) {
                    JSONObject product = productInfos.getJSONObject(i);
                    WxShopOrderDetail detail = new WxShopOrderDetail();
                    
                    // 设置基本信息
                    detail.setOrderId(orderId);
                    detail.setShopAppid(shopAppid);
                    detail.setOpenid(order.getString("openid"));
                    detail.setUnionid(order.getString("unionid"));
                    
                    // 设置商品信息
                    detail.setProductId(product.getLong("product_id"));
                    detail.setSkuId(product.getLong("sku_id"));
                    detail.setThumbImg(product.getString("thumb_img"));
                    detail.setSkuCnt(product.getInteger("sku_cnt"));
                    detail.setSalePrice(product.getInteger("sale_price"));
                    detail.setTitle(product.getString("title"));
                    detail.setSkuCode(product.getString("sku_code"));
                    detail.setMarketPrice(product.getInteger("market_price"));
                    detail.setSkuAttrs(product.getJSONArray("sku_attrs").toJSONString());
                    detail.setRealPrice(product.getInteger("real_price"));
                    detail.setIsDiscounted(product.getBoolean("is_discounted"));
                    detail.setEstimatePrice(product.getInteger("estimate_price"));
                    detail.setIsChangePrice(product.getBoolean("is_change_price"));
                    detail.setChangePrice(product.getInteger("change_price"));
                    
                    // 设置价格信息
                    detail.setProductPrice(priceInfo.getInteger("product_price"));
                    detail.setOrderPrice(priceInfo.getInteger("order_price"));
                    detail.setFreight(priceInfo.getInteger("freight"));
                    detail.setDiscountedPrice(priceInfo.getInteger("discounted_price"));
                    detail.setOriginalOrderPrice(priceInfo.getInteger("original_order_price"));
                    detail.setEstimateProductPrice(priceInfo.getInteger("estimate_product_price"));
                    detail.setChangeDownPrice(priceInfo.getInteger("change_down_price"));
                    detail.setChangeFreight(priceInfo.getInteger("change_freight"));
                    detail.setIsChangeFreight(priceInfo.getBoolean("is_change_freight"));
                    detail.setUseDeduction(priceInfo.getBoolean("use_deduction"));
                    detail.setDeductionPrice(priceInfo.getInteger("deduction_price"));
                    detail.setMerchantReceievePrice(priceInfo.getInteger("merchant_receieve_price"));
                    detail.setMerchantDiscountedPrice(priceInfo.getInteger("merchant_discounted_price"));
                    detail.setFinderDiscountedPrice(priceInfo.getInteger("finder_discounted_price"));
                    
                    // 设置时间
                    detail.setCreateTime(new Date());
                    detail.setUpdateTime(new Date());
                    
                    wxShopOrderDetailMapper.saveAndFlush(detail);
                }
            }
        } catch (Exception e) {
            log.error("获取订单详情失败，shopAppid: {}, orderId: {}", shopAppid, orderId, e);
        }
    }

    @ApiOperation(value = "刷新历史订单详情")
    @GetMapping("/refreshOrderDetails")
    public Result refreshOrderDetails() {
        try {
            List<WxShopOrderNotify> orderNotifies = wxShopOrderNotifyMapper.findAll();
            int successCount = 0;
            int failCount = 0;
            
            for (WxShopOrderNotify notify : orderNotifies) {
                try {
                    saveOrderDetail(notify.getShopAppid(), notify.getOrderId());
                    successCount++;
                } catch (Exception e) {
                    log.error("刷新订单详情失败，orderId: {}", notify.getOrderId(), e);
                    failCount++;
                }
            }
            
            return Result.OK(String.format("刷新完成，成功：%d，失败：%d", successCount, failCount));
        } catch (Exception e) {
            log.error("刷新历史订单详情失败", e);
            return Result.error("刷新失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "微信订单通知回调接口")
    @RequestMapping(value = "/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public Object notify(
            @RequestParam(value = "signature", required = false) String signature,
            @RequestParam(value = "timestamp", required = false) String timestamp,
            @RequestParam(value = "nonce", required = false) String nonce,
            @RequestParam(value = "echostr", required = false) String echostr,
            @RequestBody(required = false) String requestBody) {
        
        log.info("收到微信回调请求，signature=[{}], timestamp=[{}], nonce=[{}], echostr=[{}], requestBody=[{}]",
                signature, timestamp, nonce, echostr, requestBody);

        // 如果是配置验证请求
        if (echostr != null) {
            if (checkSignature(signature, timestamp, nonce)) {
                return echostr;
            }
            return "非法请求";
        }

        // 处理订单通知
        if (requestBody != null) {
            try {
                // 验证消息签名
                if (!checkSignature(signature, timestamp, nonce)) {
                    return "非法请求";
                }

                // 解析订单通知数据
                JSONObject notifyData = JSONObject.parseObject(requestBody);
                
                // 如果是订单结算通知，直接返回成功
                if ("event".equals(notifyData.getString("MsgType")) 
                    && "trade_manage_order_settlement".equals(notifyData.getString("Event"))) {
                    log.info("收到订单结算通知，忽略处理：{}", requestBody);
                    return "success";
                }

                JSONObject data = notifyData.getJSONObject("Data");
                
                // 保存通知记录
                WxShopOrderNotify orderNotify = new WxShopOrderNotify();
                orderNotify.setToUserName(notifyData.getString("ToUserName"));
                orderNotify.setFromUserName(notifyData.getString("FromUserName"));
                orderNotify.setCreateTime(new Date(notifyData.getLong("CreateTime") * 1000));
                orderNotify.setOrderId(data.getString("order_id"));
                orderNotify.setShopAppid(data.getString("shop_appid"));
                orderNotify.setSystemCreateTime(new Date());
                orderNotify.setSystemUpdateTime(new Date());

                wxShopOrderNotifyMapper.save(orderNotify);
                
                // 获取并保存订单详情
                saveOrderDetail(data.getString("shop_appid"), data.getString("order_id"));
                
                log.info("订单通知保存成功：{}", orderNotify);
                return "success";
            } catch (Exception e) {
                log.error("处理订单通知异常", e);
                return "fail";
            }
        }

        return "success";
    }

    private boolean checkSignature(String signature, String timestamp, String nonce) {
        try {
            String[] arr = new String[]{TOKEN, timestamp, nonce};
            Arrays.sort(arr);
            StringBuilder content = new StringBuilder();
            for (String str : arr) {
                content.append(str);
            }
            
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(content.toString().getBytes());
            StringBuilder tmpStr = new StringBuilder();
            for (byte b : digest) {
                tmpStr.append(String.format("%02x", b));
            }
            
            return tmpStr.toString().equals(signature);
        } catch (NoSuchAlgorithmException e) {
            log.error("验证签名异常", e);
            return false;
        }
    }

}
