package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 场馆订单详情请求类
 */
@Data
@ApiModel(value = "场馆订单详情请求", description = "查询场馆订单详情的请求参数")
public class VenueOrderDetailRequest {

    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID", example = "202405110001", notes = "订单ID、退款单ID和核销码至少有一个不能为空")
    private String orderId;
    
    /**
     * 核销码
     */
    @ApiModelProperty(value = "核销码", example = "123456789012", notes = "订单ID、退款单ID和核销码至少有一个不能为空")
    private String verificationCode;
    
    /**
     * 退款单ID
     */
    @ApiModelProperty(value = "退款单ID", example = "R1718248636123", notes = "订单ID、退款单ID和核销码至少有一个不能为空")
    private String refundId;
    
    /**
     * 校验订单ID、退款单ID和核销码至少有一个不为空
     */
    public boolean isValid() {
        return (orderId != null && !orderId.isEmpty()) || 
               (verificationCode != null && !verificationCode.isEmpty()) ||
               (refundId != null && !refundId.isEmpty());
    }
} 