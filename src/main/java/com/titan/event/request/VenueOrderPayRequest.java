package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 场馆订单支付请求
 */
@Data
@ApiModel(description = "场馆订单支付请求")
public class VenueOrderPayRequest {

    @NotBlank(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID", required = true, example = "O2023102312345678901234")
    private String orderId;

    @NotNull(message = "支付方式不能为空")
    @ApiModelProperty(value = "支付方式：1-微信支付，2-储值卡支付", required = true, example = "1")
    private Integer payType;

    @ApiModelProperty(value = "储值卡ID", required = false, example = "1", notes = "储值卡支付时必填")
    private Long userCardId;
}
