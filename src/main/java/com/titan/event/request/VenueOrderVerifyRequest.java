package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 场馆订单核销请求
 */
@Data
@ApiModel(description = "场馆订单核销请求")
public class VenueOrderVerifyRequest {

    @NotBlank(message = "核销码不能为空")
    @ApiModelProperty(value = "订单核销码", required = true, example = "123456789012")
    private String verificationCode;
    
    @ApiModelProperty(value = "API核销密钥", required = false, example = "a1b2c3d4e5f6g7h8i9j0")
    private String key;
} 