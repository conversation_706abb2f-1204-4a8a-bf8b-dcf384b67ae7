package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 场馆订单退款请求
 */
@Data
@ApiModel(description = "场馆订单退款请求")
public class VenueOrderRefundRequest {

    @NotBlank(message = "订单ID不能为空")
    @ApiModelProperty(value = "订单ID", required = true, example = "1234567890")
    private String orderId;

    @ApiModelProperty(value = "退款原因", example = "个人原因取消")
    private String refundReason;

    private String key;

}