package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 场馆退单列表查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "场馆退单列表查询请求", description = "用于按退款状态查询退单列表的请求参数")
public class VenueRefundOrderListRequest extends BaseSearchRequest {

    @ApiModelProperty(value = "退款状态: 0-全部, 1-已完成, 2-待退款, 3-待审核, 4-已作废", example = "0", required = true)
    @NotNull(message = "退款状态不能为空")
    private Integer refundStatus;
} 