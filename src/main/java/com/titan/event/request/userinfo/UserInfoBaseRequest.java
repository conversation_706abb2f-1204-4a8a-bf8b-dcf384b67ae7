package com.titan.event.request.userinfo;

import com.titan.event.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "用户信息请求参数")
public class UserInfoBaseRequest extends BaseSearchRequest {

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "uuid")
    private String uuid;

}
