package com.titan.event.request.userinfo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.titan.event.util.LocalDateDeserializer;
import com.titan.event.util.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR> lhx
 * @date 2023/10/30 16:17
 */
@Getter
@Setter
@ApiModel(description = "用户修改请求参数")
public class UserUpdateRequest {

    @ApiModelProperty(value = "头像地址")
    private String avatar;

    @ApiModelProperty(value = "昵称")
    private String nickName;

}
