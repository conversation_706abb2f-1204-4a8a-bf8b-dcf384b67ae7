package com.titan.event.request.carouselchart;

import com.titan.event.enums.DefaultFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Getter
@Setter
@ApiModel(description = "轮播图列表请求参数")
public class CarouselChartBaseRequest {

    @ApiModelProperty(value = "显示标记 0显示 1隐藏")
    private Integer showStatus;

}
