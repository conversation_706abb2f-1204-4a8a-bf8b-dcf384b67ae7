package com.titan.event.request.usercard;

import com.titan.event.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户卡列表查询请求参数
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "用户卡列表查询请求", description = "用户卡列表分页查询请求参数")
public class UserCardListRequest extends BaseSearchRequest {
    
    /**
     * 卡类型（0-权益卡 1-储值卡 2-计次卡 3-会员卡）
     * 权益卡为计次卡并且是无限次使用
     * 储值卡就是卡类型是储值卡的
     * 计次卡是非无限次的计次卡
     * 会员卡写死0
     */
    @ApiModelProperty(value = "卡类型：0-权益卡 1-储值卡 2-计次卡 3-会员卡", example = "1")
    private Integer cardType;

    /**
     * 可用状态（0-不可用 1-可用）
     */
    @ApiModelProperty(value = "可用状态：0-不可用 1-可用，不传则查询全部", example = "1")
    private Integer available;
}
