package com.titan.event.request.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 用户卡详情查询请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "用户卡详情查询请求", description = "用户卡详情查询请求参数")
public class UserCardDetailRequest {
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1", required = true)
    @NotNull(message = "用户会员卡ID不能为空")
    private Long userCardId;
}
