package com.titan.event.request.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 卡片购买请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "卡片购买请求", description = "卡片购买请求参数")
public class CardPurchaseRequest {
    
    /**
     * 会员卡ID
     */
    @ApiModelProperty(value = "会员卡ID", example = "1", required = true)
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;
}
