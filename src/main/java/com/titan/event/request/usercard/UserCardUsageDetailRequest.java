package com.titan.event.request.usercard;

import com.titan.event.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 用户卡使用明细查询请求参数
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "用户卡使用明细查询请求", description = "用户卡使用明细分页查询请求参数")
public class UserCardUsageDetailRequest extends BaseSearchRequest {
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1", required = true)
    @NotNull(message = "用户会员卡ID不能为空")
    private Long userCardId;
}
