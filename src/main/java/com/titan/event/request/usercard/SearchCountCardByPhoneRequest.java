package com.titan.event.request.usercard;

import com.titan.event.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 按手机号查询有效计次卡请求参数
 * 只查询有效的计次卡（剩余次数>0且未过期的卡片）
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "按手机号查询有效计次卡请求", description = "按手机号模糊查询有效计次卡列表的请求参数，只返回剩余次数大于0且未过期的卡片")
public class SearchCountCardByPhoneRequest extends BaseSearchRequest {
    
    /**
     * 手机号（支持模糊查询）
     */
    @ApiModelProperty(value = "手机号（支持模糊查询）", example = "138", required = true)
    @NotBlank(message = "手机号不能为空")
    private String phone;
    
    /**
     * 场馆ID（可选，用于筛选特定场馆的卡片）
     */
    @ApiModelProperty(value = "场馆ID（可选）", example = "1")
    private Long venueId;
}
