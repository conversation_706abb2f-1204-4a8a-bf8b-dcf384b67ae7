package com.titan.event.request.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改副卡信息请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "修改副卡信息请求", description = "修改副卡信息请求参数，支持全量传递")
public class UpdateSubCardsRequest {
    
    /**
     * 用户会员卡ID
     */
    @ApiModelProperty(value = "用户会员卡ID", example = "1", required = true)
    @NotNull(message = "用户会员卡ID不能为空")
    private Long userCardId;
    
    /**
     * 副卡信息列表（固定传递2个副卡的手机号，没有数据传null）
     * 校验规则：
     * 1. 列表长度必须为2
     * 2. 最多只能有2个有效手机号
     * 3. 手机号不能重复
     * 4. 副卡手机号不能与主卡手机号相同
     */
    @ApiModelProperty(value = "副卡信息列表（固定2个副卡，最多2个有效手机号）", required = true)
    @Valid
    private List<SubCardInfo> subCards;
    
    /**
     * 副卡信息
     */
    @Data
    @ApiModel(value = "副卡信息", description = "副卡的详细信息")
    public static class SubCardInfo {

        /**
         * 副卡用户手机号
         */
        @ApiModelProperty(value = "副卡用户手机号", example = "13812345678")
        private String userPhone;
    }
}
