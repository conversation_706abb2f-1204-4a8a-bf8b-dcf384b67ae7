package com.titan.event.request.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 储值卡充值请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "储值卡充值请求", description = "储值卡充值请求参数")
public class StorageCardRechargeRequest {
    
    /**
     * 用户会员卡ID（如果传了卡ID就为这个卡片充值）
     */
    @ApiModelProperty(value = "用户会员卡ID（如果传了卡ID就为这个卡片充值）", example = "1")
    private Long userCardId;
    
    /**
     * 场馆ID（如果没有传卡ID，需要场馆ID查询这个场馆的储值卡为用户开卡）
     */
    @ApiModelProperty(value = "场馆ID（如果没有传卡ID，需要场馆ID查询这个场馆的储值卡为用户开卡）", example = "1")
    private Long venueId;
    
    /**
     * 充值金额（单位：元）
     */
    @ApiModelProperty(value = "充值金额（单位：元）", example = "100.00", required = true)
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    private BigDecimal amount;
}
