package com.titan.event.request.usercard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 计次卡扣减请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "计次卡扣减请求", description = "计次卡次数扣减的请求参数")
public class DeductCountCardRequest {
    
    /**
     * 用户卡ID
     */
    @ApiModelProperty(value = "用户卡ID", example = "1", required = true)
    @NotNull(message = "用户卡ID不能为空")
    private Long userCardId;
    
    /**
     * 扣减次数
     */
    @ApiModelProperty(value = "扣减次数", example = "1", required = true)
    @NotNull(message = "扣减次数不能为空")
    @Min(value = 1, message = "扣减次数必须大于0")
    private Integer deductCount;
    
    /**
     * 扣减原因/备注
     */
    @ApiModelProperty(value = "扣减原因/备注", example = "场地使用")
    private String remark;
}
