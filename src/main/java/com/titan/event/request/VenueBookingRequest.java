package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 场地预订请求
 */
@Data
@ApiModel(description = "场地预订请求")
public class VenueBookingRequest {

    @ApiModelProperty(value = "预订场地列表", required = true)
    @NotEmpty(message = "预订场地不能为空")
    @Valid
    private List<VenueBookingItem> items;

    /**
     * 场地预订项
     */
    @Data
    @ApiModel(description = "场地预订项")
    public static class VenueBookingItem {
        
        @ApiModelProperty(value = "场地ID", required = true, example = "1")
        @NotNull(message = "场地ID不能为空")
        private Long siteId;
        
        @ApiModelProperty(value = "分区ID", required = true, example = "1")
        private Long sectionId;
        
        @ApiModelProperty(value = "预订日期", required = true, example = "2025-04-17")
        @NotNull(message = "预订日期不能为空")
        private String date;
        
        @ApiModelProperty(value = "开始时间", required = true, example = "18:00")
        @NotNull(message = "开始时间不能为空")
        private String start;
        
        @ApiModelProperty(value = "结束时间", required = true, example = "19:00")
        @NotNull(message = "结束时间不能为空")
        private String end;
    }
} 