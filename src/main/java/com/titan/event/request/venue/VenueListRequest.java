package com.titan.event.request.venue;

import com.titan.event.request.BaseSearchRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 场馆列表请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "VenueListRequest", description = "场馆列表查询请求")
public class VenueListRequest extends BaseSearchRequest {
    
    /**
     * 省份
     */
    @ApiModelProperty(value = "省份", example = "上海市", notes = "按省份筛选场馆")
    private String province;
} 