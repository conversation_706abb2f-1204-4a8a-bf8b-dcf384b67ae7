package com.titan.event.request.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场馆详情请求
 */
@Data
@ApiModel(value = "VenueDetailRequest", description = "场馆详情查询请求")
public class VenueDetailRequest {
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", example = "1", required = true, notes = "场馆的唯一标识")
    private Long venueId;
} 