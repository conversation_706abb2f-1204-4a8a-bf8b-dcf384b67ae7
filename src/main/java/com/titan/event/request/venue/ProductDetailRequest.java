package com.titan.event.request.venue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 商品详情请求DTO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "商品详情请求", description = "获取商品详情的请求参数")
public class ProductDetailRequest {

    @NotNull(message = "商品ID不能为空")
    @ApiModelProperty(value = "商品ID", required = true, example = "1")
    private Long productId;
} 