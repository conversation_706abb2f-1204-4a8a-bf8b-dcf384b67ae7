package com.titan.event.request.venuesite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场地查询请求参数
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "场地查询请求", description = "根据场地类型和日期查询场地信息")
public class SiteQueryRequest {
    
    /**
     * 场馆ID
     */
    @ApiModelProperty(value = "场馆ID", required = false, example = "1")
    private Long venueId;
    
    /**
     * 场地类型(1:羽毛球, 2:篮球, 3:乒乓球, 4:网球, 5:足球, 6:其他)
     */
    @ApiModelProperty(value = "场地类型", required = true, example = "1")
    private Integer siteType;
    
    /**
     * 查询日期（格式：yyyy-MM-dd）
     */
    @ApiModelProperty(value = "查询日期", required = true, example = "2023-05-01")
    private String date;
} 