package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 场馆订单请求基类
 */
@Data
@ApiModel(description = "场馆订单请求")
public class VenueOrderRequest {

    @ApiModelProperty(value = "订单类型", required = true, example = "1", notes = "1:场地预订, 2:商品购买, 3:卡片购买")
    @NotBlank(message = "订单类型不能为空")
    private String itemType;
} 