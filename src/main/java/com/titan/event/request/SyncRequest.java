package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> lhx
 * @date 2023/7/28 14:48
 */
@Data
@Builder
@ApiModel(description = "同步请求对象")
public class SyncRequest {

    @ApiModelProperty(value = "开始时间", example = "2023-07-27")
    private String start;

    @ApiModelProperty(value = "结束时间", example = "2023-07-28")
    private String end;
}
