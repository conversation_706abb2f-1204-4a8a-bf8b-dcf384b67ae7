package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 场馆订单列表查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "场馆订单列表查询请求", description = "用于按状态查询订单列表的请求参数")
public class VenueOrderListRequest extends BaseSearchRequest {

    @ApiModelProperty(value = "订单状态: 0-全部, 1-待支付, 2-待核销, 3-已完成, 4-已作废", example = "0", required = true)
    @NotNull(message = "订单状态不能为空")
    private Integer status;
} 