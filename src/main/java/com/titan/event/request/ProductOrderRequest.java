package com.titan.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品购买请求
 */
@Data
@ApiModel(description = "商品购买请求")
public class ProductOrderRequest {

    @ApiModelProperty(value = "商品列表", required = true)
    @NotEmpty(message = "商品列表不能为空")
    @Valid
    private List<ProductOrderItem> items;

    /**
     * 商品购买项
     */
    @Data
    @ApiModel(description = "商品购买项")
    public static class ProductOrderItem {
        
        @ApiModelProperty(value = "商品ID", required = true, example = "1")
        @NotNull(message = "商品ID不能为空")
        private Long productId;
        
        @ApiModelProperty(value = "购买数量", required = true, example = "1")
        @NotNull(message = "购买数量不能为空")
        @Min(value = 1, message = "购买数量必须大于0")
        private Integer num;
    }
} 