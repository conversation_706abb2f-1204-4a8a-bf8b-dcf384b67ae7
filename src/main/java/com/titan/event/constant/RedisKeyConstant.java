package com.titan.event.constant;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:30
 */
public final class RedisKeyConstant {

    private RedisKeyConstant(){

    }

    /**
     * 赛事
     */
    public static final String EVENT_STOCK_KEY = "event:stock:";

    /**
     * 赛事项目
     */
    public static final String EVENT_PROJECT_STOCK_KEY = "event:project:stock:";

    /**
     * 赛事组别
     */
    public static final String EVENT_GROUP_STOCK_KEY = "event:group:stock:";

    /**
     * 赛事退款
     */
    public static final String EVENT_REFUND_KEY = "event:refund:";

    public static final String STOCK_LOCK_PREFIX = "event:stock:lock:";
    
    /**
     * 场地预订锁定前缀
     */
    public static final String VENUE_BOOKING_LOCK_PREFIX = "venue:booking:lock:";
    
    /**
     * 支付通知锁前缀
     */
    public static final String PAY_NOTIFY_LOCK_PREFIX = "pay:notify:lock:";
    
    /**
     * 退款通知锁前缀
     */
    public static final String REFUND_NOTIFY_LOCK_PREFIX = "refund:notify:lock:";
}
