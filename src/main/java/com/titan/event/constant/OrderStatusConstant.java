package com.titan.event.constant;

/**
 * 订单状态常量
 */
public class OrderStatusConstant {

    private OrderStatusConstant() {
        // 防止实例化
    }

    /**
     * 订单类型
     */
    public static final class OrderType {
        /**
         * 场地预订
         */
        public static final int BOOKING = 1;

        /**
         * 商品购买
         */
        public static final int PRODUCT = 2;

        /**
         * 卡片购买
         */
        public static final int CARD = 3;
    }

    /**
     * 订单状态
     */
    public static final class OrderStatus {
        /**
         * 待支付
         */
        public static final int WAIT_PAY = 0;

        /**
         * 已支付
         */
        public static final int PAID = 1;

        /**
         * 已取消
         */
        public static final int CANCELED = 2;

        /**
         * 已退款
         */
        public static final int REFUNDED = 3;

        /**
         * 已完成
         */
        public static final int COMPLETED = 4;
    }

    /**
     * 支付状态
     */
    public static final class PayStatus {
        /**
         * 未支付
         */
        public static final String NOTPAY = "NOTPAY";

        /**
         * 已支付
         */
        public static final String SUCCESS = "SUCCESS";

        /**
         * 已退款
         */
        public static final String REFUND = "REFUND";

        /**
         * 已关闭
         */
        public static final String CLOSED = "CLOSED";
    }

    /**
     * 支付方式
     */
    public static final class PaymentMethod {
        /**
         * 微信支付
         */
        public static final int WECHAT = 1;

        /**
         * 支付宝
         */
        public static final int ALIPAY = 2;

        /**
         * 现场支付
         */
        public static final int OFFLINE = 3;
    }
} 