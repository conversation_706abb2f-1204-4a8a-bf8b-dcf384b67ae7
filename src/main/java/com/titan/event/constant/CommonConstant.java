package com.titan.event.constant;

/**
 * @Description: 通用常量
 * @author: jeecg-boot
 */
public interface CommonConstant {

 /** {@code 500 Server Error} (HTTP/1.0 - RFC 1945) */
 Integer SC_INTERNAL_SERVER_ERROR_500 = 500;
 /** {@code 200 OK} (HTTP/1.0 - RFC 1945) */
 Integer SC_OK_200 = 200;

 /**访问权限认证未通过 510*/
 Integer SC_JEECG_NO_AUTHZ=510;


 /**未知的*/
 String UNKNOWN = "unknown";

 /**字符串http*/
 String STR_HTTP = "http";

 /**String 类型的空值*/
 String STRING_NULL = "null";



 /**
  * 八字=======
  */
 String BAZI_JIUXING = "jiuxing";

 String BAZI_HEHUN = "hehun";

 String BAZI_HEPAN = "hepan";

 String BAZI_PAIPAN = "paipan";

 String BAZI_CESUAN = "cesuan";

 String BAZI_JINGPAN = "jingpan";

 String BAZI_JINGSUAN = "jingsuan";

 String BAZI_ZWPAN = "zwpan";

 String BAZI_ZWLPAN = "zwlpan";

 String BAZI_WEILAI = "weilai";

 String BAZI_LIUDAOLUNHUI = "liudaolunhui";

 String YUCE_ZHENGYUAN = "zhengyuan";

 String YUCE_JIEHUN = "jiehun";

 /**
  * 灵签
  */
 String DIVINATION_FOZU_TYPE = "FOZU";
 String DIVINATION_LVZU_TYPE = "LVZU";
 String DIVINATION_MAZU_TYPE = "MAZU";
 String DIVINATION_YUELAO_TYPE = "YUELAO";
 String DIVINATION_GUANYIN_TYPE = "GUANYIN";
 String DIVINATION_ZHUGE_TYPE = "ZHUGE";

 /**
  * 生肖星座
  */
 String ZODIAC_SIGN_TYPE = "xingzuo";
 String ZODIAC_TYPE = "shengxiao";
}
