package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "wx_shop")
public class WxShop implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "shop_id")
    private String shopId;
    
    @Column(name = "shop_name")
    private String shopName;
    
    @Column(name = "bind_status")
    private String bindStatus;
    
    @Column(name = "bind_time")
    private LocalDateTime bindTime;

    public WxShop() {

    }
}