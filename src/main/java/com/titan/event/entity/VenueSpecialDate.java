package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 特殊日期实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_special_date")
@DynamicInsert
@DynamicUpdate
public class VenueSpecialDate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "special_date_id")
    private Long specialDateId;

    /**
     * 特殊日期名称
     */
    @Column(name = "special_date_name")
    private String specialDateName;

    /**
     * 特殊日期
     */
    @Column(name = "special_date")
    private Date specialDate;

    /**
     * 结束日期
     */
    @Column(name = "end_date")
    private Date endDate;

    /**
     * 特殊日期类型(1:促销日 2:赛事日 3:闭馆日 4:其他)
     */
    @Column(name = "special_date_type")
    private Integer specialDateType;

    /**
     * 状态（0正常 1停用）
     */
    @Column(name = "status")
    private String status;

    /**
     * 场地ID
     */
    @Column(name = "site_id")
    private Long siteId;

    /**
     * 关联价格方案ID
     */
    @Column(name = "scheme_id")
    private Long schemeId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag")
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
} 