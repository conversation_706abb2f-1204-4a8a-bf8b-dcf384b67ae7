package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员卡表实体类
 */
@Data
@Entity
@Table(name = "venue_member_card")
@DynamicInsert
@DynamicUpdate
public class VenueMemberCard {

    /**
     * 会员卡ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "card_id")
    private Long cardId;

    /**
     * 卡名称
     */
    @Column(name = "card_name", nullable = false, length = 100)
    private String cardName;

    /**
     * 封面图片
     */
    @Column(name = "cover_image", length = 255)
    private String coverImage;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场馆名称
     */
    @Column(name = "venue_name", nullable = false, length = 100)
    private String venueName;

    /**
     * 卡类型（1储值卡 2计次卡）
     */
    @Column(name = "card_type", nullable = false)
    private Integer cardType;

    /**
     * 是否无限次（0否 1是）
     */
    @Column(name = "unlimited_usage")
    private Integer unlimitedUsage;

    /**
     * 有效期类型（1天 2月 3年 4固定时间范围）
     */
    @Column(name = "validity_type", nullable = false)
    private Integer validityType;

    /**
     * 有效期值
     */
    @Column(name = "validity_value")
    private Integer validityValue;

    /**
     * 有效期开始时间
     */
    @Column(name = "validity_start_time")
    private LocalDateTime validityStartTime;

    /**
     * 有效期结束时间
     */
    @Column(name = "validity_end_time")
    private LocalDateTime validityEndTime;

    /**
     * 状态（0正常 1停用）
     */
    @Column(name = "status", length = 1)
    private String status;

    /**
     * 卡介绍
     */
    @Column(name = "card_description", columnDefinition = "text")
    private String cardDescription;

    /**
     * 卡价格
     */
    @Column(name = "card_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal cardPrice;

    /**
     * 卡划线价格
     */
    @Column(name = "card_original_price", precision = 10, scale = 2)
    private BigDecimal cardOriginalPrice;

    /**
     * 销量
     */
    @Column(name = "sales_volume")
    private Integer salesVolume;

    /**
     * 注水销量
     */
    @Column(name = "inflated_sales_volume")
    private Integer inflatedSalesVolume;

    /**
     * 卡号前缀
     */
    @Column(name = "card_number_prefix", length = 20)
    private String cardNumberPrefix;

    /**
     * 是否启用副卡（0否 1是）
     */
    @Column(name = "sub_card_enabled")
    private Boolean subCardEnabled;

    /**
     * 副卡数量限制
     */
    @Column(name = "sub_card_limit")
    private Integer subCardLimit;

    /**
     * 次数
     */
    @Column(name = "usage_count")
    private Integer usageCount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 