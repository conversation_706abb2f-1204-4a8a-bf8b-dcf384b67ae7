package com.titan.event.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 微信店铺订单通知记录
 */
@Data
@Entity
@Table(name = "wx_shop_order_notify")
@ApiModel(value = "微信店铺订单通知记录")
public class WxShopOrderNotify {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键")
    private Long id;

    @Column(name = "to_user_name", length = 64)
    @ApiModelProperty(value = "接收者")
    private String toUserName;

    @Column(name = "from_user_name", length = 64)
    @ApiModelProperty(value = "发送者openid")
    private String fromUserName;

    @Column(name = "create_time")
    @ApiModelProperty(value = "消息创建时间")
    private Date createTime;

    @Column(name = "order_id", length = 255)
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @Column(name = "shop_appid", length = 64)
    @ApiModelProperty(value = "店铺appid")
    private String shopAppid;

    @Column(name = "system_create_time")
    @ApiModelProperty(value = "系统创建时间")
    private Date systemCreateTime;

    @Column(name = "system_update_time")
    @ApiModelProperty(value = "系统更新时间")
    private Date systemUpdateTime;
} 