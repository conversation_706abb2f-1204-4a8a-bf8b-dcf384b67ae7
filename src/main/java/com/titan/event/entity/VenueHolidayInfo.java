package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 节假日信息表实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_holiday_info")
@DynamicInsert
@DynamicUpdate
public class VenueHolidayInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "holiday_id")
    private Long holidayId;

    /**
     * 节假日名称
     */
    @Column(name = "holiday_name", nullable = false)
    private String holidayName;

    /**
     * 节假日代码
     */
    @Column(name = "holiday_code", nullable = false)
    private String holidayCode;

    /**
     * 节假日类型(1:法定节假日, 2:调休工作日)
     */
    @Column(name = "holiday_type")
    private Integer holidayType;

    /**
     * 显示顺序
     */
    @Column(name = "display_order")
    private Integer displayOrder;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
} 