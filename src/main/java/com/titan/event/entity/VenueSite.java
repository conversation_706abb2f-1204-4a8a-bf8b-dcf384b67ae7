package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 场地信息表实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_site")
@DynamicInsert
@DynamicUpdate
public class VenueSite {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "site_id")
    private Long siteId;

    /**
     * 所属场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场地名称
     */
    @Column(name = "site_name", nullable = false)
    private String siteName;

    /**
     * 场地类型(1:羽毛球, 2:篮球, 3:乒乓球, 4:网球, 5:足球, 6:其他)
     */
    @Column(name = "site_type", nullable = false)
    private Integer siteType;

    /**
     * 场地状态(0:正常, 1:维修中, 2:已关闭)
     */
    @Column(name = "site_status")
    private Integer siteStatus;

    /**
     * 是否允许分割(0:不允许, 1:允许)
     */
    @Column(name = "is_split_allowed")
    private Boolean isSplitAllowed;

    /**
     * 最大容纳人数
     */
    @Column(name = "max_capacity")
    private Integer maxCapacity;

    /**
     * 面积(平方米)
     */
    @Column(name = "site_area")
    private Double siteArea;

    /**
     * 场地图片
     */
    @Column(name = "site_image_url")
    private String siteImageUrl;

    /**
     * 场地编号
     */
    @Column(name = "site_code")
    private String siteCode;

    /**
     * 显示顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag")
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
    
    /**
     * 法定节假日价格是否生效
     */
    @Column(name = "holiday_enabled")
    private String holidayEnabled;
    
    /**
     * 特殊日期价格是否生效
     */
    @Column(name = "special_date_enabled")
    private String specialDateEnabled;
} 