package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 场馆订单核销码表
 * 存储订单的核销信息，包括核销码、核销状态等
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "venue_verification_code")
@DynamicInsert
@DynamicUpdate
public class VenueVerificationCode {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单编号
     */
    @Column(name = "order_id", nullable = false, length = 32)
    private String orderId;

    /**
     * 核销码 - 12位数字
     */
    @Column(name = "verification_code", nullable = false, length = 12, unique = true)
    private String verificationCode;

    /**
     * 核销状态：0-未核销，1-已核销
     */
    @Column(name = "verification_status", nullable = false)
    private Integer verificationStatus;

    /**
     * 核销时间
     */
    @Column(name = "verification_time")
    private LocalDateTime verificationTime;

    /**
     * 核销人ID
     */
    @Column(name = "verifier_id")
    private Long verifierId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 