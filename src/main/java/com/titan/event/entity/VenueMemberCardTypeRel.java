package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 会员卡与卡类型关联表实体类
 */
@Data
@Entity
@Table(name = "venue_member_card_type_rel")
@DynamicInsert
@DynamicUpdate
public class VenueMemberCardTypeRel {

    /**
     * 关联ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rel_id")
    private Long relId;

    /**
     * 会员卡ID
     */
    @Column(name = "card_id", nullable = false)
    private Long cardId;

    /**
     * 卡类型ID
     */
    @Column(name = "card_type_id", nullable = false)
    private Long cardTypeId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 