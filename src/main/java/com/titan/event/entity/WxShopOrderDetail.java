package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 微信店铺订单详情实体类
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "wx_shop_order_detail")
public class WxShopOrderDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 店铺appid
     */
    private String shopAppid;
    
    /**
     * 买家openid
     */
    private String openid;
    
    /**
     * 买家在开放平台的唯一标识符
     */
    private String unionid;
    
    // 商品信息
    /**
     * 商品spuid
     */
    private Long productId;
    
    /**
     * 商品skuid
     */
    private Long skuId;
    
    /**
     * 商品缩略图
     */
    private String thumbImg;
    
    /**
     * sku数量
     */
    private Integer skuCnt;
    
    /**
     * 售卖单价(单位：分)
     */
    private Integer salePrice;
    
    /**
     * 商品标题
     */
    private String title;
    
    /**
     * 商品编码
     */
    private String skuCode;
    
    /**
     * 市场单价(单位：分)
     */
    private Integer marketPrice;
    
    /**
     * sku属性JSON字符串
     */
    private String skuAttrs;
    
    /**
     * sku实付总价(单位：分)，取estimate_price和change_price中较小值
     */
    private Integer realPrice;
    
    /**
     * 是否有商家优惠金额
     */
    private Boolean isDiscounted;
    
    /**
     * 使用所有优惠后sku总价(单位：分)
     */
    private Integer estimatePrice;
    
    /**
     * 是否修改过价格
     */
    private Boolean isChangePrice;
    
    /**
     * 改价后sku总价(单位：分)
     */
    private Integer changePrice;
    
    // 价格信息
    /**
     * 商品总价(单位：分)
     */
    private Integer productPrice;
    
    /**
     * 用户实付金额(单位：分)
     * order_price = original_order_price - discounted_price - finder_discounted_price - deduction_price - change_down_price
     */
    private Integer orderPrice;
    
    /**
     * 运费(单位：分)
     */
    private Integer freight;
    
    /**
     * 商家优惠金额(单位：分)
     */
    private Integer discountedPrice;
    
    /**
     * 订单原始价格(单位：分)
     * original_order_price = product_price + freight
     */
    private Integer originalOrderPrice;
    
    /**
     * 商品预估价格(单位：分)
     */
    private Integer estimateProductPrice;
    
    /**
     * 改价后降低金额(单位：分)
     */
    private Integer changeDownPrice;
    
    /**
     * 改价后运费(单位：分)
     */
    private Integer changeFreight;
    
    /**
     * 是否修改运费
     */
    private Boolean isChangeFreight;
    
    /**
     * 是否使用了会员积分抵扣
     */
    private Boolean useDeduction;
    
    /**
     * 会员积分抵扣金额(单位：分)
     */
    private Integer deductionPrice;
    
    /**
     * 商家实收金额(单位：分)
     * merchant_receieve_price = original_order_price - discounted_price - deduction_price - change_down_price
     */
    private Integer merchantReceievePrice;
    
    /**
     * 商家优惠金额(单位：分)，含义同discounted_price
     */
    private Integer merchantDiscountedPrice;
    
    /**
     * 达人优惠金额(单位：分)
     */
    private Integer finderDiscountedPrice;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    public WxShopOrderDetail() {

    }
}