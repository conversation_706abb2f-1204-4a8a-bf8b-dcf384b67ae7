package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 场地时段价格实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_timeslot_price")
@DynamicInsert
@DynamicUpdate
public class VenueTimeslotPrice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "price_id")
    private Long priceId;

    /**
     * 场地ID
     */
    @Column(name = "site_id", nullable = false)
    private Long siteId;
    
    /**
     * 分区ID，null表示全场
     */
    @Column(name = "section_id")
    private Long sectionId;
    
    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private String startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "end_time", nullable = false)
    private String endTime;
    
    /**
     * 价格
     */
    @Column(name = "price", nullable = false)
    private BigDecimal price;
    
    /**
     * 星期几，1-7代表周一到周日
     */
    @Column(name = "week_days")
    private Integer weekDays;
    
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag")
    private String delFlag;
    
    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    
    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
    
    /**
     * 额外的查询参数，不是数据库字段
     */
    @Transient
    private Map<String, Object> params;
} 