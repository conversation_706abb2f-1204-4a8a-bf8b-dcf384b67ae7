package com.titan.event.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 部门表
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "sys_dept")
public class SysDept implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 部门id */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long deptId;

    /** 部门名称 */
    private String deptName;

    /** 父部门id */
    private Long parentId;

    /** 客服id */
    @Column(name = "service_id")
    private String serviceId;

    // 其他字段省略...
} 