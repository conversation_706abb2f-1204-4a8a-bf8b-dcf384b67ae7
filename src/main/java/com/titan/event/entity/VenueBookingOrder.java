package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 场馆预约订单详情
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "venue_booking_order")
@DynamicInsert
@DynamicUpdate
public class VenueBookingOrder {

    /**
     * 预订订单ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单编号
     */
    @Column(name = "order_id", nullable = false, length = 32)
    private String orderId;

    /**
     * 场地ID
     */
    @Column(name = "site_id", nullable = false)
    private Long siteId;

    /**
     * 场地名称
     */
    @Column(name = "site_name", nullable = false, length = 64)
    private String siteName;

    /**
     * 场地图片
     */
    @Column(name = "site_image", columnDefinition = "text")
    private String siteImage;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场馆名称
     */
    @Column(name = "venue_name", nullable = false, length = 64)
    private String venueName;

    /**
     * 场地分区ID
     */
    @Column(name = "section_id")
    private Long sectionId;

    /**
     * 场地分区名称
     */
    @Column(name = "section_name", length = 100)
    private String sectionName;

    /**
     * 预约日期
     */
    @Column(name = "booking_date", nullable = false)
    private LocalDate bookingDate;

    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalTime startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time", nullable = false)
    private LocalTime endTime;

    /**
     * 时长(分钟)
     */
    @Column(name = "duration", nullable = false)
    private Integer duration;

    /**
     * 价格
     */
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 原价
     */
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    /**
     * 预约状态：0-待使用，1-已使用，2-已取消
     */
    @Column(name = "booking_status", nullable = false)
    private Integer bookingStatus;

    /**
     * 预订用途
     */
    @Column(name = "booking_purpose", length = 500)
    private String bookingPurpose;

    /**
     * 参与人数
     */
    @Column(name = "participant_count")
    private Integer participantCount;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 场地标题（完整标题：场地类型名称+场地名称+分区名称）
     */
    @Column(name = "title", length = 200)
    private String title;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 