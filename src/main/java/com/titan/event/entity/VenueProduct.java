package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 场馆商品实体类
 */
@Data
@Entity
@Table(name = "venue_product")
@DynamicInsert
@DynamicUpdate
public class VenueProduct {

    /**
     * 商品ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "product_id")
    private Long productId;

    /**
     * 商品名称
     */
    @Column(name = "product_name", nullable = false, length = 100)
    private String productName;

    /**
     * 商品编码
     */
    @Column(name = "product_code", nullable = false, length = 32)
    private String productCode;

    /**
     * 所属场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 上下架状态(0:下架 1:上架)
     */
    @Column(name = "status")
    private Boolean status;

    /**
     * 商品图片(多图JSON格式)
     */
    @Column(name = "product_images", columnDefinition = "text")
    private String productImages;

    /**
     * 注水销量
     */
    @Column(name = "fake_sales")
    private Integer fakeSales;

    /**
     * 真实销量
     */
    @Column(name = "real_sales")
    private Integer realSales;

    /**
     * 价格
     */
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 划线价格
     */
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    /**
     * 商品详细介绍(富文本)
     */
    @Column(name = "product_detail", columnDefinition = "text")
    private String productDetail;

    /**
     * 库存
     */
    @Column(name = "stock")
    private Integer stock;

    /**
     * 限购数量
     */
    @Column(name = "purchase_limit")
    private Integer purchaseLimit;

    /**
     * 购买须知详情(富文本)
     */
    @Column(name = "purchase_notes", columnDefinition = "text")
    private String purchaseNotes;

    /**
     * 有效期(天)
     */
    @Column(name = "validity_days")
    private Integer validityDays;

    /**
     * 排序序号
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 