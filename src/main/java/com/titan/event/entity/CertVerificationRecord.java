package com.titan.event.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 身份证二要素核验记录
 * <AUTHOR>
 */
@Entity
@Table(name = "cert_verification_record")
@Data
public class CertVerificationRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 姓名（明文）
     */
    @Column(name = "cert_name", length = 50, nullable = false)
    private String certName;

    /**
     * 身份证号（明文）
     */
    @Column(name = "cert_no", length = 20, nullable = false)
    private String certNo;

    /**
     * 验证结果 1-通过 0-失败
     */
    @Column(name = "verification_result", nullable = false)
    private Integer verificationResult;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        createTime = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
} 