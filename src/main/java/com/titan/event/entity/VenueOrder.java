package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 场馆订单总表
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "venue_order")
@DynamicInsert
@DynamicUpdate
public class VenueOrder {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单编号
     */
    @Column(name = "order_id", nullable = false, length = 32, unique = true)
    private String orderId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id")
    private Long venueId;

    /**
     * 场馆名称
     */
    @Column(name = "venue_name", length = 100)
    private String venueName;

    /**
     * 订单类型：1-场馆预约，2-场馆产品，3-卡片购买
     */
    @Column(name = "order_type", nullable = false)
    private Integer orderType;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name", length = 32)
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    /**
     * 订单总金额
     */
    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @Column(name = "discount_amount", precision = 10, scale = 2)
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    @Column(name = "actual_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal actualAmount;

    /**
     * 备注
     */
    @Column(name = "remark", length = 255)
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 