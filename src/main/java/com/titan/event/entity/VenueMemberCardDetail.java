package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员卡使用明细表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "venue_member_card_detail")
@DynamicInsert
@DynamicUpdate
public class VenueMemberCardDetail {

    /**
     * 明细ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "detail_id")
    private Long detailId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name", length = 100)
    private String userName;

    /**
     * 会员卡ID
     */
    @Column(name = "card_id", nullable = false)
    private Long cardId;

    /**
     * 会员卡名称
     */
    @Column(name = "card_name", nullable = false, length = 100)
    private String cardName;

    /**
     * 用户会员卡ID
     */
    @Column(name = "user_card_id")
    private Long userCardId;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场馆名称
     */
    @Column(name = "venue_name", nullable = false, length = 100)
    private String venueName;

    /**
     * 操作类型（1消费 2充值 3退款）
     */
    @Column(name = "operation_type", nullable = false)
    private Integer operationType;

    /**
     * 操作金额
     */
    @Column(name = "amount", precision = 10, scale = 2)
    private BigDecimal amount;

    /**
     * 操作次数
     */
    @Column(name = "usage_count")
    private Integer usageCount;

    /**
     * 操作时间
     */
    @Column(name = "operation_time", nullable = false)
    private LocalDateTime operationTime;

    /**
     * 操作说明
     */
    @Column(name = "operation_description", length = 500)
    private String operationDescription;

    /**
     * 操作人员
     */
    @Column(name = "operator", length = 100)
    private String operator;

    /**
     * 操作来源（1管理端 2用户端）
     */
    @Column(name = "operation_source")
    private Integer operationSource;

    /**
     * 余额
     */
    @Column(name = "balance", precision = 10, scale = 2)
    private BigDecimal balance;

    /**
     * 剩余次数
     */
    @Column(name = "remaining_count")
    private Integer remainingCount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 