package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 场馆标签实体类
 */
@Data
@Entity
@Table(name = "venue_tag")
@DynamicInsert
@DynamicUpdate
public class VenueTag {

    /**
     * 标签ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "tag_id")
    private Long tagId;

    /**
     * 标签名称
     */
    @Column(name = "tag_name", nullable = false, length = 50)
    private String tagName;

    /**
     * 标签类型(1:运动项目, 2:设施, 3:服务, 4:其他)
     */
    @Column(name = "tag_type")
    private Integer tagType;

    /**
     * 标签图标
     */
    @Column(name = "tag_icon", length = 255)
    private String tagIcon;

    /**
     * 显示顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 