package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员卡价格表实体类
 */
@Data
@Entity
@Table(name = "venue_member_card_price")
@DynamicInsert
@DynamicUpdate
public class VenueMemberCardPrice {

    /**
     * 价格ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "price_id")
    private Long priceId;

    /**
     * 会员卡ID
     */
    @Column(name = "card_id", nullable = false)
    private Long cardId;

    /**
     * 场地ID
     */
    @Column(name = "site_id", nullable = false)
    private Long siteId;

    /**
     * 时间段
     */
    @Column(name = "time_slot", nullable = false, length = 20)
    private String timeSlot;

    /**
     * 场地类型（0全场 1半场）
     */
    @Column(name = "site_section")
    private Integer siteSection;

    /**
     * 价格
     */
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 