package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 场馆标签关联实体类
 */
@Data
@Entity
@Table(name = "venue_tag_rel", uniqueConstraints = {
    @UniqueConstraint(name = "idx_venue_tag", columnNames = {"venue_id", "tag_id"})
})
@DynamicInsert
@DynamicUpdate
public class VenueTagRel {

    /**
     * 关联ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rel_id")
    private Long relId;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 标签ID
     */
    @Column(name = "tag_id", nullable = false)
    private Long tagId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
} 