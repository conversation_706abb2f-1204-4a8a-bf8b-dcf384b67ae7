package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户会员卡表实体类
 */
@Data
@Entity
@Table(name = "venue_member_card_user")
@DynamicInsert
@DynamicUpdate
public class VenueMemberCardUser {

    /**
     * 用户会员卡ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_card_id")
    private Long userCardId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name", length = 100)
    private String userName;

    /**
     * 会员卡ID
     */
    @Column(name = "card_id", nullable = false)
    private Long cardId;

    /**
     * 会员卡名称
     */
    @Column(name = "card_name", nullable = false, length = 100)
    private String cardName;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场馆名称
     */
    @Column(name = "venue_name", nullable = false, length = 100)
    private String venueName;

    /**
     * 会员卡号
     */
    @Column(name = "card_number", nullable = false, length = 30)
    private String cardNumber;

    /**
     * 卡类型（1储值卡 2计次卡）
     */
    @Column(name = "card_type", nullable = false)
    private Integer cardType;

    /**
     * 是否无限次（0否 1是）
     */
    @Column(name = "unlimited_usage")
    private Integer unlimitedUsage;

    /**
     * 余额
     */
    @Column(name = "balance", precision = 10, scale = 2)
    private BigDecimal balance;

    /**
     * 剩余次数
     */
    @Column(name = "remaining_count")
    private Integer remainingCount;

    /**
     * 有效期开始时间
     */
    @Column(name = "validity_start_time")
    private LocalDateTime validityStartTime;

    /**
     * 有效期结束时间
     */
    @Column(name = "validity_end_time")
    private LocalDateTime validityEndTime;

    /**
     * 状态（0正常 1停用 2过期）
     */
    @Column(name = "status", length = 1)
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 