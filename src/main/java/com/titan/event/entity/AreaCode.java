package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 赛事对象 event
 * 
 * <AUTHOR>
 * @date 2024-11-21
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "area_code")
public class AreaCode implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String code;

    private String name;

    public AreaCode() {

    }
}
