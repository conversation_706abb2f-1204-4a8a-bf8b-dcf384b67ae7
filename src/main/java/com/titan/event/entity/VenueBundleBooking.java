package com.titan.event.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 捆绑订场实体类
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_bundle_booking")
public class VenueBundleBooking implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 捆绑订场ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场地类型(1:羽毛球, 2:篮球, 3:乒乓球, 4:网球, 5:足球, 6:其他)
     */
    @Column(name = "site_type", nullable = false)
    private Integer siteType;

    /**
     * 场地ID列表，以逗号分隔
     */
    @Column(name = "site_id_list", nullable = false, length = 500)
    private String siteIdList;

    /**
     * 场地名称
     */
    @Column(name = "site_name", nullable = false, length = 200)
    private String siteName;

    /**
     * 场馆分区ID列表，以逗号分隔
     */
    @Column(name = "section_id_list", length = 500)
    private String sectionIdList;

    /**
     * 开始日期
     */
    @Column(name = "start_date", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "end_date", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 星期几（逗号分隔的字符串，例如：1,2,3,4,5,6,7表示周一到周日）
     */
    @Column(name = "week_days", nullable = false, length = 50)
    private String weekDays;

    /**
     * 时间段列表，以逗号分隔，如"08:00-09:00,09:00-10:00"
     */
    @Column(name = "time_ranges", nullable = false, length = 500)
    private String timeRanges;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 