package com.titan.event.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "event_carousel")
public class EventCarousel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 类型
     */
    private Long carouselType;

    /**
     * 轮播图片
     */
    private String carouselPic;

    /**
     * 内容
     */
    private Long contentId;

    /**
     * 文本1
     */
    private String contentText1;

    /**
     * 文本1
     */
    private String contentText2;

    /**
     * 是否显示
     */
    private Long showStatus;

    private Long sort;

    public EventCarousel() {

    }
}
