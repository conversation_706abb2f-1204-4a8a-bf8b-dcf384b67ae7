package com.titan.event.entity;

import com.titan.event.enums.DefaultFlag;
import com.titan.event.util.NumUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:54
 */
@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "platform_user_info")
public class UserInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long userId;

    @Column(name = "uuid")
    private String uuid;

    private String openId;

    private String unionId;

    @Column(name = "nickname")
    private String nickname;

    @Column(name = "phone")
    private String phone;

    private String phoneCode;

    @Column(name = "avatar")
    private String avatar;

    /**
     * 是否游客
     */
    @Enumerated
    private DefaultFlag touristState;

    /**
     * 是否管理员
     */
    @Enumerated
    @Column(name = "admin_state")
    private DefaultFlag adminState = DefaultFlag.NO;

    @Column(name = "create_date")
    @CreatedDate
    private LocalDateTime createDate;

    @Column(name = "modify_date")
    @LastModifiedDate
    private LocalDateTime modifyDate;

    public UserInfo() {
        this.adminState = DefaultFlag.NO;
    }
}
