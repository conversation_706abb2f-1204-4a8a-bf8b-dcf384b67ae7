package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 节假日日期实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_holiday_date")
@DynamicInsert
@DynamicUpdate
public class VenueHolidayDate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "holiday_id")
    private Long holidayId;

    /**
     * 节假日开始日期
     */
    @Column(name = "start_date")
    private Date startDate;
    
    /**
     * 节假日结束日期
     */
    @Column(name = "end_date")
    private Date endDate;
    
    /**
     * 状态（0正常 1停用）
     */
    @Column(name = "status")
    private String status;
    
    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    
    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
} 