package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 场地分区表实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_site_section")
@DynamicInsert
@DynamicUpdate
public class VenueSiteSection {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "section_id")
    private Long sectionId;

    /**
     * 所属场地ID
     */
    @Column(name = "site_id", nullable = false)
    private Long siteId;

    /**
     * 分区名称
     */
    @Column(name = "section_name", nullable = false)
    private String sectionName;

    /**
     * 分区编号
     */
    @Column(name = "section_code")
    private String sectionCode;

    /**
     * 分区状态（0正常 1停用）
     */
    @Column(name = "status")
    private String status;

    /**
     * 座位数量
     */
    @Column(name = "seat_count")
    private Integer seatCount;

    /**
     * 显示顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag")
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
} 