package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 场馆信息实体类
 */
@Data
@Entity
@Table(name = "venue")
@DynamicInsert
@DynamicUpdate
public class Venue {

    /**
     * 场馆ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "venue_id")
    private Long venueId;

    /**
     * 场馆名称
     */
    @Column(name = "venue_name", nullable = false, length = 100)
    private String venueName;

    /**
     * 场馆地址
     */
    @Column(name = "venue_address", nullable = false, length = 255)
    private String venueAddress;

    /**
     * 场馆面积(平方米)
     */
    @Column(name = "venue_area")
    private Double venueArea;

    /**
     * 场馆容量(人数)
     */
    @Column(name = "venue_capacity")
    private Integer venueCapacity;

    /**
     * 场馆类型(1:体育馆, 2:游泳馆, 3:田径场, 4:综合场馆)
     */
    @Column(name = "venue_type")
    private Integer venueType;

    /**
     * 场馆状态(0:闲置中, 1:使用中, 2:维修中)
     */
    @Column(name = "venue_status")
    private Integer venueStatus;

    /**
     * 营业状态(0:休息中, 1:营业中)
     */
    @Column(name = "business_status")
    private Integer businessStatus;

    /**
     * 是否手动设置营业状态(0:自动, 1:手动)
     */
    @Column(name = "business_status_manual")
    private Integer businessStatusManual;

    /**
     * 营业启始时间
     */
    @Column(name = "business_start_time", length = 10)
    private String businessStartTime;

    /**
     * 营业结束时间
     */
    @Column(name = "business_end_time", length = 10)
    private String businessEndTime;

    /**
     * 联系人
     */
    @Column(name = "contact_person", length = 50)
    private String contactPerson;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    /**
     * 开放时间
     */
    @Column(name = "open_time", length = 20)
    private String openTime;

    /**
     * 关闭时间
     */
    @Column(name = "close_time", length = 20)
    private String closeTime;

    /**
     * 营业时间描述
     */
    @Column(name = "business_time_desc", length = 100)
    private String businessTimeDesc;

    /**
     * 场馆图片地址
     */
    @Column(name = "venue_image_url", length = 255)
    private String venueImageUrl;

    /**
     * 经度
     */
    @Column(name = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @Column(name = "latitude")
    private Double latitude;

    /**
     * 场馆详细介绍(富文本)
     */
    @Column(name = "venue_description", columnDefinition = "text")
    private String venueDescription;

    /**
     * 场馆地区
     */
    @Column(name = "venue_region", length = 255)
    private String venueRegion;

    /**
     * 省份
     */
    @Column(name = "province", length = 50)
    private String province;

    /**
     * 城市
     */
    @Column(name = "city", length = 50)
    private String city;

    /**
     * 区县
     */
    @Column(name = "district", length = 50)
    private String district;

    /**
     * 微信客服ID
     */
    @Column(name = "wx_customer_service_id", length = 64)
    private String wxCustomerServiceId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
} 