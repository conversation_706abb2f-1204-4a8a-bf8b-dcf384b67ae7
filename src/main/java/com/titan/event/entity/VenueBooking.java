package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预订记录表实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_booking")
@DynamicInsert
@DynamicUpdate
public class VenueBooking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "booking_id")
    private Long bookingId;

    /**
     * 场馆ID
     */
    @Column(name = "venue_id", nullable = false)
    private Long venueId;

    /**
     * 场地ID
     */
    @Column(name = "site_id", nullable = false)
    private Long siteId;

    /**
     * 分区ID(为空表示整个场地)
     */
    @Column(name = "section_id")
    private Long sectionId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 预订日期
     */
    @Column(name = "booking_date", nullable = false)
    private Date bookingDate;

    /**
     * 开始时间(HH:MM)
     */
    @Column(name = "start_time", nullable = false)
    private String startTime;

    /**
     * 结束时间(HH:MM)
     */
    @Column(name = "end_time", nullable = false)
    private String endTime;

    /**
     * 预订状态(0:待支付, 1:已支付, 2:已取消, 3:已完成, 4:已拒绝)
     */
    @Column(name = "booking_status")
    private Integer bookingStatus;

    /**
     * 预订类型(1:普通预订, 2:长期包场, 3:管理员锁定, 4:整租)
     */
    @Column(name = "booking_type")
    private Integer bookingType;

    /**
     * 实际支付金额
     */
    @Column(name = "actual_price", nullable = false)
    private BigDecimal actualPrice;

    /**
     * 原始价格
     */
    @Column(name = "original_price", nullable = false)
    private BigDecimal originalPrice;

    /**
     * 使用人数
     */
    @Column(name = "attendee_count")
    private Integer attendeeCount;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 支付时间
     */
    @Column(name = "payment_time")
    private Date paymentTime;

    /**
     * 支付方式(1:微信, 2:支付宝, 3:现场支付)
     */
    @Column(name = "payment_method")
    private Integer paymentMethod;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag")
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
} 