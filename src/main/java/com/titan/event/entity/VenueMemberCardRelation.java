package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 会员卡主副卡关系表实体类
 */
@Data
@Entity
@Table(name = "venue_member_card_relation")
@DynamicInsert
@DynamicUpdate
public class VenueMemberCardRelation {

    /**
     * 关系ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "relation_id")
    private Long relationId;

    /**
     * 会员卡ID
     */
    @Column(name = "card_id", nullable = false)
    private Long cardId;

    /**
     * 会员卡用户ID
     */
    @Column(name = "card_user_id", nullable = false)
    private Long cardUserId;

    /**
     * 卡类型（1主卡 2副卡）
     */
    @Column(name = "card_relation_type", nullable = false)
    private Integer cardRelationType;

    /**
     * 状态（0正常 1停用）
     */
    @Column(name = "status", length = 1)
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag;

    /**
     * 创建者
     */
    @Column(name = "create_by", length = 64)
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
} 