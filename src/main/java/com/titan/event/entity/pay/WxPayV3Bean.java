package com.titan.event.entity.pay;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <p>IJPay 让支付触手可及，封装了微信支付、支付宝支付、银联支付常用的支付方式以及各种常用的接口。</p>
 *
 * <p>不依赖任何第三方 mvc 框架，仅仅作为工具使用简单快速完成支付模块的开发，可轻松嵌入到任何系统里。 </p>
 *
 * <p>IJPay 交流群: 723992875、864988890</p>
 *
 * <p>Node.js 版: <a href="https://gitee.com/javen205/TNWX">https://gitee.com/javen205/TNWX</a></p>
 *
 * <p>微信配置 Bean</p>
 *
 * <AUTHOR>
 */
@Component
@PropertySource("classpath:/wxpay_v3.properties")
@ConfigurationProperties(prefix = "v3")
public class WxPayV3Bean {
	private String appId;
	private String keyPath;
	private String publicKeyPath;
	private String certPath;
	private String certP12Path;
	private String platformCertPath;
	private String mchId;
	private String apiKey;
	private String apiKey3;
	private String domain;

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getKeyPath() {
		return keyPath;
	}

	public void setKeyPath(String keyPath) {
		this.keyPath = keyPath;
	}

	public String getPublicKeyPath() {
		return publicKeyPath;
	}

	public void setPublicKeyPath(String publicKeyPath) {
		this.publicKeyPath = publicKeyPath;
	}

	public String getCertPath() {
		return certPath;
	}

	public void setCertPath(String certPath) {
		this.certPath = certPath;
	}

	public String getCertP12Path() {
		return certP12Path;
	}

	public void setCertP12Path(String certP12Path) {
		this.certP12Path = certP12Path;
	}

	public String getPlatformCertPath() {
		return platformCertPath;
	}

	public void setPlatformCertPath(String platformCertPath) {
		this.platformCertPath = platformCertPath;
	}

	public String getMchId() {
		return mchId;
	}

	public void setMchId(String mchId) {
		this.mchId = mchId;
	}

	public String getApiKey() {
		return apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public String getApiKey3() {
		return apiKey3;
	}

	public void setApiKey3(String apiKey3) {
		this.apiKey3 = apiKey3;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	@Override
	public String toString() {
		return "WxPayV3Bean{" +
			"keyPath='" + keyPath + '\'' +
			", certPath='" + certPath + '\'' +
			", certP12Path='" + certP12Path + '\'' +
			", platformCertPath='" + platformCertPath + '\'' +
			", mchId='" + mchId + '\'' +
			", apiKey='" + apiKey + '\'' +
			", apiKey3='" + apiKey3 + '\'' +
			", domain='" + domain + '\'' +
			'}';
	}
}
