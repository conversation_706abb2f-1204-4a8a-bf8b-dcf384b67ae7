package com.titan.event.entity.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@Entity
@AllArgsConstructor
@Table(name = "platform_pay_order")
public class PayOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long userId;

    private String openId;

    private String orderId;

    private String timeExpire;

    private double amount;

    private String productId;

    private String body;

    private String payType;

    /**
     * SUCCESS：支付成功
     * REFUND：转入退款
     * NOTPAY：未支付
     * CLOSED：已关闭
     * REVOKED：已撤销（付款码支付）
     * USERPAYING：用户支付中（付款码支付）
     * PAYERROR：支付失败(其他原因，如银行返回失败)
     * NONEEDPAY: 无需支付
     */
    private String payStatus;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 退单id
     */
    private String refundId;

    private String transactionId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 支付回调通知时间
     */
    private LocalDateTime payNotifyTime;

    /**
     * 支付回调通知原始报文
     */
    private String payNotifyData;

    /**
     * 退款回调通知时间
     */
    private LocalDateTime refundNotifyTime;

    /**
     * 退单创建时间
     */
    private LocalDateTime refundOrderTime;

    /**
     * 退款回调通知原始报文
     */
    private String refundNotifyData;

    private String prepayId;

    private String uploadStatus;

    /**
     * 用户卡ID（储值卡充值时使用）
     */
    private Long userCardId;

    /**
     * 会员卡ID（卡片购买时使用）
     */
    private Long cardId;

    public PayOrder() {

    }
}
