package com.titan.event.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 特殊日期价格实体类
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "venue_special_date_price")
@DynamicInsert
@DynamicUpdate
public class VenueSpecialDatePrice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "price_id")
    private Long priceId;

    /**
     * 特殊日期ID
     */
    @Column(name = "special_date_id", nullable = false)
    private Long specialDateId;

    /**
     * 场地ID
     */
    @Column(name = "site_id", nullable = false)
    private Long siteId;

    /**
     * 场地区域(0全场,1半场)
     */
    @Column(name = "site_section")
    private Integer siteSection;

    /**
     * 时间段，如 "00:00 ~ 01:00"
     */
    @Column(name = "time_slot")
    private String timeSlot;

    /**
     * 价格
     */
    @Column(name = "price", nullable = false)
    private BigDecimal price;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}