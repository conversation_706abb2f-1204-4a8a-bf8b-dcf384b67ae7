package com.titan.event.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务配置类
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "titan.job")
public class JobConfig {
    
    private static boolean jobEnabled = true;
    
    /**
     * 是否启用定时任务，默认启用
     */
    private boolean enabled = true;
    
    /**
     * setter方法被Spring调用时同时设置静态变量
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        JobConfig.jobEnabled = enabled;
        log.info("定时任务全局开关设置为: {}", enabled);
    }
    
    /**
     * 判断当前是否应该执行定时任务
     * @return 是否执行定时任务
     */
    public static boolean isJobEnabled() {
        return jobEnabled;
    }
} 