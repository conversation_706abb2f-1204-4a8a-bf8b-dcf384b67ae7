package com.titan.event.config.properties;

import lombok.Data;

/**
 * <AUTHOR> loso
 * @version :
 * @date : Created in 2021/7/31 8:17 下午
 * @description : 缓存配置
 * @modified By : loso
 */
@Data
public class CacheProperties {

    /**
     * 描述需要创建的效缓存的名称
     */
    private String cacheNames;

    /**
     * 描述缓存常用的初始容量
     */
    private int initialCapacity;

    /**
     * 描述缓存最大的缓存大小
     */
    private int maximumSize;

    /**
     * 描述短效缓存在写入之后过期的时间
     */
    private int expireAfterWrite;

    /**
     * 描述短效缓存在写入之后过期的时间
     */
    private int expireAfterAccess;
}
