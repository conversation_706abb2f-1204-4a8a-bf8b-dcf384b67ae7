package com.titan.event.config;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> loso
 * @version :
 * @date : Created in 2021/7/31 8:33 下午
 * @description :
 * @modified By : loso
 */
@Slf4j
//@EnableCaching
//@EnableConfigurationProperties({ShortCacheProperties.class, LongCacheProperties.class})
//@Configuration
public class CaffeineCacheConfig {

//    /**
//     * 长效缓存
//     */
//    private LongCacheProperties longCacheProperties;
//    /**
//     * 短效缓存
//     */
//    private ShortCacheProperties shortCacheProperties;
//
//    /**
//     * 创建基于Caffeine的Cache Manager
//     * @return
//     */
//    @Bean("caffeineCacheManager")
//    public CacheManager CaffeineCacheManager() {
//        log.info("cache com.jindidata.es2.salarycore initialize ...");
//        SimpleCacheManager cacheManager = new SimpleCacheManager();
//        List<CaffeineCache> caffeineCacheList = new ArrayList<CaffeineCache>();
//        List<CaffeineCache> shortCacheList = getCacheList(shortCacheProperties);
//        List<CaffeineCache> longCacheList = getCacheList(longCacheProperties);
//        caffeineCacheList.addAll(shortCacheList);
//        caffeineCacheList.addAll(longCacheList);
//        cacheManager.setCaches(caffeineCacheList);
//        return cacheManager;
//    }
//
//    private List<CaffeineCache> getCacheList(CacheProperties cacheProperties){
//        List<CaffeineCache> caffeineCacheList = new ArrayList<CaffeineCache>();
//        List<String> shortCacheNameList = Arrays.asList(cacheProperties.getCacheNames().split(","));
//        shortCacheNameList.forEach(name -> {
//            LoadingCache<Object, Object> shortCaffeine = Caffeine.newBuilder().recordStats()
//                    .expireAfterWrite(cacheProperties.getExpireAfterWrite(), TimeUnit.SECONDS)
//                    .expireAfterAccess(cacheProperties.getExpireAfterAccess(),TimeUnit.SECONDS)
//                    .initialCapacity(cacheProperties.getInitialCapacity())
//                    .maximumSize(cacheProperties.getMaximumSize())
//                    .build(OptimisticTypesPersistence::load);
//            caffeineCacheList.add(new CaffeineCache(name, shortCaffeine));
//        });
//        return caffeineCacheList;
//    }
//
//    public CaffeineCacheConfig(LongCacheProperties longCacheProperties, ShortCacheProperties shortCacheProperties) {
//        this.longCacheProperties = longCacheProperties;
//        this.shortCacheProperties = shortCacheProperties;
//    }
}
