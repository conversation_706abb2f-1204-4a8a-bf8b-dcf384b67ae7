package com.titan.event.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

/**
 * 定时任务调度配置
 */
@Slf4j
@Configuration
@EnableScheduling
public class SchedulingConfiguration implements SchedulingConfigurer {

    private final JobConfig jobConfig;

    public SchedulingConfiguration(JobConfig jobConfig) {
        this.jobConfig = jobConfig;
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        if (!jobConfig.isEnabled()) {
            log.info("定时任务已全局禁用，但任务仍会被调度，在任务执行时会根据开关决定是否执行具体业务逻辑");
        } else {
            log.info("定时任务已启用");
        }
    }
} 