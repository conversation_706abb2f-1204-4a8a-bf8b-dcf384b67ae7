package com.titan.event.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * 定时任务执行条件
 */
@Slf4j
public class JobCondition implements Condition {

    private final JobConfig jobConfig;

    public JobCondition(JobConfig jobConfig) {
        this.jobConfig = jobConfig;
    }

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        boolean enabled = jobConfig.isEnabled();
        log.debug("定时任务条件检查，结果: {}", enabled);
        return enabled;
    }
} 