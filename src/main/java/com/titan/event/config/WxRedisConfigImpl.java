package com.titan.event.config;

import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.redis.core.StringRedisTemplate;

@EqualsAndHashCode(callSuper = true)
@Data
public class WxRedisConfigImpl extends WxMaDefaultConfigImpl {
    
    private final StringRedisTemplate redisTemplate;
    private final String keyPrefix;
    
    public WxRedisConfigImpl(StringRedisTemplate redisTemplate, String appId) {
        this.redisTemplate = redisTemplate;
        this.keyPrefix = "wx:miniapp:" + appId + ":";
    }
    
    @Override
    public String getAccessToken() {
        return redisTemplate.opsForValue().get(keyPrefix + "access_token");
    }
    
    @Override
    public void setAccessToken(String accessToken) {
        redisTemplate.opsForValue().set(keyPrefix + "access_token", accessToken);
    }
} 