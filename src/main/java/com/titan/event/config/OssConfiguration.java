package com.titan.event.config;

import com.titan.event.util.oss.OssBootUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 云存储 配置
 * @author: jeecg-boot
 */
@Configuration
@ConditionalOnProperty(prefix = "oss", name = "endpoint")
public class OssConfiguration {

    @Value("${oss.endpoint}")
    private String endpoint;
    @Value("${oss.accessKey}")
    private String accessKeyId;
    @Value("${oss.secretKey}")
    private String accessKeySecret;
    @Value("${oss.bucketName}")
    private String bucketName;
    @Value("${oss.staticDomain:}")
    private String staticDomain;
    @Value("${oss.roleArn}")
    private String roleArn;
    @Value("${oss.regionId}")
    private String regionId;
    @Value("${oss.roleSessionName}")
    private String roleSessionName;

    @Bean
    public void initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);
        OssBootUtil.setRoleArn(roleArn);
        OssBootUtil.setRegionId(regionId);
        OssBootUtil.setRoleSessionName(roleSessionName);
    }
}