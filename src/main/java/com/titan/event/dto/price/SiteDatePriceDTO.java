package com.titan.event.dto.price;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 场地日期价格DTO
 * 
 * <AUTHOR>
 */
@Data
public class SiteDatePriceDTO {
    
    /**
     * 场地ID
     */
    private Long siteId;
    
    /**
     * 场地名称
     */
    private String siteName;
    
    /**
     * 分区ID
     */
    private Long sectionId;
    
    /**
     * 分区名称
     */
    private String sectionName;
    
    /**
     * 日期
     */
    private String date;
    
    /**
     * 各时段的价格，key为时段ID（0-23表示0点到23点），value为价格
     */
    private Map<Integer, BigDecimal> timeslotPrices = new HashMap<>();
    
    /**
     * 各时段价格的来源，key为时段ID，value为价格来源（1:普通价格, 2:节假日价格, 3:特殊日期价格）
     */
    private Map<Integer, Integer> priceSource = new HashMap<>();
    
    /**
     * 设置指定时段的价格和来源
     * 
     * @param timeSlot 时段ID（0-23）
     * @param price 价格
     * @param source 价格来源
     */
    public void setTimeslotPrice(Integer timeSlot, BigDecimal price, Integer source) {
        this.timeslotPrices.put(timeSlot, price);
        this.priceSource.put(timeSlot, source);
    }
} 