package com.titan.event.util;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * 时区日期时间工具类
 */
public class DateTimeZoneUtil {

    private static final DateTimeFormatter timeZoneFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'");

    /**
     * 将时间戳转换为带时区的时间字符串
     * @param timestamp 时间戳（毫秒）
     * @return 带时区的时间字符串，格式为yyyy-MM-dd'T'HH:mm:ss'+08:00'
     */
    public static String dateToTimeZone(long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.of("+8"));
        return dateTime.format(timeZoneFormatter);
    }
    
    /**
     * 获取当前时间的带时区的时间字符串
     * @return 当前时间的带时区字符串
     */
    public static String getCurrentTimeZone() {
        return LocalDateTime.now().format(timeZoneFormatter);
    }
    
    /**
     * 计算从当前时间开始，指定分钟后的时间字符串
     * @param minutes 分钟数
     * @return 指定分钟后的时间字符串
     */
    public static String getTimeZoneAfterMinutes(int minutes) {
        return LocalDateTime.now().plusMinutes(minutes).format(timeZoneFormatter);
    }
    
    /**
     * 将ISO格式的时间字符串转换为LocalDateTime
     * 支持yyyy-MM-dd'T'HH:mm:ss'+08:00'格式
     * 
     * @param timeZoneStr ISO格式时间字符串
     * @return LocalDateTime对象，如果转换失败则返回null
     */
    public static LocalDateTime parseTimeZone(String timeZoneStr) {
        if (timeZoneStr == null || timeZoneStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return LocalDateTime.parse(
                timeZoneStr.replace("+08:00", ""), 
                DateTimeFormatter.ISO_LOCAL_DATE_TIME
            );
        } catch (Exception e) {
            // 尝试另一种格式解析
            try {
                return LocalDateTime.parse(timeZoneStr, DateTimeFormatter.ISO_DATE_TIME);
            } catch (Exception ex) {
                return null;
            }
        }
    }
} 