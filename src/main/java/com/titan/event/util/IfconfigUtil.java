package com.titan.event.util;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStreamReader;
import java.io.LineNumberReader;

/**
 * Created by Administrator on 16-12-16.
 */
public class IfconfigUtil {
    /**
     * 二进制转换十进制掩码
     *
     */
    public static String getNetmask(String s) {
        StringBuffer string = new StringBuffer();
        StringBuffer netmask = new StringBuffer();
        int z = Integer.parseInt(s);
        int o = 32-z;
        for(int i=0;i<z;i++){
            string.append(1);
        }
        for(int i=0;i<o;i++){
            string.append(0);
        }
        String net = string.toString();
        Integer.valueOf((String) net.subSequence(0,8),2).toString();
        netmask.append(Integer.valueOf((String) net.subSequence(0,8),2).toString()+".");
        netmask.append(Integer.valueOf((String) net.subSequence(8,16),2).toString()+".");
        netmask.append(Integer.valueOf((String) net.subSequence(16,24),2).toString()+".");
        netmask.append(Integer.valueOf((String) net.subSequence(24,32), 2).toString());
        return netmask.toString();
    }

    //获取ip地址
    public String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if(ip.equals("0:0:0:0:0:0:0:1")){
            ip = "127.0.0.1";
        }
        return ip;
    }

    public String getClientIpv6Address(HttpServletRequest request) {
        // 尝试从X-Forwarded-For头部获取IP，适用于经过代理或负载均衡器的情况
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && !ip.isEmpty()) {
            // 如果X-Forwarded-For存在多个IP，通常第一个是客户端的IP
            String[] ips = ip.split(",");
            if (ips.length > 0) {
                // 验证是否为IPv6地址（简单检查）
                if (ips[0].contains(":")) {
                    return ips[0].trim();
                }
            }
        }

        // 如果X-Forwarded-For头部没有合适的IPv6地址，则尝试从直接连接中获取
        String remoteAddr = request.getRemoteAddr();
        // 验证是否为IPv6地址（简单检查）
        if (remoteAddr.contains(":")) {
            return remoteAddr;
        }

        // 如果没有找到IPv6地址，返回null或默认值
        return null; // 或者你可以返回一个表示“未知”的字符串
    }

    //获取MAC地址的方法
    public String getMACAddress(String ip)throws Exception{
        String mac = "00:00:00:00:00:00";
        try {
            Process p = Runtime.getRuntime().exec("arp -n");
            InputStreamReader ir = new InputStreamReader(p.getInputStream());
            LineNumberReader input = new LineNumberReader(ir);
            p.waitFor();
            boolean flag = true;
            while(flag) {
                String str = input.readLine();
                if (str != null) {
                    String[] temp = str.trim().split("\\s+");
                    if (temp[0].equals(ip)) {
                        mac = temp[2];
                        break;
                    }
                } else{
                    flag = false;
                }
            }
            p.destroy();
        } catch (Exception e) {
            e.printStackTrace(System.out);
        }
        return mac;
    }


}