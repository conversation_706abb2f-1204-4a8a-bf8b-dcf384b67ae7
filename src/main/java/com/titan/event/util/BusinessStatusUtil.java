package com.titan.event.util;

import com.titan.event.entity.Venue;
import org.springframework.util.StringUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 场馆营业状态工具类
 */
public class BusinessStatusUtil {

    /**
     * 判断场馆的营业状态
     * 
     * @param venue 场馆信息
     * @return 营业状态(0:休息中, 1:营业中)
     */
    public static Integer getBusinessStatus(Venue venue) {
        if (venue == null) {
            return 0;
        }
        
        // 1. 如果是手动设置的营业状态，直接返回设置的值
        if (venue.getBusinessStatusManual() != null && venue.getBusinessStatusManual() == 1) {
            return venue.getBusinessStatus();
        }
        
        // 2. 如果是自动判断，根据营业时间判断
        if (StringUtils.hasText(venue.getBusinessStartTime()) && StringUtils.hasText(venue.getBusinessEndTime())) {
            try {
                // 获取当前时间
                LocalTime now = LocalTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                
                // 解析营业开始和结束时间
                LocalTime startTime = LocalTime.parse(venue.getBusinessStartTime(), formatter);
                LocalTime endTime = LocalTime.parse(venue.getBusinessEndTime(), formatter);
                
                // 判断当前时间是否在营业时间范围内
                if (isTimeInRange(now, startTime, endTime)) {
                    return 1; // 营业中
                } else {
                    return 0; // 休息中
                }
            } catch (Exception e) {
                // 时间格式错误或者其他异常，默认返回0
                return 0;
            }
        }
        
        // 3. 没有设置营业时间，默认返回0
        return 0;
    }
    
    /**
     * 判断当前时间是否在指定范围内
     * 
     * @param time 当前时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否在范围内
     */
    private static boolean isTimeInRange(LocalTime time, LocalTime startTime, LocalTime endTime) {
        // 如果结束时间小于开始时间，说明跨天了
        if (endTime.isBefore(startTime)) {
            return !time.isAfter(endTime) || !time.isBefore(startTime);
        } else {
            return !time.isBefore(startTime) && !time.isAfter(endTime);
        }
    }
} 