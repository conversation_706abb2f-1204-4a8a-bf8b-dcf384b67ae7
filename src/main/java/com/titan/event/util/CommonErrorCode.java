package com.titan.event.util;

/**
 * <p>公共异常码定义</p>
 * Created by of628-we<PERSON><PERSON> on 2018-06-21-下午2:58.
 */
public final class CommonErrorCode {

    private CommonErrorCode() {
    }

    /**
     * 指定异常，不走国际化，异常信息由B2bRuntimeException字段result设定
     */
    public final static String SPECIFIED = "K-999999";

    /**
     * 重复提交
     */
    public final static String REPEAT_REQUEST = "K-999997";

    /**
     * 重复提交
     */
    public final static String INCLUDE_BAD_WORD = "K-999996";

    /**
     * 操作成功
     */
    public final static String SUCCESSFUL = "K-000000";

    /**
     * 操作失败
     */
    public final static String FAILED = "K-000001";


    /**
     * 参数错误
     */
    public final static String PARAMETER_ERROR = "K-000009";

    /**
     * 验证码错误
     */
    public final static String VERIFICATION_CODE_ERROR = "K-000010";

    /**
     * 上传文件失败
     */
    public final static String UPLOAD_FILE_ERROR = "K-000011";

    /**
     * 发送失败
     */
    public final static String SEND_FAILURE = "K-000012";

    /**
     * 您没有权限访问
     */
    public final static String PERMISSION_DENIED = "K-000014";


    /**
     * 非法字符
     */
    public final static String ILLEGAL_CHARACTER = "K-000017";


    /**
     * 无法调用远程服务
     */
    public final static String THIRD_SERVICE_ERROR = "K-000021";


    /**
     * 阿里云连接异常
     */
    public final static String ALIYUN_CONNECT_ERROR = "K-090702";

    /**
     * 阿里云上传图片失败
     */
    public final static String ALIYUN_IMG_UPLOAD_ERROR = "K-090703";

    /**
     * 操作频繁，请稍后重试！
     */
    public final static String FREQUENT_OPERATION = "K-000023";

    /**
     * 发送短信失败
     */
    public final static String SEND_SMS_ERROR = "K-000025";

    /**
     * 没有此用户！
     */
    public final static String NO_USER = "K-000024";

    /**
     * 订阅******************************************************************************************
     */

    /**
     * 没有可恢复的商品
     */
    public final static String NO_RESTORABLE_PRODUCT_EXCEPTION = "K-000025";

    /**
     * 会员资格已过期
     */
    public final static String MEMBERSHIP_EXPIRED_EXCEPTION = "K-000026";

    /**
     * 恢复失败
     */
    public final static String RESTORE_FAILURE_MESSAGE = "K-000027";

    /**
     * 营销******************************************************************************************
     */
    public final static String STAR5_DUP = "K-050001";

    /**
     * 邀请******************************************************************************************
     */

    public final static String INVITE_SELF = "K-060001";

    public final static String INVITE_CODE = "K-060002";

    public final static String INVITE_DUP = "K-060003";

    /**
     * 签到******************************************************************************************
     */

    public final static String SIGN_DUP = "K-070001";

    /**
     * 档案******************************************************************************************
     */

    /**
     * 超过最大档案数量上限
     */
    public final static String MAX_PROFILE_LIMIT_EXCEEDED_EXCEPTION = "K-010001";

    /**
     * 未找到档案信息
     */
    public final static String INVALID_NOT_FOUND_PROFILE = "K-010002";

    /**
     * 档案性别不正确
     */
    public final static String INVALID_PROFILE_GENDER = "K-010003";

    /**
     * 档案关系不正确
     */
    public final static String INVALID_PROFILE_RELATIONSHIP = "K-010004";

    /**
     * 运势年不正确
     */
    public final static String INVALID_YUNSHI_YEAR = "K-010004";


}
