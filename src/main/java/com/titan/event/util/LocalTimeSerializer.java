package com.titan.event.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:36
 */
public class LocalTimeSerializer extends JsonSerializer<LocalTime> {
    private static final String DATE_TIME_FORMATTER_MILLI_PATTERN = "HH:mm";
    private static DateTimeFormatter DATE_TIME_FORMATTER_MILLI = DateTimeFormatter.ofPattern("HH:mm");

    public LocalTimeSerializer() {
    }

    public void serialize(LocalTime value, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        if (value == null) {
            jgen.writeNull();
        } else {
            jgen.writeString(DATE_TIME_FORMATTER_MILLI.format(value));
        }

    }
}
