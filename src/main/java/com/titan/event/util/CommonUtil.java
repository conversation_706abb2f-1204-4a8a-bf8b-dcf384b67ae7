package com.titan.event.util;

import com.titan.event.config.JwtProperties;
import com.titan.event.util.base.Operator;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * BFF公共工具类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/12/29.
 */
@Component
@Slf4j
public final class CommonUtil {

    @Value("${jwt.secret-key}")
    private String key;

    @Autowired
    private JwtProperties jwtProperties;

    /**
     * 文件名 正则字符串
     * 文件名支持的字符串：字母数字中文.-_()（） 除此之外的字符将被删除
     */
    private static String FILE_NAME_REGEX = "[^A-Za-z\\.\\(\\)\\-（）\\_0-9\\u4e00-\\u9fa5]";

    /**
     * 获取当前登录编号
     *
     * @return
     */
    public String getOperatorId() {
        return getOperator().getUserId();
    }

    /**
     * 获取当前用户的openId
     *
     * @return
     */
    public String getOpenId() {
        Claims claims = (Claims) (HttpUtil.getRequest().getAttribute("claims"));
        if (claims == null) {
            return null;
        }
        return String.valueOf(claims.get("openId"));
    }

    /**
     * 正则表达式：验证手机号 匹配最新的正则表达式
     */
    public String REGEX_MOBILE = "^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\\d{8}$";

    /**
     * 正则表达式：验证邮箱
     */
    public String REGEX_EMAIL = "^([a-z0-9A-Z_]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    /**
     * 正则表达式：银行卡
     */
    public String REGEX_BANK_CARD = "^([1-9]{1})(\\d{14}|\\d{15}|\\d{16}|\\d{18})$";

    /**
     * 图片格式后缀大全
     */
    public static String[] IMAGE_SUFFIX = new String[]{"bmp", "jpg", "jpeg", "heif", "png", "tif", "gif", "pcx", "tga",
            "exif", "fpx", "svg", "psd", "cdr", "pcd", "dxf", "ufo", "eps", "ai", "raw", "WMF", "webp"};


    /**
     * 常见视频格式后缀大全
     */
    public static String[] VIDEO_SUFFIX = new String[]{"avi", "wmv", "rm", "rmvb", "mpeg1", "mpeg2", "mpeg4(mp4)",
            "3gp", "asf", "swf"
            , "vob", "dat", "mov", "m4v", "flv", "f4v", "mkv", "mts", "ts", "imv", "amv", "xv", "qsv"};



    /**
     * 获取当前登录对象
     *
     * @return
     */
    public Operator getOperator() {
        Claims claims = (Claims) (HttpUtil.getRequest().getAttribute("claims"));
        if (claims == null) {
            return new Operator();
        }
        return Operator.builder()
                .account(ObjectUtils.toString(claims.get("phone")))
                .ip(String.valueOf(claims.get("ip")))
                .userId(String.valueOf(claims.get("userId")))
                .terminalToken(claims.get("terminalToken")==null?String.valueOf(claims.get("customerId")):String.valueOf(claims.get("terminalToken")))
//                .adminId(ObjectUtils.toString(claims.get("adminId")))
//                .name(String.valueOf(claims.get("customerName")))
//                .firstLogin(claims.get("firstLogin")==null?Boolean.FALSE:Boolean.valueOf(Objects.toString(claims.get("firstLogin"))))
                //生成用户token，用于同一用户不同终端登陆区别
                //.terminalToken(String.valueOf(claims.get("terminalToken")))
                .build();
    }

    /**
     * 获取用户的登陆的终端token
     * @return
     */
    public String getTerminalToken(){
        String terminalToken =  getOperator().getTerminalToken();
        if(StringUtils.isEmpty(terminalToken)||"null".equals(terminalToken)){
            terminalToken = getOperator().getUserId();
        }
        return  terminalToken;

    }
    /**
     * 获取当前登录对象(JWT忽略的时候使用)
     *
     * @return
     */
    public Operator getUserInfo() {
        Claims claims = (Claims) (HttpUtil.getRequest().getAttribute("claims"));
        if (claims == null) {
            //从header中直接获取token解析 —— 解决需要在被过滤的请求中获取当前登录人信息
            String token = this.getToken(HttpUtil.getRequest());
            if(StringUtils.isNotBlank(token)){
                claims =  this.validate(token);
            }else{
                return new Operator();
            }
        }
        return Operator.builder()
                .account(ObjectUtils.toString(claims.get("customerAccount")))
                .adminId(ObjectUtils.toString(claims.get("adminId")))
                .ip(String.valueOf(claims.get("ip")))
                .name(String.valueOf(claims.get("customerName")))
                .userId(String.valueOf(claims.get("customerId")))
                .build();
    }

    /**
     * 获取jwtToken
     * @return
     */
    private String getToken(HttpServletRequest request) {

        String jwtHeaderKey = StringUtils.isNotBlank(jwtProperties.getJwtHeaderKey()) ? jwtProperties.getJwtHeaderKey
                () : "Authorization";
        String jwtHeaderPrefix = StringUtils.isNotBlank(jwtProperties.getJwtHeaderPrefix()) ? jwtProperties
                .getJwtHeaderPrefix() : "Bearer ";

        String authHeader = request.getHeader(jwtHeaderKey);

        //当token失效,直接返回失败
        if(authHeader != null && authHeader.length() > 16){
            return authHeader.substring(jwtHeaderPrefix.length());
        }
        return null;
    }

    /**
     * 验证转换为Claims
     * @param token
     * @return
     */
    private Claims validate(String token) {
        try {
            final Claims claims = Jwts.parser().setSigningKey(key).parseClaimsJws(token).getBody();
            log.debug("JwtFilter out ['Authorization success']");
            return claims;
        } catch (final SignatureException | MalformedJwtException | ExpiredJwtException e) {
            log.info("JwtFilter exception, exMsg:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 判断文件名是否带盘符，重新处理
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName){
        //判断是否带有盘符信息
        // Check for Unix-style path
        int unixSep = fileName.lastIndexOf('/');
        // Check for Windows-style path
        int winSep = fileName.lastIndexOf('\\');
        // Cut off at latest possible point
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1)  {
            // Any sort of path separator found...
            fileName = fileName.substring(pos + 1);
        }
        //替换上传文件名字的特殊字符
        fileName = fileName.replace("=","").replace(",","").replace("&","")
                .replace("#", "").replace("“", "").replace("”", "");
        //替换上传文件名字中的空格
        fileName=fileName.replaceAll("\\s","");
        //update-beign-author:taoyan date:20220302 for: /issues/3381 online 在线表单 使用文件组件时，上传文件名中含%，下载异常
        fileName = fileName.replaceAll(FILE_NAME_REGEX, "");
        //update-end-author:taoyan date:20220302 for: /issues/3381 online 在线表单 使用文件组件时，上传文件名中含%，下载异常
        return fileName;
    }



}
