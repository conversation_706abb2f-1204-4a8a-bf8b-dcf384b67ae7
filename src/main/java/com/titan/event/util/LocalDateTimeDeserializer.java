package com.titan.event.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:37
 */
public class LocalDateTimeDeserializer  extends JsonDeserializer<LocalDateTime> {
    private static final String DATE_TIME_FORMATTER_MILLI_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_TIME_FORMATTER_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static DateTimeFormatter DATE_TIME_FORMATTER_MILLI = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public LocalDateTimeDeserializer() {
    }

    public LocalDateTime deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonToken t = jp.getCurrentToken();
        if (t == JsonToken.VALUE_STRING) {
            String text = jp.getText();
            if (StringUtils.isBlank(text)) {
                return null;
            }

            String str = text.trim();
            if ("yyyy-MM-dd HH:mm:ss".length() == str.length()) {
                return LocalDateTime.parse(str, DATE_TIME_FORMATTER);
            }

            if ("yyyy-MM-dd HH:mm:ss.SSS".length() == str.length()) {
                return LocalDateTime.parse(str, DATE_TIME_FORMATTER_MILLI);
            }
        }

        if (t == JsonToken.VALUE_NUMBER_INT) {
            return (new Timestamp(jp.getLongValue())).toLocalDateTime();
        } else {
            throw ctxt.mappingException(this.handledType());
        }
    }
}
