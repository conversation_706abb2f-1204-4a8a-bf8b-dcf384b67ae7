package com.titan.event.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/3
 */
public class DtUtil {
    private DtUtil() {
    }

    public static final String NORM_WEEK_PATTERN = "yyyyww";

    public static boolean isExpiried(Date date) {
        if (date == null) {
            return false;
        }
        Date now = new Date();
        return DateUtil.compare(date, now) <= 0;
    }

    public static boolean isFuture(Date date) {
        if (date == null) {
            return false;
        }
        Date now = new Date();
        return DateUtil.compare(date, now) > 0;
    }

    public static final Date getMonday(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        Date mondayDate = calendar.getTime();
        mondayDate = DateUtil.beginOfDay(mondayDate);
        return mondayDate;
    }

    public static final boolean isMonday(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int n = calendar.get(Calendar.DAY_OF_WEEK);
        return n == Calendar.MONDAY;
    }

    public static final List<String> dateBetween(Date from, Date to) {
        List<String> ret = new ArrayList<>();
        DateRange range = DateUtil.range(from, to, DateField.DAY_OF_MONTH);
        range.forEach(dt -> ret.add(dt.toDateStr()));
        return ret;
    }

    public static final List<String> dateListOf(Date from, int dayCnt) {
        Date to = DateUtil.offsetDay(from, dayCnt - 1);
        return dateBetween(from, to);
    }
}
