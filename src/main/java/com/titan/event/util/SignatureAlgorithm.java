package com.titan.event.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Arrays;

public class SignatureAlgorithm {

    private static final String KEY = "MKT_aMPCKKX6ZDjDxnCU";

    public String generateSignature(String timestamp, String nonce) {
        // 按照字符串比较顺序从小到大排序
        String[] params = new String[]{timestamp, nonce, KEY};
        Arrays.sort(params);

        // 连接参数
        StringBuilder sb = new StringBuilder();
        for (String param : params) {
            sb.append(param);
        }

        return calculateMD5(sb.toString());
    }

    private static String calculateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }

    // 方法1：获取当前时间戳（秒级）
    public long getCurrentTimestampInSeconds() {
        return Instant.now().getEpochSecond();
    }

    private final SecureRandom secureRandom = new SecureRandom();

    // 方法2：生成随机nonce字符串，这里假设长度为10
    public String generateNonce() {
        StringBuilder nonceBuilder = new StringBuilder(10);
        for (int i = 0; i < 10; i++) {
            nonceBuilder.append(Integer.toHexString(secureRandom.nextInt(16)));
        }
        return nonceBuilder.toString().toUpperCase();
    }

        public static void main(String[] args) {
//            String timestamp = String.valueOf(getCurrentTimestampInSeconds());
//            String nonce = generateNonce();
//            System.out.println(timestamp);
//            System.out.println(nonce.toLowerCase());
//            String signature = SignatureAlgorithm.generateSignature(timestamp, nonce);
//            System.out.println(signature); // 输出：A9CDEB2F8E066788868888A8A8A8A8A8
        }

}
