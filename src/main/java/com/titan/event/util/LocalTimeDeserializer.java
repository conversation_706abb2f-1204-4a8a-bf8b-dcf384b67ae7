package com.titan.event.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> lhx
 * @date 2023/10/25 11:37
 */
public class LocalTimeDeserializer extends JsonDeserializer<LocalTime> {
    private static final String DATE_TIME_FORMATTER_MILLI_PATTERN = "HH:mm";
    private static final String DATE_TIME_FORMATTER_PATTERN = "HH:mm";
    private static DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static DateTimeFormatter DATE_TIME_FORMATTER_MILLI = DateTimeFormatter.ofPattern("HH:mm");

    public LocalTimeDeserializer() {
    }

    public LocalTime deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonToken t = jp.getCurrentToken();
        if (t == JsonToken.VALUE_STRING) {
            String text = jp.getText();
            if (StringUtils.isBlank(text)) {
                return null;
            }

            String str = text.trim();
            if ("HH:mm".length() == str.length()) {
                return LocalTime.parse(str, DATE_TIME_FORMATTER);
            }

            if ("HH:mm".length() == str.length()) {
                return LocalTime.parse(str, DATE_TIME_FORMATTER_MILLI);
            }
        }

        if (t == JsonToken.VALUE_NUMBER_INT) {
            return (new Timestamp(jp.getLongValue())).toLocalDateTime().toLocalTime();
        } else {
            throw ctxt.mappingException(this.handledType());
        }
    }
}
