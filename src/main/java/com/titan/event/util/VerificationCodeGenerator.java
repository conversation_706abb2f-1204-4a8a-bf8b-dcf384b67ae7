package com.titan.event.util;

import com.titan.event.mapper.VenueVerificationCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;

/**
 * 核销码生成工具类
 */
@Component
public class VerificationCodeGenerator {

    private static final SecureRandom RANDOM = new SecureRandom();
    
    @Autowired
    private VenueVerificationCodeMapper verificationCodeMapper;
    
    /**
     * 生成12位数字核销码
     * 包含重复性校验，确保生成的核销码在系统中是唯一的
     * 
     * @return 12位数字核销码
     */
    public String generateVerificationCode() {
        String code;
        // 循环生成直到找到一个未被使用的核销码
        do {
            // 生成12位随机数字
            StringBuilder codeBuilder = new StringBuilder(12);
            for (int i = 0; i < 12; i++) {
                codeBuilder.append(RANDOM.nextInt(10));
            }
            code = codeBuilder.toString();
            
            // 查询数据库中是否已存在该核销码
        } while (verificationCodeMapper.findByVerificationCode(code).isPresent());
        
        return code;
    }
} 