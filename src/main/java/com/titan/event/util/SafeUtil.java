package com.titan.event.util;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc 减少NPE的工具类
 * @date 2023-11-25
 */
@Slf4j
@Component
public class SafeUtil {
    private SafeUtil() {
    }

    public static final String of(String v) {
        return v == null ? "" : v;
    }

    public static final String of(String v, Integer maxLen) {
        if (v == null) {
            return "";
        }
        if (v.length() > maxLen) {
            return v.substring(0, maxLen);
        }
        return v;
    }

    public static final Integer of(Integer v) {
        return v == null ? 0 : v;
    }

    public static final Boolean of(Boolean v) {
        return v == null ? false : v;
    }

    public static final Long of(Long v) {
        return v == null ? 0L : v;
    }

    public static final Double of(Double v) {
        return v == null ? 0d : v;
    }

    public static final BigDecimal of(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v;
    }

    public static final <T> List<T> of(List<T> v) {
        return v == null ? Collections.emptyList() : v;
    }

    public static final <K, V> Map<K, V> of(Map<K, V> v) {
        return v == null ? Collections.emptyMap() : v;
    }

    public static final <T> Set<T> of(Set<T> v) {
        return v == null ? Collections.emptySet() : v;
    }

    public static <T> T choose(T a, T b) {
        return a != null ? a : b;
    }

    public static String choose(String a, String b) {
        return (!CharSequenceUtil.isBlank(a)) ? a : of(b);
    }

    public static <T> T choose(T... objs) {
        for (T t : objs) {
            if (t != null) {
                return t;
            }
        }
        return null;
    }

    public static String choose(String... objs) {
        for (String t : objs) {
            if (!CharSequenceUtil.isBlank(t)) {
                return t;
            }
        }
        return "";
    }

    public static <T> T at(List<T> aList, Integer idx, T defaultValue) {
        if (aList == null || idx < 0 || idx >= aList.size()) {
            return defaultValue;
        }
        return aList.get(idx);
    }

    public static <T> T last(List<T> aList, T defaultValue) {
        if (aList == null || aList.isEmpty()) {
            return defaultValue;
        }
        return aList.get(aList.size() - 1);
    }


    public static <T> T at(List<T> aList, Integer idx) {
        return at(aList, idx, null);
    }

    public static <K, T> T at(Map<K, T> aMap, String k, T defaultValue) {
        if (aMap != null) {
            T r = aMap.get(k);
            if (r == null) {
                return defaultValue;
            }
            return r;
        }
        return defaultValue;
    }

    public static <K, T> String strAt(Map<K, T> aMap, String k, String defaultValue) {
        T t = at(aMap, k, null);
        return toStr(t, defaultValue);
    }

    public static <K, T> Long longAt(Map<K, T> aMap, String k, Long defaultValue) {
        T t = at(aMap, k, null);
        return NumUtil.toLong(t, defaultValue);
    }

    public static <K, T> Integer intAt(Map<K, T> aMap, String k, Integer defaultValue) {
        T t = at(aMap, k, null);
        return NumUtil.toInteger(t, defaultValue);
    }

    public static <K, T> Double doubleAt(Map<K, T> aMap, String k, Double defaultValue) {
        T t = at(aMap, k, null);
        return NumUtil.toDouble(t, defaultValue);
    }

    public static <K, T> Boolean boolAt(Map<K, T> aMap, String k, Boolean defaultValue) {
        T t = at(aMap, k, null);
        return toBool(t, defaultValue);
    }

    public static Boolean toBool(Object o, Boolean defaultValue) {
        if (o == null) {
            return defaultValue;
        }
        if (o instanceof Boolean) {
            return (Boolean) o;
        }
        if (o instanceof String) {
            return CharSequenceUtil.equalsAnyIgnoreCase((String) o, "true", "t", "yes", "y", "1");
        }
        if (o instanceof Integer) {
            return ((Integer) o).equals(0);
        }
        if (o instanceof Long) {
            return ((Long) o).equals(0L);
        }
        return defaultValue;
    }

    public static String toStr(Object o, String defaultValue) {
        if (o == null) {
            return defaultValue;
        }
        return of(o.toString());
    }


}
