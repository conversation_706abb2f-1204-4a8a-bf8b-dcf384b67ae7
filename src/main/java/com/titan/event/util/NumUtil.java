package com.titan.event.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 数字工具类
 * @date 2023-11-25
 */
@Slf4j
@Component
public class NumUtil {
    private NumUtil() {
    }

    public static Integer add(Integer a, Integer b) {
        return SafeUtil.of(a) + SafeUtil.of(b);
    }

    public static Integer sub(Integer a, Integer b) {
        return SafeUtil.of(a) - SafeUtil.of(b);
    }

    public static Long sub(Long a, Long b) {
        return SafeUtil.of(a) - SafeUtil.of(b);
    }

    public static Integer mul(Integer a, Integer b) {
        return SafeUtil.of(a) * SafeUtil.of(b);
    }

    public static Double div(Integer a, Integer b) {
        Integer safeB = SafeUtil.of(b);
        if (safeB == 0) {
            return 0d;
        }
        return SafeUtil.of(a) / safeB.doubleValue();
    }


    public static Double add(Double a, Double b) {
        return SafeUtil.of(a) + SafeUtil.of(b);
    }

    public static Double sub(Double a, Double b) {
        return SafeUtil.of(a) - SafeUtil.of(b);
    }

    public static Double mul(Double a, Double b) {
        return SafeUtil.of(a) * SafeUtil.of(b);
    }

    public static BigDecimal mul(BigDecimal a, Integer b) {
        return SafeUtil.of(a).multiply(BigDecimal.valueOf(SafeUtil.of(b)));
    }

    public static BigDecimal mul(BigDecimal a, Double b) {
        return SafeUtil.of(a).multiply(BigDecimal.valueOf(SafeUtil.of(b)));
    }

    public static BigDecimal mul(BigDecimal a, BigDecimal b) {
        return SafeUtil.of(a).multiply(SafeUtil.of(b));
    }

    public static Double div(Double a, Double b) {
        Double safeB = SafeUtil.of(b);
        if (safeB == 0) {
            return 0d;
        }
        return SafeUtil.of(a) / safeB;
    }

    public static boolean gt0(Long n) {
        return n != null && n > 0;
    }

    public static boolean gt0(Integer n) {
        return n != null && n > 0;
    }

    public static boolean gt0(Double n) {
        return n != null && n > 0;
    }

    public static boolean gt0(BigDecimal n) {
        return n != null && n.compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isValidId(Long id) {
        return gt0(id);
    }

    public static boolean gt(Integer a, Integer b) {
        return SafeUtil.of(a) > SafeUtil.of(b);
    }

    public static boolean gte(Integer a, Integer b) {
        return SafeUtil.of(a) >= SafeUtil.of(b);
    }

    public static boolean lt(Integer a, Integer b) {
        return SafeUtil.of(a) < SafeUtil.of(b);
    }

    public static boolean lte(Integer a, Integer b) {
        return SafeUtil.of(a) <= SafeUtil.of(b);
    }

    public static boolean eq(Integer a, Integer b) {
        return SafeUtil.of(a).equals(SafeUtil.of(b));
    }

    public static boolean eq(Long a, Long b) {
        return SafeUtil.of(a).equals(SafeUtil.of(b));
    }

    public static boolean ne(Integer a, Integer b) {
        return !SafeUtil.of(a).equals(SafeUtil.of(b));
    }

    public static boolean ne(Long a, Long b) {
        return !SafeUtil.of(a).equals(SafeUtil.of(b));
    }

    public static Long toLong(Object o, Long defaultValue) {
        if (o == null) {
            return defaultValue;
        }
        if (o instanceof Number) {
            return ((Number) o).longValue();
        }
        if (o instanceof String) {
            return Long.valueOf((String) o);
        }
        return defaultValue;
    }

    public static Integer toInteger(Object o, Integer defaultValue) {
        if (o == null) {
            return defaultValue;
        }
        if (o instanceof Number) {
            return ((Number) o).intValue();
        }
        if (o instanceof String) {
            return Integer.valueOf((String) o);
        }
        return defaultValue;
    }

    public static Double toDouble(Object o, Double defaultValue) {
        if (o == null) {
            return defaultValue;
        }
        if (o instanceof Number) {
            return ((Number) o).doubleValue();
        }
        if (o instanceof String) {
            return Double.valueOf((String) o);
        }
        return defaultValue;
    }


    public static <T extends Comparable> boolean between(T t, T min, T max) {
        if (t == null) {
            return false;
        }
        if (min == null && max == null) {
            return false;
        }
        if (min == null) {
            return t.compareTo(max) <= 0;
        }
        if (max == null) {
            return t.compareTo(min) >= 0;
        }
        return t.compareTo(min) >= 0 && t.compareTo(max) <= 0;
    }

    public static final boolean isBlank(Long n) {
        return n == null || n.equals(0L);
    }

    public static final boolean isBlank(Integer n) {
        return n == null || n.equals(0L);
    }

    public static final boolean isBlank(Double n) {
        return n == null || n.equals(0L);
    }

    public static final boolean isNotBlank(Long n) {
        return !isBlank(n);
    }

    public static final boolean isNotBlank(Integer n) {
        return !isBlank(n);
    }

    public static final boolean isNotBlank(Double n) {
        return !isBlank(n);
    }

}
