package com.titan.event.job;

import com.titan.event.config.JobConfig;
import com.titan.event.constant.RedisKeyConstant;
import com.titan.event.service.impl.VenueOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * 微信支付超时订单处理定时任务
 * 
 * 注意：本定时任务作为超时订单处理的兜底机制
 * 系统采用了两种超时处理机制：
 * 1. 主动模式：订单创建时通过Redisson延迟队列安排定时取消，精确到秒级别
 * 2. 被动模式：本定时任务每分钟扫描一次数据库，处理延迟队列可能漏掉的订单
 * 
 * 这种双重保障机制确保了超时订单能够被及时处理，即使某一机制出现问题
 */
@Slf4j
@Component
//@Profile("prod")
public class WxPayTimeoutOrderJobHandler {

    @Autowired
    private VenueOrderServiceImpl venueOrderService;

    @Autowired
    private RedissonClient redissonClient;

    // 分布式锁键名
    private static final String TASK_LOCK_KEY = "wx:pay:timeout:job:lock";
    
    // 超时时间设置（分钟）
    private static final int ORDER_TIMEOUT_MINUTES = 30;

    /**
     * 每分钟执行一次的定时任务，处理微信支付超时订单
     * 
     * 作为兜底机制，处理以下情况：
     * 1. 延迟队列处理失败的订单
     * 2. 系统重启时可能丢失的延迟队列任务
     * 3. 延迟队列任务执行异常的订单
     */
    @Scheduled(fixedRate = 60 * 1000)  // 每60秒执行一次
    @Async("taskExecutor")
    @Transactional
    public void handleTimeoutOrders() {
        // 检查全局开关，如果禁用直接返回
        if (!JobConfig.isJobEnabled()) {
            log.debug("定时任务全局开关已关闭，跳过微信支付超时订单处理任务");
            return;
        }
        
        RLock taskLock = redissonClient.getLock(TASK_LOCK_KEY);
        try {
            // 尝试获取锁，等待5秒，持有锁60秒
            boolean isLocked = taskLock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("其他节点正在执行微信支付超时订单处理任务，跳过本次执行");
                return;
            }
            
            log.info("开始执行微信支付超时订单处理定时任务（兜底机制）");
            
            // 调用VenueOrderServiceImpl中的handleTimeoutOrders方法处理超时订单
            int processedCount = venueOrderService.handleTimeoutOrders(ORDER_TIMEOUT_MINUTES);
            
            log.info("微信支付超时订单处理定时任务执行完成，处理订单数：{}", processedCount);
            
        } catch (Exception e) {
            log.error("执行微信支付超时订单处理定时任务异常", e);
        } finally {
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }
} 