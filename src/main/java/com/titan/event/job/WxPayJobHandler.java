package com.titan.event.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.titan.event.annotation.ConditionalScheduled;
import com.titan.event.config.JobConfig;
import com.titan.event.constant.RedisKeyConstant;
import com.titan.event.entity.pay.PayOrder;
import com.titan.event.enums.*;
import com.titan.event.mapper.PayOrderMapper;
import com.titan.event.redis.RedisService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> lhx
 * @date 2023/10/23 10:30
 */
@Slf4j
@Component
@Profile("prod")
public class WxPayJobHandler {

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisService redisService;

    private static final String TASK_LOCK_KEY = "wx:pay:job:lock";

    @Scheduled(fixedRate = 1000 * 60 * 5)
    @Async("taskExecutor")
    @Transactional
    public void insertMessage() {
        // 检查全局开关，如果禁用直接返回
        if (!JobConfig.isJobEnabled()) {
            log.debug("定时任务全局开关已关闭，跳过微信支付订单处理任务");
            return;
        }
        
        RLock taskLock = redissonClient.getLock(TASK_LOCK_KEY);
        try {
            // 尝试获取锁，等待5秒，持有锁60秒
            boolean isLocked = taskLock.tryLock(5, 60, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("其他节点正在执行定时任务，跳过本次执行");
                return;
            }
            
            log.info("开始执行微信支付订单处理定时任务");
            // 分批处理，避免一次加载过多数据
            int pageSize = 100;
            int currentPage = 0;
            List<PayOrder> payOrderList;
            
            do {
                payOrderList = payOrderMapper.findExpireOrderWithPage(currentPage * pageSize, pageSize);
                if (payOrderList == null || payOrderList.isEmpty()) {
                    break;
                }
                
//                processPayOrders(payOrderList);

                // 手动清理，帮助GC
                payOrderList.clear();
                currentPage++;
                
                // 每处理一批后小暂停，避免资源竞争
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            } while (payOrderList.size() == pageSize);
            
        } catch (Exception e) {
            log.error("执行定时任务异常", e);
        } finally {
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }
    
}
