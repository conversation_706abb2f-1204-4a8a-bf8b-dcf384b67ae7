package com.titan.event.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * STS临时凭证返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel(value = "阿里云STS临时凭证响应体", description = "包含访问阿里云服务所需的临时安全凭证信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StsResponse implements Serializable{

    @ApiModelProperty(value = "安全令牌", notes = "用于临时访问阿里云资源的安全令牌", required = true, example = "CAIS+gF1q6Ft5B2yfSjIr5bPNuLgjo5U4qWFRmHBpjlfU8ofnrzujDz2IHFPeXtgBuAdsPU3lmtY7/oglqVoRoReREvCKM1565XuGf6z01GEPcn548DiPBk/QZD8Z7KYA5i+JYLCdJXY+beAQSJFVa1ILf3rIS5Tm1vHiMB1ZLRdMKJKJKhhCCJe1/IaxmRa/yHB5iUL9qzOzZjGn1CwMz65Mjt+yGjE2IASpPvN6bGqnKzMPjy+Lk1HfnEb5p8fLJ1QenfVpSbvJf+0JwCax0p6Qkj6OIBvehgojMELmAOLZh78vt7IuaiDKzg3cE6CUNdGxeOcvh0qDyQx7OcnZBEmSQsZgp0Ve+hzNpIVhLXD/2T5t0JZJzB8T7dU5F7inhzdLLU=")
    private String securityToken;

    @ApiModelProperty(value = "临时访问密钥ID", notes = "用于标识临时访问凭证的AccessKeyId", required = true, example = "STS.NTvNFGVRU6QkeApXfVrm7Jooo")
    private String AccessKeyId;

    @ApiModelProperty(value = "临时访问密钥", notes = "用于签名请求的临时AccessKeySecret", required = true, example = "CHRF4BRi8HBtY8jtwqpL1zcLo9QnAQ1rQi1fFnRHbUMR")
    private String AccessKeySecret;

    @ApiModelProperty(value = "过期时间", notes = "临时凭证的过期时间，ISO8601格式", required = true, example = "2023-12-31T15:59:59Z")
    private String Expiration;

}
