package com.titan.event.response.countryCode;

import com.titan.event.enums.DefaultFlag;
import com.titan.event.vo.countryCode.CountryCodeVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 赛事信息相应
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CountryCodeResponse implements Serializable{

    private List<CountryCodeVO> countryCode;

}
