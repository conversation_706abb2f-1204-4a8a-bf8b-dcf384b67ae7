package com.titan.event.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 订单创建响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "订单创建响应对象，包含下单成功后的订单标识信息")
public class CreateOrderResponse {

    @ApiModelProperty(value = "订单ID - 系统生成的唯一订单编号，用于后续查询和支付", example = "O2023102312345678901234", required = true)
    private String orderId;
    
    @ApiModelProperty(value = "场馆订单ID - 场馆订单在数据库中的主键ID", example = "123", required = true)
    private Long venueOrderId;

    @ApiModelProperty(value = "预支付ID - 微信支付生成的预支付ID，用于发起小程序支付，场地预订时为空", example = "wx123456789", required = false)
    private String prepayId;

}