package com.titan.event.response;

import com.titan.event.vo.UploadVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件上传返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel(value = "文件上传响应体", description = "包含上传文件后的信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadResponse implements Serializable{

    /**
     * 文件信息
     */
    @ApiModelProperty(value = "上传文件信息", notes = "包含上传文件的URL和文件名等详细信息", required = true)
    private UploadVO uploadInfo;

}
