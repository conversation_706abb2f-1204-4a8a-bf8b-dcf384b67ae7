package com.titan.event.response.venue;

import com.titan.event.vo.venue.VenueListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 场馆列表响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VenueListResponse", description = "场馆列表响应")
public class VenueListResponse implements Serializable {
    
    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数", example = "100", notes = "满足条件的总记录数")
    private Long total;
    
    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1", notes = "当前请求的页码")
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", example = "10", notes = "每页显示的记录数")
    private Integer pageSize;
    
    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数", example = "10", notes = "总页数")
    private Integer pages;
    
    /**
     * 场馆列表
     */
    @ApiModelProperty(value = "场馆列表", notes = "当前页的场馆列表数据")
    private List<VenueListVO> list;
} 