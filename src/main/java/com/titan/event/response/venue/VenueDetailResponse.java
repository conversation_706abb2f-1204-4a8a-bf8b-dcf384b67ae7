package com.titan.event.response.venue;

import com.titan.event.vo.venue.VenueDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 场馆详情响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VenueDetailResponse", description = "场馆详情响应")
public class VenueDetailResponse implements Serializable {
    
    /**
     * 场馆详情
     */
    @ApiModelProperty(value = "场馆详情", notes = "场馆的详细信息")
    private VenueDetailVO venue;
} 