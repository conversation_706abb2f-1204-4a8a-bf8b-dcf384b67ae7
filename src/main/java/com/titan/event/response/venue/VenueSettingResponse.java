package com.titan.event.response.venue;

import com.titan.event.vo.venue.VenueSettingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 场馆设置响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VenueSettingResponse", description = "场馆设置响应")
public class VenueSettingResponse implements Serializable {
    
    /**
     * 省份列表
     */
    @ApiModelProperty(value = "省份列表", notes = "包含可选择的省份信息")
    private List<VenueSettingVO.AreaVO> provinces;
} 