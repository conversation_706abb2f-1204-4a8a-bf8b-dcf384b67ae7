package com.titan.event.response.carouselchart;

import com.titan.event.vo.carouselchart.CarouselChartVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CarouselChartResponse implements Serializable{

    @ApiModelProperty(name = "轮播图信息")
    private List<CarouselChartVO> carouselCharts;

}
