package com.titan.event.response;

import com.titan.event.vo.UserInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 登录返回
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/11/28.
 */
@ApiModel(value = "登录返回响应体", description = "包含用户登录后的令牌及基本信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LoginResponse implements Serializable{

    /**
     * jwt验证token
     */
    @ApiModelProperty(value = "JWT验证令牌", notes = "用于接口授权验证的JWT令牌", required = true)
    private String token;

    /**
     * openId
     */
    @ApiModelProperty(value = "微信OpenID", notes = "用户在当前小程序下的唯一标识", required = false)
    private String openId;

    /**
     * uuid
     */
    @ApiModelProperty(value = "用户唯一标识", notes = "系统内用户的唯一标识UUID", required = true)
    private String uuid;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "用户手机号", notes = "已绑定的用户手机号码", required = false)
    private String phone;

    /**
     * 用户详细信息
     */
    @ApiModelProperty(value = "用户详细信息", notes = "包含用户的完整个人信息", required = true)
    private UserInfoVO userInfo;

    /**
     * 微信会话密钥
     */
    @ApiModelProperty(value = "微信会话密钥", notes = "用于解密微信加密数据的会话密钥", required = false)
    private String sessionKey;

}
