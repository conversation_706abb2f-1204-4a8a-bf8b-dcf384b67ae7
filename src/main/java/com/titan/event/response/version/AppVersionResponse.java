package com.titan.event.response.version;

import com.titan.event.vo.version.AppVersionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppVersionResponse implements Serializable{

    @ApiModelProperty(name = "文章信息")
    private List<AppVersionVO> versions;

}
