package com.titan.event.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 场馆订单支付响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "场馆订单支付响应对象")
public class VenueOrderPayResponse {

    @ApiModelProperty(value = "订单ID", example = "O2023102312345678901234", required = true)
    private String orderId;
    
    @ApiModelProperty(value = "支付方式：1-微信支付，2-储值卡支付", example = "1", required = true)
    private Integer payType;
    
    @ApiModelProperty(value = "支付状态：SUCCESS-支付成功，NOTPAY-未支付，PAYERROR-支付失败", example = "SUCCESS", required = true)
    private String payStatus;
    
    @ApiModelProperty(value = "支付金额", example = "180.00", required = true)
    private BigDecimal payAmount;
    
    @ApiModelProperty(value = "预支付ID", example = "wx123456789", notes = "微信支付时返回，用于发起小程序支付")
    private String prepayId;
    
    @ApiModelProperty(value = "支付时间", example = "2023-10-23 14:30:45")
    private LocalDateTime payTime;
    
    @ApiModelProperty(value = "交易流水号", example = "4200001234202310231234567890", notes = "支付成功后的交易流水号")
    private String transactionId;
    
    @ApiModelProperty(value = "储值卡余额", example = "50.00", notes = "储值卡支付时返回支付后的余额")
    private BigDecimal cardBalance;
}
