package com.titan.event.init;

import com.alibaba.fastjson.JSONObject;
import com.titan.event.constant.RedisKeyConstant;
import com.titan.event.mapper.*;
import com.titan.event.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 初始化数据
 * @version: 1.0
 */
@Slf4j
@Order(1)
@Component
public class InitRunner implements CommandLineRunner {

	@Autowired
	private RedisService redisService;

	@Value("${init}")
	private boolean init;

	@Override
	public void run(String... args) throws Exception {
		log.info("=======系统初始化======="+init);
		if(init){

		}
	}
}
