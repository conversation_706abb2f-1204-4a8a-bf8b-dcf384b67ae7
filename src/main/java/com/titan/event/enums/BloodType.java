package com.titan.event.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnumProperty;

/**
 * 血型枚举
 * <AUTHOR>
 */
public enum BloodType {
    @ApiEnumProperty("A型")
    A(0),
    @ApiEnumProperty("B型")
    B(1),
    @ApiEnumProperty("AB型")
    AB(2),
    @ApiEnumProperty("O型")
    O(3),
    @ApiEnumProperty("Rh型")
    RH(4),
    @ApiEnumProperty("不清楚")
    UNKNOWN(5);

    private int type;

    BloodType(int type){
        this.type = type;
    }

    @JsonValue
    public int toValue() {
        return this.type;
    }

    public static BloodType fromValue(int value) {
        for (BloodType source : BloodType.values()) {
            if (source.type == value) {
                return source;
            }
        }
        throw new IllegalArgumentException("No enum constant with value " + value + " found.");
    }
}
