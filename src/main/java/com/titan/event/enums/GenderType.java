package com.titan.event.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnum;
import com.titan.event.annotation.ApiEnumProperty;

/**
 * 性别类型 0男 1女 2保密
 * <AUTHOR>
 */
@ApiEnum
public enum GenderType {

    @ApiEnumProperty("男")
    MALE("男"),

    @ApiEnumProperty("女")
    FEMALE("女");

    private static final GenderType[] VALUES = values();

    private String value;

    GenderType(String value) {
        this.value = value;
    }

    public GenderType fromValue(int value) {
        return values()[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
