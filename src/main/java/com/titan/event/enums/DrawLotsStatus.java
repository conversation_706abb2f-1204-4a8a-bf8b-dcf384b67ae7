package com.titan.event.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnumProperty;

/**
 * <AUTHOR>
 */

public enum DrawLotsStatus {
    @ApiEnumProperty("0:已中签")
    SELECTED_DRAWN,
    @ApiEnumProperty("1:待抽签")
    PENDING_DRAWN,
    @ApiEnumProperty("2:未中签")
    NOT_SELECTED_DRAWN;

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
