package com.titan.event.enums;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnumProperty;
import com.titan.event.util.SpringContextUtil;

import java.util.Date;

/**
 * <AUTHOR>
 */

public enum PublishStatus {
    @ApiEnumProperty("0:已发布")
    PUBLISHED,
    @ApiEnumProperty("1:未发布")
    NOT_PUBLISHED;

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
