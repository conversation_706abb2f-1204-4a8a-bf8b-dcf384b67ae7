package com.titan.event.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnumProperty;

/**
 * <AUTHOR>
 */

public enum RefundStatus {
    @ApiEnumProperty("0:退款失败")
    REFUND_FAIL,
    @ApiEnumProperty("1:已退款")
    REFUND,
    @ApiEnumProperty("2:退款处理中")
    REFUND_PROCESSING;

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
