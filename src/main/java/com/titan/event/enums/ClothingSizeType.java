package com.titan.event.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnumProperty;

/**
 * 衣服尺码枚举
 * <AUTHOR>
 */
public enum ClothingSizeType {
    @ApiEnumProperty("XS（150-155）")
    XS(0),
    @ApiEnumProperty("S（155-160）")
    S(1),
    @ApiEnumProperty("M（160-165）")
    M(2),
    @ApiEnumProperty("L（165-170）")
    L(3),
    @ApiEnumProperty("XL（170-175）")
    XL(4),
    @ApiEnumProperty("2XL（175-180）")
    XXL(5),
    @ApiEnumProperty("3XL（180-185）")
    XXXL(6),
    @ApiEnumProperty("4XL（185-190）")
    XXXXL(7);

    private int type;

    ClothingSizeType(int type){
        this.type = type;
    }

    @JsonValue
    public int toValue() {
        return this.type;
    }

    public static ClothingSizeType fromValue(int value) {
        for (ClothingSizeType source : ClothingSizeType.values()) {
            if (source.type == value) {
                return source;
            }
        }
        throw new IllegalArgumentException("No enum constant with value " + value + " found.");
    }
}
