package com.titan.event.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.titan.event.annotation.ApiEnum;
import com.titan.event.annotation.ApiEnumProperty;

/**
 * 证件类型 0二代身份证 1护照 2港澳居民往来内地通行证 3台湾居民往来大陆通行证
 * <AUTHOR>
 */
@ApiEnum
public enum IdCardType {

    @ApiEnumProperty("二代身份证")
    ID_CARD,

    @ApiEnumProperty("护照")
    PASSPORT,

    @ApiEnumProperty("港澳居民往来内地通行证")
    HK_MACAO_PERMIT,

    @ApiEnumProperty("台湾居民往来大陆通行证")
    TAIWAN_PERMIT;

    public IdCardType fromValue(int value) {
        return values()[value];
    }

    @JsonValue
    public int toValue() {
        return this.ordinal();
    }
}
