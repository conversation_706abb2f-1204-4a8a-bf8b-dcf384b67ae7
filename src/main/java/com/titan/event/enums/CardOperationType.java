package com.titan.event.enums;

/**
 * 会员卡操作类型枚举
 * 
 * <AUTHOR>
 */
public enum CardOperationType {
    
    /**
     * 消费
     */
    CONSUME(1, "消费"),
    
    /**
     * 充值
     */
    RECHARGE(2, "充值"),
    
    /**
     * 退款
     */
    REFUND(3, "退款");
    
    private final Integer code;
    private final String name;
    
    CardOperationType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "未知";
        }
        for (CardOperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return "未知";
    }
    
    /**
     * 根据code获取枚举
     */
    public static CardOperationType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CardOperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
