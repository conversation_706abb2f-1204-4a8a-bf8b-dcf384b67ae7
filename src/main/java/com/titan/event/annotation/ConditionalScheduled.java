package com.titan.event.annotation;

import com.titan.event.config.JobCondition;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 条件定时任务注解
 * 组合了@Scheduled和@Conditional(JobCondition.class)
 * 当titan.job.enabled=false时，不会执行定时任务
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Scheduled
@Conditional(JobCondition.class)
public @interface ConditionalScheduled {
    /**
     * 固定时间间隔执行，单位毫秒
     */
    long fixedRate() default -1;

    /**
     * 固定延迟执行，单位毫秒
     */
    long fixedDelay() default -1;

    /**
     * Cron表达式
     */
    String cron() default "";

    /**
     * Cron表达式的时区
     */
    String zone() default "";

    /**
     * 初始延迟时间，单位毫秒
     */
    long initialDelay() default -1;
} 