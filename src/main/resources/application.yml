server:
  port: ${PORT:8082}
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /venue-api
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

spring:
  application:
    name: venue
  shardingsphere:
    enabled: true
    datasource:
      names: master
      master:
        driver-class-name: com.mysql.cj.jdbc.Driver
        type: com.alibaba.druid.pool.DruidDataSource
        url: jdbc:mysql://${MYSQL_IP:rm-2zepr942039g285g9eo.mysql.rds.aliyuncs.com}:${MYSQL_PORT:3306}/venue?characterEncoding=UTF-8&&zeroDateTimeBehavior=convertToNull&autoReconnect=true&failOverReadOnly=false&connectTimeout=0&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${MYSQL_USERNAME:venue}
        password: ${MYSQL_PASSWORD:lC6GeZXJWNhYM3Sv}
        filters: stat
        maxActive: 50
        initialSize: 5
        maxWait: 30000
        minIdle: 5
        timeBetweenEvictionRunsMillis: 30000
        minEvictableIdleTimeMillis: 300000
        validationQuery: select 1
        testWhileIdle: true
        testOnBorrow: true
        testOnReturn: false
        poolPreparedStatements: true
        maxOpenPreparedStatements: 50
        removeAbandoned: true
        removeAbandonedTimeout: 180
        validationInterval: 30000
    props:
      sql:
        show: false
  main:
    allow-bean-definition-overriding: true
  redis:
    host: ${REDIS_IP:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:}
    database: 5
    timeout: 5000
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 5
        max-wait: 3000
  servlet:
    multipart:
      maxFileSize: 10MB
      maxRequestSize: 100MB

  jpa:
    database: mysql
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        hbm2ddl:
          auto: update
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
#    show-sql: true
    open-in-view: false
  # loacl prod
  profiles:
    active: ${ACTIVE:local}
  mapper-locations: classpath:com/titan/event/mapper/xml/*Mapper.xml
  type-aliases-package: com.titan.se.db.order.model

redisson:
  url: ${REDISSON_URL:localhost:6379}
  database: 5
  password: ${REDISSON_PWD:}

swagger:
  contact:
    name: lhx
    url: http://www.xxx.com
    email: <EMAIL>
  title: test
  description: 如有接口描述不清晰, 请联系相应开发人员添加
  version: 1.0.0
  enable: true

# jwt配置
jwt:
  secret-key: eda1782204cf41efaca1e051ccc610be62acdcf24c09f011f343583c41cfb941f
  url-patterns: /**
#  url-patterns: /d
  excluded-urls: /user/tourists/login,/user/wx/login,/user/wx/phone,/system/setting,/notify,/operate/*,/wxPay/refundNotify,/wxPay/apiRefundNotify,/wxPay/payNotify,/wxProduct/notify,/event/signApiRefund,/venueOrder/refundApiOrder,/venueOrder/verifyApiOrder

#阿里云oss存储和大鱼短信秘钥配置
oss:
  accessKey: ${OSS_ACC_KEY:LTAI5t7ALomWPK1FK3DA9qft}
  secretKey: ${OSS_SEC_KEY:******************************}
  endpoint: ${OSS_END_POINT:oss-cn-beijing.aliyuncs.com}
  bucketName: ${OSS_BUCKET_NAME:tmg-venue}
  staticDomain: ${OSS_STATIC_DOMAIN:https://tmg-venue.oss-cn-beijing.aliyuncs.com}
  resourceDomain: ${OSS_RESOURCE_DOMAIN:https://venue-resource.titan24.com}
  roleArn: ${ROLE_ARN:acs:ram::1366744272495066:role/stsuser}
  regionId: ${REGION_ID:cn-beijing}
  roleSessionName: ${ROLE_SESSION_NAME:turntext}

xxl:
  job:
    logretentiondays: 7
    admin:
      addresses: ${XXL_JOB_ADDRESS:http://***************:8990/xxl-job-admin}
    executor:
      appname: ${XXL_JOB_APP_NAME:birthday}
      ip: ${IP:}
      port: ${XXL_JOB_EXECUTOR_PORT:9991}
      logpath: ./log/jobhandler
      logretentiondays: 7
    accessToken: ${XXL_JOB_ACCESS_TOKEN:b5f7ed32b2e24789bdce1308afcafebe}

init: ${INIT_FLAG:false}

operate:
  pwd: ${OPERATE_PWD:gzH0skq4nr1m6LPx}

# 添加定时任务开关配置
titan:
  job:
    enabled: ${JOB_ENABLED:false}

wx:
  miniapp:
    configs:
      - appid: wx4be4a1eb1149410f
        secret: e8d947ace865b74bbd03b14b8c63e8f9
        msgDataFormat: JSON