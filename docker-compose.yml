version: '3'

services:
  event-api:
    image: event-api:latest
    container_name: event-api
    restart: always
    ports:
      - "8082:8082"
    environment:
      - ACTIVE=prod
      - PORT=8082
      - MYSQL_IP=mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=event
      - MYSQL_PASSWORD=password
      - REDIS_IP=redis
      - REDIS_PORT=6379
      - REDIS_PWD=
      - JOB_ENABLED=true  # 控制定时任务的开关，可设置为true或false
      - XXL_JOB_ADDRESS=http://xxl-job:8990/xxl-job-admin
      - XXL_JOB_APP_NAME=birthday
      - XXL_JOB_EXECUTOR_PORT=9991
      - XXL_JOB_ACCESS_TOKEN=b5f7ed32b2e24789bdce1308afcafebe
    volumes:
      - ./logs:/data/logs
    networks:
      - app-network

  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=event
      - MYSQL_USER=event
      - MYSQL_PASSWORD=password
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - app-network

  redis:
    image: redis:6
    container_name: redis
    restart: always
    volumes:
      - redis-data:/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mysql-data:
  redis-data: 